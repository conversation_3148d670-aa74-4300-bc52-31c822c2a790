{"pagination": {"ListAccounts": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Accounts"}, "ListAccountsForParent": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Accounts"}, "ListChildren": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Children"}, "ListCreateAccountStatus": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "CreateAccountStatuses"}, "ListHandshakesForAccount": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Handshakes"}, "ListHandshakesForOrganization": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Handshakes"}, "ListOrganizationalUnitsForParent": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "OrganizationalUnits"}, "ListParents": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Parents"}, "ListPolicies": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Policies"}, "ListPoliciesForTarget": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Policies"}, "ListRoots": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Roots"}, "ListTargetsForPolicy": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Targets"}, "ListAWSServiceAccessForOrganization": {"result_key": "EnabledServicePrincipals", "output_token": "NextToken", "input_token": "NextToken", "limit_key": "MaxResults"}, "ListTagsForResource": {"input_token": "NextToken", "output_token": "NextToken", "result_key": "Tags"}, "ListDelegatedAdministrators": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "DelegatedAdministrators"}, "ListDelegatedServicesForAccount": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "DelegatedServices"}}}