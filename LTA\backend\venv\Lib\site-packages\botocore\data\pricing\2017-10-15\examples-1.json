{"version": "1.0", "examples": {"DescribeServices": [{"input": {"FormatVersion": "aws_v1", "MaxResults": 1, "ServiceCode": "AmazonEC2"}, "output": {"FormatVersion": "aws_v1", "NextToken": "abcdefg123", "Services": [{"AttributeNames": ["volumeType", "maxIopsvolume", "instanceCapacity10xlarge", "locationType", "operation"], "ServiceCode": "AmazonEC2"}]}, "comments": {"input": {}, "output": {}}, "description": "Retrieves the service for the given Service Code.", "id": "to-retrieve-service-metadata", "title": "To retrieve a list of services and service codes"}], "GetAttributeValues": [{"input": {"AttributeName": "volumeType", "MaxResults": 2, "ServiceCode": "AmazonEC2"}, "output": {"AttributeValues": [{"Value": "Throughput Optimized HDD"}, {"Value": "Provisioned IOPS"}], "NextToken": "GpgauEXAMPLEezucl5LV0w==:7GzYJ0nw0DBTJ2J66EoTIIynE6O1uXwQtTRqioJzQadBnDVgHPzI1en4BUQnPCLpzeBk9RQQAWaFieA4+DapFAGLgk+Z/9/cTw9GldnPOHN98+FdmJP7wKU3QQpQ8MQr5KOeBkIsAqvAQYdL0DkL7tHwPtE5iCEByAmg9gcC/yBU1vAOsf7R3VaNN4M5jMDv3woSWqASSIlBVB6tgW78YL22KhssoItM/jWW+aP6Jqtq4mldxp/ct6DWAl+xLFwHU/CbketimPPXyqHF3/UXDw=="}, "comments": {"input": {}, "output": {}}, "description": "This operation returns a list of values available for the given attribute.", "id": "to-retreive-attribute-values", "title": "To retrieve a list of attribute values"}], "GetProducts": [{"input": {"Filters": [{"Field": "ServiceCode", "Type": "TERM_MATCH", "Value": "AmazonEC2"}, {"Field": "volumeType", "Type": "TERM_MATCH", "Value": "Provisioned IOPS"}], "FormatVersion": "aws_v1", "MaxResults": 1}, "output": {"FormatVersion": "aws_v1", "NextToken": "57r3EXAMPLEujbzWfHF7Ciw==:ywSmZsD3mtpQmQLQ5XfOsIMkYybSj+vAT+kGmwMFq+K9DGmIoJkz7lunVeamiOPgthdWSO2a7YKojCO+zY4dJmuNl2QvbNhXs+AJ2Ufn7xGmJncNI2TsEuAsVCUfTAvAQNcwwamtk6XuZ4YdNnooV62FjkV3ZAn40d9+wAxV7+FImvhUHi/+f8afgZdGh2zPUlH8jlV9uUtj0oHp8+DhPUuHXh+WBII1E/aoKpPSm3c=", "PriceList": ["{\"product\":{\"productFamily\":\"Storage\",\"attributes\":{\"storageMedia\":\"SSD-backed\",\"maxThroughputvolume\":\"320 MB/sec\",\"volumeType\":\"Provisioned IOPS\",\"maxIopsvolume\":\"20000\",\"servicecode\":\"AmazonEC2\",\"usagetype\":\"CAN1-EBS:VolumeUsage.piops\",\"locationType\":\"AWS Region\",\"location\":\"Canada (Central)\",\"servicename\":\"Amazon Elastic Compute Cloud\",\"maxVolumeSize\":\"16 TiB\",\"operation\":\"\"},\"sku\":\"WQGC34PB2AWS8R4U\"},\"serviceCode\":\"AmazonEC2\",\"terms\":{\"OnDemand\":{\"WQGC34PB2AWS8R4U.JRTCKXETXF\":{\"priceDimensions\":{\"WQGC34PB2AWS8R4U.JRTCKXETXF.6YS6EN2CT7\":{\"unit\":\"GB-Mo\",\"endRange\":\"Inf\",\"description\":\"$0.138 per GB-month of Provisioned IOPS SSD (io1)  provisioned storage - Canada (Central)\",\"appliesTo\":[],\"rateCode\":\"WQGC34PB2AWS8R4U.JRTCKXETXF.6YS6EN2CT7\",\"beginRange\":\"0\",\"pricePerUnit\":{\"USD\":\"0.1380000000\"}}},\"sku\":\"WQGC34PB2AWS8R4U\",\"effectiveDate\":\"2017-08-01T00:00:00Z\",\"offerTermCode\":\"JRTCKXETXF\",\"termAttributes\":{}}}},\"version\":\"20170901182201\",\"publicationDate\":\"2017-09-01T18:22:01Z\"}"]}, "comments": {"input": {}, "output": {}}, "description": "This operation returns a list of products that match the given criteria.", "id": "to-retrieve-available products", "title": "To retrieve a list of products"}]}}