{"pagination": {"ListAnalyses": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "AnalysisSummaryList"}, "ListDashboardVersions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "DashboardVersionSummaryList"}, "ListDashboards": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "DashboardSummaryList"}, "ListDataSets": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "DataSetSummaries"}, "ListDataSources": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "DataSources"}, "ListIngestions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Ingestions"}, "ListNamespaces": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Namespaces"}, "ListTemplateAliases": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "TemplateAliasList"}, "ListTemplateVersions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "TemplateVersionSummaryList"}, "ListTemplates": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "TemplateSummaryList"}, "ListThemeVersions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ThemeVersionSummaryList"}, "ListThemes": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ThemeSummaryList"}, "SearchAnalyses": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "AnalysisSummaryList"}, "SearchDashboards": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "DashboardSummaryList"}, "SearchDataSets": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "DataSetSummaries"}, "SearchDataSources": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "DataSourceSummaries"}, "ListAssetBundleExportJobs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "AssetBundleExportJobSummaryList"}, "ListAssetBundleImportJobs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "AssetBundleImportJobSummaryList"}, "ListGroupMemberships": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "GroupMemberList"}, "ListGroups": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "GroupList"}, "ListIAMPolicyAssignments": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "IAMPolicyAssignments"}, "ListIAMPolicyAssignmentsForUser": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ActiveAssignments"}, "ListUserGroups": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "GroupList"}, "ListUsers": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "UserList"}, "SearchGroups": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "GroupList"}, "DescribeFolderPermissions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Permissions"}, "DescribeFolderResolvedPermissions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Permissions"}, "ListFolderMembers": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "FolderMemberList"}, "ListFolders": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "FolderSummaryList"}, "SearchFolders": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "FolderSummaryList"}, "ListRoleMemberships": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "MembersList"}, "ListFoldersForResource": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Folders"}, "ListBrands": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Brands"}, "ListCustomPermissions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "CustomPermissionsList"}, "SearchTopics": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "TopicSummaryList"}}}