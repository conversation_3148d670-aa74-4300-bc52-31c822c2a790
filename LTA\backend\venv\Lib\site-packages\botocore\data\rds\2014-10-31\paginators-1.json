{"pagination": {"DescribeCertificates": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "Certificates"}, "DescribeDBClusterBacktracks": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "DBClusterBacktracks"}, "DescribeDBClusterParameterGroups": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "DBClusterParameterGroups"}, "DescribeDBClusterParameters": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "Parameters"}, "DescribeDBClusterSnapshots": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "DBClusterSnapshots"}, "DescribeDBClusters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBClusters"}, "DescribeDBEngineVersions": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "DBEngineVersions"}, "DescribeDBInstanceAutomatedBackups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBInstanceAutomatedBackups"}, "DescribeDBInstances": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "DBInstances"}, "DescribeDBLogFiles": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "DescribeDBLogFiles"}, "DescribeDBParameterGroups": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "DBParameterGroups"}, "DescribeDBParameters": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "Parameters"}, "DescribeDBSecurityGroups": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "DBSecurityGroups"}, "DescribeDBSnapshots": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "DBSnapshots"}, "DescribeDBSubnetGroups": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "DBSubnetGroups"}, "DescribeEngineDefaultClusterParameters": {"input_token": "<PERSON><PERSON>", "output_token": "EngineDefaults.Marker", "limit_key": "MaxRecords", "result_key": "EngineDefaults.Parameters"}, "DescribeEngineDefaultParameters": {"input_token": "<PERSON><PERSON>", "output_token": "EngineDefaults.Marker", "limit_key": "MaxRecords", "result_key": "EngineDefaults.Parameters"}, "DescribeEventSubscriptions": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "EventSubscriptionsList"}, "DescribeEvents": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "Events"}, "DescribeGlobalClusters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "GlobalClusters"}, "DescribeOptionGroupOptions": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "OptionGroupOptions"}, "DescribeOptionGroups": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "OptionGroupsList"}, "DescribeOrderableDBInstanceOptions": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "OrderableDBInstanceOptions"}, "DescribePendingMaintenanceActions": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "PendingMaintenanceActions"}, "DescribeReservedDBInstances": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "ReservedDBInstances"}, "DescribeReservedDBInstancesOfferings": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "ReservedDBInstancesOfferings"}, "DescribeSourceRegions": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "result_key": "SourceRegions"}, "DownloadDBLogFilePortion": {"input_token": "<PERSON><PERSON>", "output_token": "<PERSON><PERSON>", "limit_key": "NumberOfLines", "more_results": "AdditionalDataPending", "result_key": "LogFileData"}, "DescribeDBClusterEndpoints": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBClusterEndpoints"}, "DescribeDBProxies": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBProxies"}, "DescribeDBProxyTargetGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "TargetGroups"}, "DescribeDBProxyTargets": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Targets"}, "DescribeExportTasks": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "ExportTasks"}, "DescribeDBProxyEndpoints": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBProxyEndpoints"}, "DescribeBlueGreenDeployments": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "BlueGreenDeployments"}, "DescribeDBClusterAutomatedBackups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBClusterAutomatedBackups"}, "DescribeIntegrations": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Integrations"}, "DescribeDBSnapshotTenantDatabases": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBSnapshotTenantDatabases"}, "DescribeTenantDatabases": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "TenantDatabases"}, "DescribeDBRecommendations": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBRecommendations"}, "DescribeDBMajorEngineVersions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBMajorEngineVersions"}}}