"""
font data tables for truetype and afm computer modern fonts
"""

from __future__ import annotations
from typing import overload, Union

latex_to_bakoma = {
    '\\__sqrt__'                 : ('cmex10', 0x70),
    '\\bigcap'                   : ('cmex10', 0x5c),
    '\\bigcup'                   : ('cmex10', 0x5b),
    '\\bigodot'                  : ('cmex10', 0x4b),
    '\\bigoplus'                 : ('cmex10', 0x4d),
    '\\bigotimes'                : ('cmex10', 0x4f),
    '\\biguplus'                 : ('cmex10', 0x5d),
    '\\bigvee'                   : ('cmex10', 0x5f),
    '\\bigwedge'                 : ('cmex10', 0x5e),
    '\\coprod'                   : ('cmex10', 0x61),
    '\\int'                      : ('cmex10', 0x5a),
    '\\langle'                   : ('cmex10', 0xad),
    '\\leftangle'                : ('cmex10', 0xad),
    '\\leftbrace'                : ('cmex10', 0xa9),
    '\\oint'                     : ('cmex10', 0x49),
    '\\prod'                     : ('cmex10', 0x59),
    '\\rangle'                   : ('cmex10', 0xae),
    '\\rightangle'               : ('cmex10', 0xae),
    '\\rightbrace'               : ('cmex10', 0xaa),
    '\\sum'                      : ('cmex10', 0x58),
    '\\widehat'                  : ('cmex10', 0x62),
    '\\widetilde'                : ('cmex10', 0x65),
    '\\{'                        : ('cmex10', 0xa9),
    '\\}'                        : ('cmex10', 0xaa),
    '{'                          : ('cmex10', 0xa9),
    '}'                          : ('cmex10', 0xaa),

    ','                          : ('cmmi10', 0x3b),
    '.'                          : ('cmmi10', 0x3a),
    '/'                          : ('cmmi10', 0x3d),
    '<'                          : ('cmmi10', 0x3c),
    '>'                          : ('cmmi10', 0x3e),
    '\\alpha'                    : ('cmmi10', 0xae),
    '\\beta'                     : ('cmmi10', 0xaf),
    '\\chi'                      : ('cmmi10', 0xc2),
    '\\combiningrightarrowabove' : ('cmmi10', 0x7e),
    '\\delta'                    : ('cmmi10', 0xb1),
    '\\ell'                      : ('cmmi10', 0x60),
    '\\epsilon'                  : ('cmmi10', 0xb2),
    '\\eta'                      : ('cmmi10', 0xb4),
    '\\flat'                     : ('cmmi10', 0x5b),
    '\\frown'                    : ('cmmi10', 0x5f),
    '\\gamma'                    : ('cmmi10', 0xb0),
    '\\imath'                    : ('cmmi10', 0x7b),
    '\\iota'                     : ('cmmi10', 0xb6),
    '\\jmath'                    : ('cmmi10', 0x7c),
    '\\kappa'                    : ('cmmi10', 0x2219),
    '\\lambda'                   : ('cmmi10', 0xb8),
    '\\leftharpoondown'          : ('cmmi10', 0x29),
    '\\leftharpoonup'            : ('cmmi10', 0x28),
    '\\mu'                       : ('cmmi10', 0xb9),
    '\\natural'                  : ('cmmi10', 0x5c),
    '\\nu'                       : ('cmmi10', 0xba),
    '\\omega'                    : ('cmmi10', 0x21),
    '\\phi'                      : ('cmmi10', 0xc1),
    '\\pi'                       : ('cmmi10', 0xbc),
    '\\psi'                      : ('cmmi10', 0xc3),
    '\\rho'                      : ('cmmi10', 0xbd),
    '\\rightharpoondown'         : ('cmmi10', 0x2b),
    '\\rightharpoonup'           : ('cmmi10', 0x2a),
    '\\sharp'                    : ('cmmi10', 0x5d),
    '\\sigma'                    : ('cmmi10', 0xbe),
    '\\smile'                    : ('cmmi10', 0x5e),
    '\\tau'                      : ('cmmi10', 0xbf),
    '\\theta'                    : ('cmmi10', 0xb5),
    '\\triangleleft'             : ('cmmi10', 0x2f),
    '\\triangleright'            : ('cmmi10', 0x2e),
    '\\upsilon'                  : ('cmmi10', 0xc0),
    '\\varepsilon'               : ('cmmi10', 0x22),
    '\\varphi'                   : ('cmmi10', 0x27),
    '\\varrho'                   : ('cmmi10', 0x25),
    '\\varsigma'                 : ('cmmi10', 0x26),
    '\\vartheta'                 : ('cmmi10', 0x23),
    '\\wp'                       : ('cmmi10', 0x7d),
    '\\xi'                       : ('cmmi10', 0xbb),
    '\\zeta'                     : ('cmmi10', 0xb3),

    '!'                          : ('cmr10', 0x21),
    '%'                          : ('cmr10', 0x25),
    '&'                          : ('cmr10', 0x26),
    '('                          : ('cmr10', 0x28),
    ')'                          : ('cmr10', 0x29),
    '+'                          : ('cmr10', 0x2b),
    '0'                          : ('cmr10', 0x30),
    '1'                          : ('cmr10', 0x31),
    '2'                          : ('cmr10', 0x32),
    '3'                          : ('cmr10', 0x33),
    '4'                          : ('cmr10', 0x34),
    '5'                          : ('cmr10', 0x35),
    '6'                          : ('cmr10', 0x36),
    '7'                          : ('cmr10', 0x37),
    '8'                          : ('cmr10', 0x38),
    '9'                          : ('cmr10', 0x39),
    ':'                          : ('cmr10', 0x3a),
    ';'                          : ('cmr10', 0x3b),
    '='                          : ('cmr10', 0x3d),
    '?'                          : ('cmr10', 0x3f),
    '@'                          : ('cmr10', 0x40),
    '['                          : ('cmr10', 0x5b),
    '\\#'                        : ('cmr10', 0x23),
    '\\$'                        : ('cmr10', 0x24),
    '\\%'                        : ('cmr10', 0x25),
    '\\Delta'                    : ('cmr10', 0xa2),
    '\\Gamma'                    : ('cmr10', 0xa1),
    '\\Lambda'                   : ('cmr10', 0xa4),
    '\\Omega'                    : ('cmr10', 0xad),
    '\\Phi'                      : ('cmr10', 0xa9),
    '\\Pi'                       : ('cmr10', 0xa6),
    '\\Psi'                      : ('cmr10', 0xaa),
    '\\Sigma'                    : ('cmr10', 0xa7),
    '\\Theta'                    : ('cmr10', 0xa3),
    '\\Upsilon'                  : ('cmr10', 0xa8),
    '\\Xi'                       : ('cmr10', 0xa5),
    '\\circumflexaccent'         : ('cmr10', 0x5e),
    '\\combiningacuteaccent'     : ('cmr10', 0xb6),
    '\\combiningbreve'           : ('cmr10', 0xb8),
    '\\combiningdiaeresis'       : ('cmr10', 0xc4),
    '\\combiningdotabove'        : ('cmr10', 0x5f),
    '\\combininggraveaccent'     : ('cmr10', 0xb5),
    '\\combiningoverline'        : ('cmr10', 0xb9),
    '\\combiningtilde'           : ('cmr10', 0x7e),
    '\\leftbracket'              : ('cmr10', 0x5b),
    '\\leftparen'                : ('cmr10', 0x28),
    '\\rightbracket'             : ('cmr10', 0x5d),
    '\\rightparen'               : ('cmr10', 0x29),
    '\\widebar'                  : ('cmr10', 0xb9),
    ']'                          : ('cmr10', 0x5d),

    '*'                          : ('cmsy10', 0xa4),
    '\N{MINUS SIGN}'             : ('cmsy10', 0xa1),
    '\\Downarrow'                : ('cmsy10', 0x2b),
    '\\Im'                       : ('cmsy10', 0x3d),
    '\\Leftarrow'                : ('cmsy10', 0x28),
    '\\Leftrightarrow'           : ('cmsy10', 0x2c),
    '\\P'                        : ('cmsy10', 0x7b),
    '\\Re'                       : ('cmsy10', 0x3c),
    '\\Rightarrow'               : ('cmsy10', 0x29),
    '\\S'                        : ('cmsy10', 0x78),
    '\\Uparrow'                  : ('cmsy10', 0x2a),
    '\\Updownarrow'              : ('cmsy10', 0x6d),
    '\\Vert'                     : ('cmsy10', 0x6b),
    '\\aleph'                    : ('cmsy10', 0x40),
    '\\approx'                   : ('cmsy10', 0xbc),
    '\\ast'                      : ('cmsy10', 0xa4),
    '\\asymp'                    : ('cmsy10', 0xb3),
    '\\backslash'                : ('cmsy10', 0x6e),
    '\\bigcirc'                  : ('cmsy10', 0xb0),
    '\\bigtriangledown'          : ('cmsy10', 0x35),
    '\\bigtriangleup'            : ('cmsy10', 0x34),
    '\\bot'                      : ('cmsy10', 0x3f),
    '\\bullet'                   : ('cmsy10', 0xb2),
    '\\cap'                      : ('cmsy10', 0x5c),
    '\\cdot'                     : ('cmsy10', 0xa2),
    '\\circ'                     : ('cmsy10', 0xb1),
    '\\clubsuit'                 : ('cmsy10', 0x7c),
    '\\cup'                      : ('cmsy10', 0x5b),
    '\\dag'                      : ('cmsy10', 0x79),
    '\\dashv'                    : ('cmsy10', 0x61),
    '\\ddag'                     : ('cmsy10', 0x7a),
    '\\diamond'                  : ('cmsy10', 0xa6),
    '\\diamondsuit'              : ('cmsy10', 0x7d),
    '\\div'                      : ('cmsy10', 0xa5),
    '\\downarrow'                : ('cmsy10', 0x23),
    '\\emptyset'                 : ('cmsy10', 0x3b),
    '\\equiv'                    : ('cmsy10', 0xb4),
    '\\exists'                   : ('cmsy10', 0x39),
    '\\forall'                   : ('cmsy10', 0x38),
    '\\geq'                      : ('cmsy10', 0xb8),
    '\\gg'                       : ('cmsy10', 0xc0),
    '\\heartsuit'                : ('cmsy10', 0x7e),
    '\\in'                       : ('cmsy10', 0x32),
    '\\infty'                    : ('cmsy10', 0x31),
    '\\lbrace'                   : ('cmsy10', 0x66),
    '\\lceil'                    : ('cmsy10', 0x64),
    '\\leftarrow'                : ('cmsy10', 0xc3),
    '\\leftrightarrow'           : ('cmsy10', 0x24),
    '\\leq'                      : ('cmsy10', 0x2219),
    '\\lfloor'                   : ('cmsy10', 0x62),
    '\\ll'                       : ('cmsy10', 0xbf),
    '\\mid'                      : ('cmsy10', 0x6a),
    '\\mp'                       : ('cmsy10', 0xa8),
    '\\nabla'                    : ('cmsy10', 0x72),
    '\\nearrow'                  : ('cmsy10', 0x25),
    '\\neg'                      : ('cmsy10', 0x3a),
    '\\ni'                       : ('cmsy10', 0x33),
    '\\nwarrow'                  : ('cmsy10', 0x2d),
    '\\odot'                     : ('cmsy10', 0xaf),
    '\\ominus'                   : ('cmsy10', 0xaa),
    '\\oplus'                    : ('cmsy10', 0xa9),
    '\\oslash'                   : ('cmsy10', 0xae),
    '\\otimes'                   : ('cmsy10', 0xad),
    '\\pm'                       : ('cmsy10', 0xa7),
    '\\prec'                     : ('cmsy10', 0xc1),
    '\\preceq'                   : ('cmsy10', 0xb9),
    '\\prime'                    : ('cmsy10', 0x30),
    '\\propto'                   : ('cmsy10', 0x2f),
    '\\rbrace'                   : ('cmsy10', 0x67),
    '\\rceil'                    : ('cmsy10', 0x65),
    '\\rfloor'                   : ('cmsy10', 0x63),
    '\\rightarrow'               : ('cmsy10', 0x21),
    '\\searrow'                  : ('cmsy10', 0x26),
    '\\sim'                      : ('cmsy10', 0xbb),
    '\\simeq'                    : ('cmsy10', 0x27),
    '\\slash'                    : ('cmsy10', 0x36),
    '\\spadesuit'                : ('cmsy10', 0xc4),
    '\\sqcap'                    : ('cmsy10', 0x75),
    '\\sqcup'                    : ('cmsy10', 0x74),
    '\\sqsubseteq'               : ('cmsy10', 0x76),
    '\\sqsupseteq'               : ('cmsy10', 0x77),
    '\\subset'                   : ('cmsy10', 0xbd),
    '\\subseteq'                 : ('cmsy10', 0xb5),
    '\\succ'                     : ('cmsy10', 0xc2),
    '\\succeq'                   : ('cmsy10', 0xba),
    '\\supset'                   : ('cmsy10', 0xbe),
    '\\supseteq'                 : ('cmsy10', 0xb6),
    '\\swarrow'                  : ('cmsy10', 0x2e),
    '\\times'                    : ('cmsy10', 0xa3),
    '\\to'                       : ('cmsy10', 0x21),
    '\\top'                      : ('cmsy10', 0x3e),
    '\\uparrow'                  : ('cmsy10', 0x22),
    '\\updownarrow'              : ('cmsy10', 0x6c),
    '\\uplus'                    : ('cmsy10', 0x5d),
    '\\vdash'                    : ('cmsy10', 0x60),
    '\\vee'                      : ('cmsy10', 0x5f),
    '\\vert'                     : ('cmsy10', 0x6a),
    '\\wedge'                    : ('cmsy10', 0x5e),
    '\\wr'                       : ('cmsy10', 0x6f),
    '\\|'                        : ('cmsy10', 0x6b),
    '|'                          : ('cmsy10', 0x6a),

    '\\_'                        : ('cmtt10', 0x5f)
}

# Automatically generated.

type12uni = {
    'aring'          : 229,
    'quotedblright'  : 8221,
    'V'              : 86,
    'dollar'         : 36,
    'four'           : 52,
    'Yacute'         : 221,
    'P'              : 80,
    'underscore'     : 95,
    'p'              : 112,
    'Otilde'         : 213,
    'perthousand'    : 8240,
    'zero'           : 48,
    'dotlessi'       : 305,
    'Scaron'         : 352,
    'zcaron'         : 382,
    'egrave'         : 232,
    'section'        : 167,
    'Icircumflex'    : 206,
    'ntilde'         : 241,
    'ampersand'      : 38,
    'dotaccent'      : 729,
    'degree'         : 176,
    'K'              : 75,
    'acircumflex'    : 226,
    'Aring'          : 197,
    'k'              : 107,
    'smalltilde'     : 732,
    'Agrave'         : 192,
    'divide'         : 247,
    'ocircumflex'    : 244,
    'asciitilde'     : 126,
    'two'            : 50,
    'E'              : 69,
    'scaron'         : 353,
    'F'              : 70,
    'bracketleft'    : 91,
    'asciicircum'    : 94,
    'f'              : 102,
    'ordmasculine'   : 186,
    'mu'             : 181,
    'paragraph'      : 182,
    'nine'           : 57,
    'v'              : 118,
    'guilsinglleft'  : 8249,
    'backslash'      : 92,
    'six'            : 54,
    'A'              : 65,
    'icircumflex'    : 238,
    'a'              : 97,
    'ogonek'         : 731,
    'q'              : 113,
    'oacute'         : 243,
    'ograve'         : 242,
    'edieresis'      : 235,
    'comma'          : 44,
    'otilde'         : 245,
    'guillemotright' : 187,
    'ecircumflex'    : 234,
    'greater'        : 62,
    'uacute'         : 250,
    'L'              : 76,
    'bullet'         : 8226,
    'cedilla'        : 184,
    'ydieresis'      : 255,
    'l'              : 108,
    'logicalnot'     : 172,
    'exclamdown'     : 161,
    'endash'         : 8211,
    'agrave'         : 224,
    'Adieresis'      : 196,
    'germandbls'     : 223,
    'Odieresis'      : 214,
    'space'          : 32,
    'quoteright'     : 8217,
    'ucircumflex'    : 251,
    'G'              : 71,
    'quoteleft'      : 8216,
    'W'              : 87,
    'Q'              : 81,
    'g'              : 103,
    'w'              : 119,
    'question'       : 63,
    'one'            : 49,
    'ring'           : 730,
    'figuredash'     : 8210,
    'B'              : 66,
    'iacute'         : 237,
    'Ydieresis'      : 376,
    'R'              : 82,
    'b'              : 98,
    'r'              : 114,
    'Ccedilla'       : 199,
    'minus'          : 8722,
    'Lslash'         : 321,
    'Uacute'         : 218,
    'yacute'         : 253,
    'Ucircumflex'    : 219,
    'quotedbl'       : 34,
    'onehalf'        : 189,
    'Thorn'          : 222,
    'M'              : 77,
    'eight'          : 56,
    'multiply'       : 215,
    'grave'          : 96,
    'Ocircumflex'    : 212,
    'm'              : 109,
    'Ugrave'         : 217,
    'guilsinglright' : 8250,
    'Ntilde'         : 209,
    'questiondown'   : 191,
    'Atilde'         : 195,
    'ccedilla'       : 231,
    'Z'              : 90,
    'copyright'      : 169,
    'yen'            : 165,
    'Eacute'         : 201,
    'H'              : 72,
    'X'              : 88,
    'Idieresis'      : 207,
    'bar'            : 124,
    'h'              : 104,
    'x'              : 120,
    'udieresis'      : 252,
    'ordfeminine'    : 170,
    'braceleft'      : 123,
    'macron'         : 175,
    'atilde'         : 227,
    'Acircumflex'    : 194,
    'Oslash'         : 216,
    'C'              : 67,
    'quotedblleft'   : 8220,
    'S'              : 83,
    'exclam'         : 33,
    'Zcaron'         : 381,
    'equal'          : 61,
    's'              : 115,
    'eth'            : 240,
    'Egrave'         : 200,
    'hyphen'         : 45,
    'period'         : 46,
    'igrave'         : 236,
    'colon'          : 58,
    'Ecircumflex'    : 202,
    'trademark'      : 8482,
    'Aacute'         : 193,
    'cent'           : 162,
    'lslash'         : 322,
    'c'              : 99,
    'N'              : 78,
    'breve'          : 728,
    'Oacute'         : 211,
    'guillemotleft'  : 171,
    'n'              : 110,
    'idieresis'      : 239,
    'braceright'     : 125,
    'seven'          : 55,
    'brokenbar'      : 166,
    'ugrave'         : 249,
    'periodcentered' : 183,
    'sterling'       : 163,
    'I'              : 73,
    'Y'              : 89,
    'Eth'            : 208,
    'emdash'         : 8212,
    'i'              : 105,
    'daggerdbl'      : 8225,
    'y'              : 121,
    'plusminus'      : 177,
    'less'           : 60,
    'Udieresis'      : 220,
    'D'              : 68,
    'five'           : 53,
    'T'              : 84,
    'oslash'         : 248,
    'acute'          : 180,
    'd'              : 100,
    'OE'             : 338,
    'Igrave'         : 204,
    't'              : 116,
    'parenright'     : 41,
    'adieresis'      : 228,
    'quotesingle'    : 39,
    'twodotenleader' : 8229,
    'slash'          : 47,
    'ellipsis'       : 8230,
    'numbersign'     : 35,
    'odieresis'      : 246,
    'O'              : 79,
    'oe'             : 339,
    'o'              : 111,
    'Edieresis'      : 203,
    'plus'           : 43,
    'dagger'         : 8224,
    'three'          : 51,
    'hungarumlaut'   : 733,
    'parenleft'      : 40,
    'fraction'       : 8260,
    'registered'     : 174,
    'J'              : 74,
    'dieresis'       : 168,
    'Ograve'         : 210,
    'j'              : 106,
    'z'              : 122,
    'ae'             : 230,
    'semicolon'      : 59,
    'at'             : 64,
    'Iacute'         : 205,
    'percent'        : 37,
    'bracketright'   : 93,
    'AE'             : 198,
    'asterisk'       : 42,
    'aacute'         : 225,
    'U'              : 85,
    'eacute'         : 233,
    'e'              : 101,
    'thorn'          : 254,
    'u'              : 117,
}

uni2type1 = {v: k for k, v in type12uni.items()}

#  The script below is to sort and format the tex2uni dict

## For decimal values: int(hex(v), 16)
#  newtex = {k: hex(v) for k, v in tex2uni.items()}
#  sd = dict(sorted(newtex.items(), key=lambda item: item[0]))
#
## For formatting the sorted dictionary with proper spacing
## the value '24' comes from finding the longest string in
## the newtex keys with len(max(newtex, key=len))
#  for key in sd:
#      print("{0:24} : {1: <s},".format("'" + key + "'", sd[key]))

tex2uni = {
    '#'                      : 0x23,
    '$'                      : 0x24,
    '%'                      : 0x25,
    'AA'                     : 0xc5,
    'AE'                     : 0xc6,
    'BbbC'                   : 0x2102,
    'BbbN'                   : 0x2115,
    'BbbP'                   : 0x2119,
    'BbbQ'                   : 0x211a,
    'BbbR'                   : 0x211d,
    'BbbZ'                   : 0x2124,
    'Bumpeq'                 : 0x224e,
    'Cap'                    : 0x22d2,
    'Colon'                  : 0x2237,
    'Cup'                    : 0x22d3,
    'DH'                     : 0xd0,
    'Delta'                  : 0x394,
    'Doteq'                  : 0x2251,
    'Downarrow'              : 0x21d3,
    'Equiv'                  : 0x2263,
    'Finv'                   : 0x2132,
    'Game'                   : 0x2141,
    'Gamma'                  : 0x393,
    'H'                      : 0x30b,
    'Im'                     : 0x2111,
    'Join'                   : 0x2a1d,
    'L'                      : 0x141,
    'Lambda'                 : 0x39b,
    'Ldsh'                   : 0x21b2,
    'Leftarrow'              : 0x21d0,
    'Leftrightarrow'         : 0x21d4,
    'Lleftarrow'             : 0x21da,
    'Longleftarrow'          : 0x27f8,
    'Longleftrightarrow'     : 0x27fa,
    'Longrightarrow'         : 0x27f9,
    'Lsh'                    : 0x21b0,
    'Nearrow'                : 0x21d7,
    'Nwarrow'                : 0x21d6,
    'O'                      : 0xd8,
    'OE'                     : 0x152,
    'Omega'                  : 0x3a9,
    'P'                      : 0xb6,
    'Phi'                    : 0x3a6,
    'Pi'                     : 0x3a0,
    'Psi'                    : 0x3a8,
    'QED'                    : 0x220e,
    'Rdsh'                   : 0x21b3,
    'Re'                     : 0x211c,
    'Rightarrow'             : 0x21d2,
    'Rrightarrow'            : 0x21db,
    'Rsh'                    : 0x21b1,
    'S'                      : 0xa7,
    'Searrow'                : 0x21d8,
    'Sigma'                  : 0x3a3,
    'Subset'                 : 0x22d0,
    'Supset'                 : 0x22d1,
    'Swarrow'                : 0x21d9,
    'Theta'                  : 0x398,
    'Thorn'                  : 0xde,
    'Uparrow'                : 0x21d1,
    'Updownarrow'            : 0x21d5,
    'Upsilon'                : 0x3a5,
    'Vdash'                  : 0x22a9,
    'Vert'                   : 0x2016,
    'Vvdash'                 : 0x22aa,
    'Xi'                     : 0x39e,
    '_'                      : 0x5f,
    '__sqrt__'               : 0x221a,
    'aa'                     : 0xe5,
    'ac'                     : 0x223e,
    'acute'                  : 0x301,
    'acwopencirclearrow'     : 0x21ba,
    'adots'                  : 0x22f0,
    'ae'                     : 0xe6,
    'aleph'                  : 0x2135,
    'alpha'                  : 0x3b1,
    'amalg'                  : 0x2a3f,
    'angle'                  : 0x2220,
    'approx'                 : 0x2248,
    'approxeq'               : 0x224a,
    'approxident'            : 0x224b,
    'arceq'                  : 0x2258,
    'ast'                    : 0x2217,
    'asterisk'               : 0x2a,
    'asymp'                  : 0x224d,
    'backcong'               : 0x224c,
    'backepsilon'            : 0x3f6,
    'backprime'              : 0x2035,
    'backsim'                : 0x223d,
    'backsimeq'              : 0x22cd,
    'backslash'              : 0x5c,
    'bagmember'              : 0x22ff,
    'bar'                    : 0x304,
    'barleftarrow'           : 0x21e4,
    'barvee'                 : 0x22bd,
    'barwedge'               : 0x22bc,
    'because'                : 0x2235,
    'beta'                   : 0x3b2,
    'beth'                   : 0x2136,
    'between'                : 0x226c,
    'bigcap'                 : 0x22c2,
    'bigcirc'                : 0x25cb,
    'bigcup'                 : 0x22c3,
    'bigodot'                : 0x2a00,
    'bigoplus'               : 0x2a01,
    'bigotimes'              : 0x2a02,
    'bigsqcup'               : 0x2a06,
    'bigstar'                : 0x2605,
    'bigtriangledown'        : 0x25bd,
    'bigtriangleup'          : 0x25b3,
    'biguplus'               : 0x2a04,
    'bigvee'                 : 0x22c1,
    'bigwedge'               : 0x22c0,
    'blacksquare'            : 0x25a0,
    'blacktriangle'          : 0x25b4,
    'blacktriangledown'      : 0x25be,
    'blacktriangleleft'      : 0x25c0,
    'blacktriangleright'     : 0x25b6,
    'bot'                    : 0x22a5,
    'bowtie'                 : 0x22c8,
    'boxbar'                 : 0x25eb,
    'boxdot'                 : 0x22a1,
    'boxminus'               : 0x229f,
    'boxplus'                : 0x229e,
    'boxtimes'               : 0x22a0,
    'breve'                  : 0x306,
    'bullet'                 : 0x2219,
    'bumpeq'                 : 0x224f,
    'c'                      : 0x327,
    'candra'                 : 0x310,
    'cap'                    : 0x2229,
    'carriagereturn'         : 0x21b5,
    'cdot'                   : 0x22c5,
    'cdotp'                  : 0xb7,
    'cdots'                  : 0x22ef,
    'cent'                   : 0xa2,
    'check'                  : 0x30c,
    'checkmark'              : 0x2713,
    'chi'                    : 0x3c7,
    'circ'                   : 0x2218,
    'circeq'                 : 0x2257,
    'circlearrowleft'        : 0x21ba,
    'circlearrowright'       : 0x21bb,
    'circledR'               : 0xae,
    'circledS'               : 0x24c8,
    'circledast'             : 0x229b,
    'circledcirc'            : 0x229a,
    'circleddash'            : 0x229d,
    'circumflexaccent'       : 0x302,
    'clubsuit'               : 0x2663,
    'clubsuitopen'           : 0x2667,
    'colon'                  : 0x3a,
    'coloneq'                : 0x2254,
    'combiningacuteaccent'   : 0x301,
    'combiningbreve'         : 0x306,
    'combiningdiaeresis'     : 0x308,
    'combiningdotabove'      : 0x307,
    'combiningfourdotsabove' : 0x20dc,
    'combininggraveaccent'   : 0x300,
    'combiningoverline'      : 0x304,
    'combiningrightarrowabove' : 0x20d7,
    'combiningthreedotsabove' : 0x20db,
    'combiningtilde'         : 0x303,
    'complement'             : 0x2201,
    'cong'                   : 0x2245,
    'coprod'                 : 0x2210,
    'copyright'              : 0xa9,
    'cup'                    : 0x222a,
    'cupdot'                 : 0x228d,
    'cupleftarrow'           : 0x228c,
    'curlyeqprec'            : 0x22de,
    'curlyeqsucc'            : 0x22df,
    'curlyvee'               : 0x22ce,
    'curlywedge'             : 0x22cf,
    'curvearrowleft'         : 0x21b6,
    'curvearrowright'        : 0x21b7,
    'cwopencirclearrow'      : 0x21bb,
    'd'                      : 0x323,
    'dag'                    : 0x2020,
    'dagger'                 : 0x2020,
    'daleth'                 : 0x2138,
    'danger'                 : 0x2621,
    'dashleftarrow'          : 0x290e,
    'dashrightarrow'         : 0x290f,
    'dashv'                  : 0x22a3,
    'ddag'                   : 0x2021,
    'ddagger'                : 0x2021,
    'ddddot'                 : 0x20dc,
    'dddot'                  : 0x20db,
    'ddot'                   : 0x308,
    'ddots'                  : 0x22f1,
    'degree'                 : 0xb0,
    'delta'                  : 0x3b4,
    'dh'                     : 0xf0,
    'diamond'                : 0x22c4,
    'diamondsuit'            : 0x2662,
    'digamma'                : 0x3dd,
    'disin'                  : 0x22f2,
    'div'                    : 0xf7,
    'divideontimes'          : 0x22c7,
    'dot'                    : 0x307,
    'doteq'                  : 0x2250,
    'doteqdot'               : 0x2251,
    'dotminus'               : 0x2238,
    'dotplus'                : 0x2214,
    'dots'                   : 0x2026,
    'dotsminusdots'          : 0x223a,
    'doublebarwedge'         : 0x2306,
    'downarrow'              : 0x2193,
    'downdownarrows'         : 0x21ca,
    'downharpoonleft'        : 0x21c3,
    'downharpoonright'       : 0x21c2,
    'downzigzagarrow'        : 0x21af,
    'ell'                    : 0x2113,
    'emdash'                 : 0x2014,
    'emptyset'               : 0x2205,
    'endash'                 : 0x2013,
    'epsilon'                : 0x3b5,
    'eqcirc'                 : 0x2256,
    'eqcolon'                : 0x2255,
    'eqdef'                  : 0x225d,
    'eqgtr'                  : 0x22dd,
    'eqless'                 : 0x22dc,
    'eqsim'                  : 0x2242,
    'eqslantgtr'             : 0x2a96,
    'eqslantless'            : 0x2a95,
    'equal'                  : 0x3d,
    'equalparallel'          : 0x22d5,
    'equiv'                  : 0x2261,
    'eta'                    : 0x3b7,
    'eth'                    : 0xf0,
    'exists'                 : 0x2203,
    'fallingdotseq'          : 0x2252,
    'flat'                   : 0x266d,
    'forall'                 : 0x2200,
    'frakC'                  : 0x212d,
    'frakZ'                  : 0x2128,
    'frown'                  : 0x2322,
    'gamma'                  : 0x3b3,
    'geq'                    : 0x2265,
    'geqq'                   : 0x2267,
    'geqslant'               : 0x2a7e,
    'gg'                     : 0x226b,
    'ggg'                    : 0x22d9,
    'gimel'                  : 0x2137,
    'gnapprox'               : 0x2a8a,
    'gneqq'                  : 0x2269,
    'gnsim'                  : 0x22e7,
    'grave'                  : 0x300,
    'greater'                : 0x3e,
    'gtrapprox'              : 0x2a86,
    'gtrdot'                 : 0x22d7,
    'gtreqless'              : 0x22db,
    'gtreqqless'             : 0x2a8c,
    'gtrless'                : 0x2277,
    'gtrsim'                 : 0x2273,
    'guillemotleft'          : 0xab,
    'guillemotright'         : 0xbb,
    'guilsinglleft'          : 0x2039,
    'guilsinglright'         : 0x203a,
    'hat'                    : 0x302,
    'hbar'                   : 0x127,
    'heartsuit'              : 0x2661,
    'hermitmatrix'           : 0x22b9,
    'hookleftarrow'          : 0x21a9,
    'hookrightarrow'         : 0x21aa,
    'hslash'                 : 0x210f,
    'i'                      : 0x131,
    'iiiint'                 : 0x2a0c,
    'iiint'                  : 0x222d,
    'iint'                   : 0x222c,
    'imageof'                : 0x22b7,
    'imath'                  : 0x131,
    'in'                     : 0x2208,
    'increment'              : 0x2206,
    'infty'                  : 0x221e,
    'int'                    : 0x222b,
    'intercal'               : 0x22ba,
    'invnot'                 : 0x2310,
    'iota'                   : 0x3b9,
    'isinE'                  : 0x22f9,
    'isindot'                : 0x22f5,
    'isinobar'               : 0x22f7,
    'isins'                  : 0x22f4,
    'isinvb'                 : 0x22f8,
    'jmath'                  : 0x237,
    'k'                      : 0x328,
    'kappa'                  : 0x3ba,
    'kernelcontraction'      : 0x223b,
    'l'                      : 0x142,
    'lambda'                 : 0x3bb,
    'lambdabar'              : 0x19b,
    'langle'                 : 0x27e8,
    'lasp'                   : 0x2bd,
    'lbrace'                 : 0x7b,
    'lbrack'                 : 0x5b,
    'lceil'                  : 0x2308,
    'ldots'                  : 0x2026,
    'leadsto'                : 0x21dd,
    'leftarrow'              : 0x2190,
    'leftarrowtail'          : 0x21a2,
    'leftbrace'              : 0x7b,
    'leftharpoonaccent'      : 0x20d0,
    'leftharpoondown'        : 0x21bd,
    'leftharpoonup'          : 0x21bc,
    'leftleftarrows'         : 0x21c7,
    'leftparen'              : 0x28,
    'leftrightarrow'         : 0x2194,
    'leftrightarrows'        : 0x21c6,
    'leftrightharpoons'      : 0x21cb,
    'leftrightsquigarrow'    : 0x21ad,
    'leftsquigarrow'         : 0x219c,
    'leftthreetimes'         : 0x22cb,
    'leq'                    : 0x2264,
    'leqq'                   : 0x2266,
    'leqslant'               : 0x2a7d,
    'less'                   : 0x3c,
    'lessapprox'             : 0x2a85,
    'lessdot'                : 0x22d6,
    'lesseqgtr'              : 0x22da,
    'lesseqqgtr'             : 0x2a8b,
    'lessgtr'                : 0x2276,
    'lesssim'                : 0x2272,
    'lfloor'                 : 0x230a,
    'lgroup'                 : 0x27ee,
    'lhd'                    : 0x25c1,
    'll'                     : 0x226a,
    'llcorner'               : 0x231e,
    'lll'                    : 0x22d8,
    'lnapprox'               : 0x2a89,
    'lneqq'                  : 0x2268,
    'lnsim'                  : 0x22e6,
    'longleftarrow'          : 0x27f5,
    'longleftrightarrow'     : 0x27f7,
    'longmapsto'             : 0x27fc,
    'longrightarrow'         : 0x27f6,
    'looparrowleft'          : 0x21ab,
    'looparrowright'         : 0x21ac,
    'lq'                     : 0x2018,
    'lrcorner'               : 0x231f,
    'ltimes'                 : 0x22c9,
    'macron'                 : 0xaf,
    'maltese'                : 0x2720,
    'mapsdown'               : 0x21a7,
    'mapsfrom'               : 0x21a4,
    'mapsto'                 : 0x21a6,
    'mapsup'                 : 0x21a5,
    'measeq'                 : 0x225e,
    'measuredangle'          : 0x2221,
    'measuredrightangle'     : 0x22be,
    'merge'                  : 0x2a55,
    'mho'                    : 0x2127,
    'mid'                    : 0x2223,
    'minus'                  : 0x2212,
    'minuscolon'             : 0x2239,
    'models'                 : 0x22a7,
    'mp'                     : 0x2213,
    'mu'                     : 0x3bc,
    'multimap'               : 0x22b8,
    'nLeftarrow'             : 0x21cd,
    'nLeftrightarrow'        : 0x21ce,
    'nRightarrow'            : 0x21cf,
    'nVDash'                 : 0x22af,
    'nVdash'                 : 0x22ae,
    'nabla'                  : 0x2207,
    'napprox'                : 0x2249,
    'natural'                : 0x266e,
    'ncong'                  : 0x2247,
    'ne'                     : 0x2260,
    'nearrow'                : 0x2197,
    'neg'                    : 0xac,
    'neq'                    : 0x2260,
    'nequiv'                 : 0x2262,
    'nexists'                : 0x2204,
    'ngeq'                   : 0x2271,
    'ngtr'                   : 0x226f,
    'ngtrless'               : 0x2279,
    'ngtrsim'                : 0x2275,
    'ni'                     : 0x220b,
    'niobar'                 : 0x22fe,
    'nis'                    : 0x22fc,
    'nisd'                   : 0x22fa,
    'nleftarrow'             : 0x219a,
    'nleftrightarrow'        : 0x21ae,
    'nleq'                   : 0x2270,
    'nless'                  : 0x226e,
    'nlessgtr'               : 0x2278,
    'nlesssim'               : 0x2274,
    'nmid'                   : 0x2224,
    'not'                    : 0x338,
    'notin'                  : 0x2209,
    'notsmallowns'           : 0x220c,
    'nparallel'              : 0x2226,
    'nprec'                  : 0x2280,
    'npreccurlyeq'           : 0x22e0,
    'nrightarrow'            : 0x219b,
    'nsim'                   : 0x2241,
    'nsimeq'                 : 0x2244,
    'nsqsubseteq'            : 0x22e2,
    'nsqsupseteq'            : 0x22e3,
    'nsubset'                : 0x2284,
    'nsubseteq'              : 0x2288,
    'nsucc'                  : 0x2281,
    'nsucccurlyeq'           : 0x22e1,
    'nsupset'                : 0x2285,
    'nsupseteq'              : 0x2289,
    'ntriangleleft'          : 0x22ea,
    'ntrianglelefteq'        : 0x22ec,
    'ntriangleright'         : 0x22eb,
    'ntrianglerighteq'       : 0x22ed,
    'nu'                     : 0x3bd,
    'nvDash'                 : 0x22ad,
    'nvdash'                 : 0x22ac,
    'nwarrow'                : 0x2196,
    'o'                      : 0xf8,
    'obar'                   : 0x233d,
    'ocirc'                  : 0x30a,
    'odot'                   : 0x2299,
    'oe'                     : 0x153,
    'oequal'                 : 0x229c,
    'oiiint'                 : 0x2230,
    'oiint'                  : 0x222f,
    'oint'                   : 0x222e,
    'omega'                  : 0x3c9,
    'ominus'                 : 0x2296,
    'oplus'                  : 0x2295,
    'origof'                 : 0x22b6,
    'oslash'                 : 0x2298,
    'otimes'                 : 0x2297,
    'overarc'                : 0x311,
    'overleftarrow'          : 0x20d6,
    'overleftrightarrow'     : 0x20e1,
    'parallel'               : 0x2225,
    'partial'                : 0x2202,
    'perp'                   : 0x27c2,
    'perthousand'            : 0x2030,
    'phi'                    : 0x3d5,
    'pi'                     : 0x3c0,
    'pitchfork'              : 0x22d4,
    'plus'                   : 0x2b,
    'pm'                     : 0xb1,
    'prec'                   : 0x227a,
    'precapprox'             : 0x2ab7,
    'preccurlyeq'            : 0x227c,
    'preceq'                 : 0x227c,
    'precnapprox'            : 0x2ab9,
    'precnsim'               : 0x22e8,
    'precsim'                : 0x227e,
    'prime'                  : 0x2032,
    'prod'                   : 0x220f,
    'propto'                 : 0x221d,
    'prurel'                 : 0x22b0,
    'psi'                    : 0x3c8,
    'quad'                   : 0x2003,
    'questeq'                : 0x225f,
    'rangle'                 : 0x27e9,
    'rasp'                   : 0x2bc,
    'ratio'                  : 0x2236,
    'rbrace'                 : 0x7d,
    'rbrack'                 : 0x5d,
    'rceil'                  : 0x2309,
    'rfloor'                 : 0x230b,
    'rgroup'                 : 0x27ef,
    'rhd'                    : 0x25b7,
    'rho'                    : 0x3c1,
    'rightModels'            : 0x22ab,
    'rightangle'             : 0x221f,
    'rightarrow'             : 0x2192,
    'rightarrowbar'          : 0x21e5,
    'rightarrowtail'         : 0x21a3,
    'rightassert'            : 0x22a6,
    'rightbrace'             : 0x7d,
    'rightharpoonaccent'     : 0x20d1,
    'rightharpoondown'       : 0x21c1,
    'rightharpoonup'         : 0x21c0,
    'rightleftarrows'        : 0x21c4,
    'rightleftharpoons'      : 0x21cc,
    'rightparen'             : 0x29,
    'rightrightarrows'       : 0x21c9,
    'rightsquigarrow'        : 0x219d,
    'rightthreetimes'        : 0x22cc,
    'rightzigzagarrow'       : 0x21dd,
    'ring'                   : 0x2da,
    'risingdotseq'           : 0x2253,
    'rq'                     : 0x2019,
    'rtimes'                 : 0x22ca,
    'scrB'                   : 0x212c,
    'scrE'                   : 0x2130,
    'scrF'                   : 0x2131,
    'scrH'                   : 0x210b,
    'scrI'                   : 0x2110,
    'scrL'                   : 0x2112,
    'scrM'                   : 0x2133,
    'scrR'                   : 0x211b,
    'scre'                   : 0x212f,
    'scrg'                   : 0x210a,
    'scro'                   : 0x2134,
    'scurel'                 : 0x22b1,
    'searrow'                : 0x2198,
    'setminus'               : 0x2216,
    'sharp'                  : 0x266f,
    'sigma'                  : 0x3c3,
    'sim'                    : 0x223c,
    'simeq'                  : 0x2243,
    'simneqq'                : 0x2246,
    'sinewave'               : 0x223f,
    'slash'                  : 0x2215,
    'smallin'                : 0x220a,
    'smallintclockwise'      : 0x2231,
    'smallointctrcclockwise' : 0x2233,
    'smallowns'              : 0x220d,
    'smallsetminus'          : 0x2216,
    'smallvarointclockwise'  : 0x2232,
    'smile'                  : 0x2323,
    'solbar'                 : 0x233f,
    'spadesuit'              : 0x2660,
    'spadesuitopen'          : 0x2664,
    'sphericalangle'         : 0x2222,
    'sqcap'                  : 0x2293,
    'sqcup'                  : 0x2294,
    'sqsubset'               : 0x228f,
    'sqsubseteq'             : 0x2291,
    'sqsubsetneq'            : 0x22e4,
    'sqsupset'               : 0x2290,
    'sqsupseteq'             : 0x2292,
    'sqsupsetneq'            : 0x22e5,
    'ss'                     : 0xdf,
    'star'                   : 0x22c6,
    'stareq'                 : 0x225b,
    'sterling'               : 0xa3,
    'subset'                 : 0x2282,
    'subseteq'               : 0x2286,
    'subseteqq'              : 0x2ac5,
    'subsetneq'              : 0x228a,
    'subsetneqq'             : 0x2acb,
    'succ'                   : 0x227b,
    'succapprox'             : 0x2ab8,
    'succcurlyeq'            : 0x227d,
    'succeq'                 : 0x227d,
    'succnapprox'            : 0x2aba,
    'succnsim'               : 0x22e9,
    'succsim'                : 0x227f,
    'sum'                    : 0x2211,
    'supset'                 : 0x2283,
    'supseteq'               : 0x2287,
    'supseteqq'              : 0x2ac6,
    'supsetneq'              : 0x228b,
    'supsetneqq'             : 0x2acc,
    'swarrow'                : 0x2199,
    't'                      : 0x361,
    'tau'                    : 0x3c4,
    'textasciiacute'         : 0xb4,
    'textasciicircum'        : 0x5e,
    'textasciigrave'         : 0x60,
    'textasciitilde'         : 0x7e,
    'textexclamdown'         : 0xa1,
    'textquestiondown'       : 0xbf,
    'textquotedblleft'       : 0x201c,
    'textquotedblright'      : 0x201d,
    'therefore'              : 0x2234,
    'theta'                  : 0x3b8,
    'thickspace'             : 0x2005,
    'thorn'                  : 0xfe,
    'tilde'                  : 0x303,
    'times'                  : 0xd7,
    'to'                     : 0x2192,
    'top'                    : 0x22a4,
    'triangle'               : 0x25b3,
    'triangledown'           : 0x25bf,
    'triangleeq'             : 0x225c,
    'triangleleft'           : 0x25c1,
    'trianglelefteq'         : 0x22b4,
    'triangleq'              : 0x225c,
    'triangleright'          : 0x25b7,
    'trianglerighteq'        : 0x22b5,
    'turnednot'              : 0x2319,
    'twoheaddownarrow'       : 0x21a1,
    'twoheadleftarrow'       : 0x219e,
    'twoheadrightarrow'      : 0x21a0,
    'twoheaduparrow'         : 0x219f,
    'ulcorner'               : 0x231c,
    'underbar'               : 0x331,
    'unlhd'                  : 0x22b4,
    'unrhd'                  : 0x22b5,
    'uparrow'                : 0x2191,
    'updownarrow'            : 0x2195,
    'updownarrowbar'         : 0x21a8,
    'updownarrows'           : 0x21c5,
    'upharpoonleft'          : 0x21bf,
    'upharpoonright'         : 0x21be,
    'uplus'                  : 0x228e,
    'upsilon'                : 0x3c5,
    'upuparrows'             : 0x21c8,
    'urcorner'               : 0x231d,
    'vDash'                  : 0x22a8,
    'varepsilon'             : 0x3b5,
    'varisinobar'            : 0x22f6,
    'varisins'               : 0x22f3,
    'varkappa'               : 0x3f0,
    'varlrtriangle'          : 0x22bf,
    'varniobar'              : 0x22fd,
    'varnis'                 : 0x22fb,
    'varnothing'             : 0x2205,
    'varphi'                 : 0x3c6,
    'varpi'                  : 0x3d6,
    'varpropto'              : 0x221d,
    'varrho'                 : 0x3f1,
    'varsigma'               : 0x3c2,
    'vartheta'               : 0x3d1,
    'vartriangle'            : 0x25b5,
    'vartriangleleft'        : 0x22b2,
    'vartriangleright'       : 0x22b3,
    'vdash'                  : 0x22a2,
    'vdots'                  : 0x22ee,
    'vec'                    : 0x20d7,
    'vee'                    : 0x2228,
    'veebar'                 : 0x22bb,
    'veeeq'                  : 0x225a,
    'vert'                   : 0x7c,
    'wedge'                  : 0x2227,
    'wedgeq'                 : 0x2259,
    'widebar'                : 0x305,
    'widehat'                : 0x302,
    'widetilde'              : 0x303,
    'wp'                     : 0x2118,
    'wr'                     : 0x2240,
    'xi'                     : 0x3be,
    'yen'                    : 0xa5,
    'zeta'                   : 0x3b6,
    '{'                      : 0x7b,
    '|'                      : 0x2016,
    '}'                      : 0x7d,
}

# Each element is a 4-tuple of the form:
#   src_start, src_end, dst_font, dst_start

_EntryTypeIn = tuple[str, str, str, Union[str, int]]
_EntryTypeOut = tuple[int, int, str, int]

_stix_virtual_fonts: dict[str, Union[dict[
    str, list[_EntryTypeIn]], list[_EntryTypeIn]]] = {
    'bb': {
        "rm": [
            ("\N{DIGIT ZERO}",
             "\N{DIGIT NINE}",
             "rm",
             "\N{MATHEMATICAL DOUBLE-STRUCK DIGIT ZERO}"),
            ("\N{LATIN CAPITAL LETTER A}",
             "\N{LATIN CAPITAL LETTER B}",
             "rm",
             "\N{MATHEMATICAL DOUBLE-STRUCK CAPITAL A}"),
            ("\N{LATIN CAPITAL LETTER C}",
             "\N{LATIN CAPITAL LETTER C}",
             "rm",
             "\N{DOUBLE-STRUCK CAPITAL C}"),
            ("\N{LATIN CAPITAL LETTER D}",
             "\N{LATIN CAPITAL LETTER G}",
             "rm",
             "\N{MATHEMATICAL DOUBLE-STRUCK CAPITAL D}"),
            ("\N{LATIN CAPITAL LETTER H}",
             "\N{LATIN CAPITAL LETTER H}",
             "rm",
             "\N{DOUBLE-STRUCK CAPITAL H}"),
            ("\N{LATIN CAPITAL LETTER I}",
             "\N{LATIN CAPITAL LETTER M}",
             "rm",
             "\N{MATHEMATICAL DOUBLE-STRUCK CAPITAL I}"),
            ("\N{LATIN CAPITAL LETTER N}",
             "\N{LATIN CAPITAL LETTER N}",
             "rm",
             "\N{DOUBLE-STRUCK CAPITAL N}"),
            ("\N{LATIN CAPITAL LETTER O}",
             "\N{LATIN CAPITAL LETTER O}",
             "rm",
             "\N{MATHEMATICAL DOUBLE-STRUCK CAPITAL O}"),
            ("\N{LATIN CAPITAL LETTER P}",
             "\N{LATIN CAPITAL LETTER Q}",
             "rm",
             "\N{DOUBLE-STRUCK CAPITAL P}"),
            ("\N{LATIN CAPITAL LETTER R}",
             "\N{LATIN CAPITAL LETTER R}",
             "rm",
             "\N{DOUBLE-STRUCK CAPITAL R}"),
            ("\N{LATIN CAPITAL LETTER S}",
             "\N{LATIN CAPITAL LETTER Y}",
             "rm",
             "\N{MATHEMATICAL DOUBLE-STRUCK CAPITAL S}"),
            ("\N{LATIN CAPITAL LETTER Z}",
             "\N{LATIN CAPITAL LETTER Z}",
             "rm",
             "\N{DOUBLE-STRUCK CAPITAL Z}"),
            ("\N{LATIN SMALL LETTER A}",
             "\N{LATIN SMALL LETTER Z}",
             "rm",
             "\N{MATHEMATICAL DOUBLE-STRUCK SMALL A}"),
            ("\N{GREEK CAPITAL LETTER GAMMA}",
             "\N{GREEK CAPITAL LETTER GAMMA}",
             "rm",
             "\N{DOUBLE-STRUCK CAPITAL GAMMA}"),
            ("\N{GREEK CAPITAL LETTER PI}",
             "\N{GREEK CAPITAL LETTER PI}",
             "rm",
             "\N{DOUBLE-STRUCK CAPITAL PI}"),
            ("\N{GREEK CAPITAL LETTER SIGMA}",
             "\N{GREEK CAPITAL LETTER SIGMA}",
             "rm",
             "\N{DOUBLE-STRUCK N-ARY SUMMATION}"),
            ("\N{GREEK SMALL LETTER GAMMA}",
             "\N{GREEK SMALL LETTER GAMMA}",
             "rm",
             "\N{DOUBLE-STRUCK SMALL GAMMA}"),
            ("\N{GREEK SMALL LETTER PI}",
             "\N{GREEK SMALL LETTER PI}",
             "rm",
             "\N{DOUBLE-STRUCK SMALL PI}"),
        ],
        "it": [
            ("\N{DIGIT ZERO}",
             "\N{DIGIT NINE}",
             "rm",
             "\N{MATHEMATICAL DOUBLE-STRUCK DIGIT ZERO}"),
            ("\N{LATIN CAPITAL LETTER A}",
             "\N{LATIN CAPITAL LETTER B}",
             "it",
             0xe154),
            ("\N{LATIN CAPITAL LETTER C}",
             "\N{LATIN CAPITAL LETTER C}",
             "it",
             "\N{DOUBLE-STRUCK CAPITAL C}"),
            ("\N{LATIN CAPITAL LETTER D}",
             "\N{LATIN CAPITAL LETTER D}",
             "it",
             "\N{DOUBLE-STRUCK ITALIC CAPITAL D}"),
            ("\N{LATIN CAPITAL LETTER E}",
             "\N{LATIN CAPITAL LETTER G}",
             "it",
             0xe156),
            ("\N{LATIN CAPITAL LETTER H}",
             "\N{LATIN CAPITAL LETTER H}",
             "it",
             "\N{DOUBLE-STRUCK CAPITAL H}"),
            ("\N{LATIN CAPITAL LETTER I}",
             "\N{LATIN CAPITAL LETTER M}",
             "it",
             0xe159),
            ("\N{LATIN CAPITAL LETTER N}",
             "\N{LATIN CAPITAL LETTER N}",
             "it",
             "\N{DOUBLE-STRUCK CAPITAL N}"),
            ("\N{LATIN CAPITAL LETTER O}",
             "\N{LATIN CAPITAL LETTER O}",
             "it",
             0xe15e),
            ("\N{LATIN CAPITAL LETTER P}",
             "\N{LATIN CAPITAL LETTER Q}",
             "it",
             "\N{DOUBLE-STRUCK CAPITAL P}"),
            ("\N{LATIN CAPITAL LETTER R}",
             "\N{LATIN CAPITAL LETTER R}",
             "it",
             "\N{DOUBLE-STRUCK CAPITAL R}"),
            ("\N{LATIN CAPITAL LETTER S}",
             "\N{LATIN CAPITAL LETTER Y}",
             "it",
             0xe15f),
            ("\N{LATIN CAPITAL LETTER Z}",
             "\N{LATIN CAPITAL LETTER Z}",
             "it",
             "\N{DOUBLE-STRUCK CAPITAL Z}"),
            ("\N{LATIN SMALL LETTER A}",
             "\N{LATIN SMALL LETTER C}",
             "it",
             0xe166),
            ("\N{LATIN SMALL LETTER D}",
             "\N{LATIN SMALL LETTER E}",
             "it",
             "\N{DOUBLE-STRUCK ITALIC SMALL D}"),
            ("\N{LATIN SMALL LETTER F}",
             "\N{LATIN SMALL LETTER H}",
             "it",
             0xe169),
            ("\N{LATIN SMALL LETTER I}",
             "\N{LATIN SMALL LETTER J}",
             "it",
             "\N{DOUBLE-STRUCK ITALIC SMALL I}"),
            ("\N{LATIN SMALL LETTER K}",
             "\N{LATIN SMALL LETTER Z}",
             "it",
             0xe16c),
            ("\N{GREEK CAPITAL LETTER GAMMA}",
             "\N{GREEK CAPITAL LETTER GAMMA}",
             "it",
             "\N{DOUBLE-STRUCK CAPITAL GAMMA}"),  # \Gamma (not in beta STIX fonts)
            ("\N{GREEK CAPITAL LETTER PI}",
             "\N{GREEK CAPITAL LETTER PI}",
             "it",
             "\N{DOUBLE-STRUCK CAPITAL PI}"),
            ("\N{GREEK CAPITAL LETTER SIGMA}",
             "\N{GREEK CAPITAL LETTER SIGMA}",
             "it",
             "\N{DOUBLE-STRUCK N-ARY SUMMATION}"),  # \Sigma (not in beta STIX fonts)
            ("\N{GREEK SMALL LETTER GAMMA}",
             "\N{GREEK SMALL LETTER GAMMA}",
             "it",
             "\N{DOUBLE-STRUCK SMALL GAMMA}"),  # \gamma (not in beta STIX fonts)
            ("\N{GREEK SMALL LETTER PI}",
             "\N{GREEK SMALL LETTER PI}",
             "it",
             "\N{DOUBLE-STRUCK SMALL PI}"),
        ],
        "bf": [
            ("\N{DIGIT ZERO}",
             "\N{DIGIT NINE}",
             "rm",
             "\N{MATHEMATICAL DOUBLE-STRUCK DIGIT ZERO}"),
            ("\N{LATIN CAPITAL LETTER A}",
             "\N{LATIN CAPITAL LETTER B}",
             "bf",
             0xe38a),
            ("\N{LATIN CAPITAL LETTER C}",
             "\N{LATIN CAPITAL LETTER C}",
             "bf",
             "\N{DOUBLE-STRUCK CAPITAL C}"),
            ("\N{LATIN CAPITAL LETTER D}",
             "\N{LATIN CAPITAL LETTER D}",
             "bf",
             "\N{DOUBLE-STRUCK ITALIC CAPITAL D}"),
            ("\N{LATIN CAPITAL LETTER E}",
             "\N{LATIN CAPITAL LETTER G}",
             "bf",
             0xe38d),
            ("\N{LATIN CAPITAL LETTER H}",
             "\N{LATIN CAPITAL LETTER H}",
             "bf",
             "\N{DOUBLE-STRUCK CAPITAL H}"),
            ("\N{LATIN CAPITAL LETTER I}",
             "\N{LATIN CAPITAL LETTER M}",
             "bf",
             0xe390),
            ("\N{LATIN CAPITAL LETTER N}",
             "\N{LATIN CAPITAL LETTER N}",
             "bf",
             "\N{DOUBLE-STRUCK CAPITAL N}"),
            ("\N{LATIN CAPITAL LETTER O}",
             "\N{LATIN CAPITAL LETTER O}",
             "bf",
             0xe395),
            ("\N{LATIN CAPITAL LETTER P}",
             "\N{LATIN CAPITAL LETTER Q}",
             "bf",
             "\N{DOUBLE-STRUCK CAPITAL P}"),
            ("\N{LATIN CAPITAL LETTER R}",
             "\N{LATIN CAPITAL LETTER R}",
             "bf",
             "\N{DOUBLE-STRUCK CAPITAL R}"),
            ("\N{LATIN CAPITAL LETTER S}",
             "\N{LATIN CAPITAL LETTER Y}",
             "bf",
             0xe396),
            ("\N{LATIN CAPITAL LETTER Z}",
             "\N{LATIN CAPITAL LETTER Z}",
             "bf",
             "\N{DOUBLE-STRUCK CAPITAL Z}"),
            ("\N{LATIN SMALL LETTER A}",
             "\N{LATIN SMALL LETTER C}",
             "bf",
             0xe39d),
            ("\N{LATIN SMALL LETTER D}",
             "\N{LATIN SMALL LETTER E}",
             "bf",
             "\N{DOUBLE-STRUCK ITALIC SMALL D}"),
            ("\N{LATIN SMALL LETTER F}",
             "\N{LATIN SMALL LETTER H}",
             "bf",
             0xe3a2),
            ("\N{LATIN SMALL LETTER I}",
             "\N{LATIN SMALL LETTER J}",
             "bf",
             "\N{DOUBLE-STRUCK ITALIC SMALL I}"),
            ("\N{LATIN SMALL LETTER K}",
             "\N{LATIN SMALL LETTER Z}",
             "bf",
             0xe3a7),
            ("\N{GREEK CAPITAL LETTER GAMMA}",
             "\N{GREEK CAPITAL LETTER GAMMA}",
             "bf",
             "\N{DOUBLE-STRUCK CAPITAL GAMMA}"),
            ("\N{GREEK CAPITAL LETTER PI}",
             "\N{GREEK CAPITAL LETTER PI}",
             "bf",
             "\N{DOUBLE-STRUCK CAPITAL PI}"),
            ("\N{GREEK CAPITAL LETTER SIGMA}",
             "\N{GREEK CAPITAL LETTER SIGMA}",
             "bf",
             "\N{DOUBLE-STRUCK N-ARY SUMMATION}"),
            ("\N{GREEK SMALL LETTER GAMMA}",
             "\N{GREEK SMALL LETTER GAMMA}",
             "bf",
             "\N{DOUBLE-STRUCK SMALL GAMMA}"),
            ("\N{GREEK SMALL LETTER PI}",
             "\N{GREEK SMALL LETTER PI}",
             "bf",
             "\N{DOUBLE-STRUCK SMALL PI}"),
        ],
    },
    'cal': [
        ("\N{LATIN CAPITAL LETTER A}",
         "\N{LATIN CAPITAL LETTER Z}",
         "it",
         0xe22d),
    ],
    'frak': {
        "rm": [
            ("\N{LATIN CAPITAL LETTER A}",
             "\N{LATIN CAPITAL LETTER B}",
             "rm",
             "\N{MATHEMATICAL FRAKTUR CAPITAL A}"),
            ("\N{LATIN CAPITAL LETTER C}",
             "\N{LATIN CAPITAL LETTER C}",
             "rm",
             "\N{BLACK-LETTER CAPITAL C}"),
            ("\N{LATIN CAPITAL LETTER D}",
             "\N{LATIN CAPITAL LETTER G}",
             "rm",
             "\N{MATHEMATICAL FRAKTUR CAPITAL D}"),
            ("\N{LATIN CAPITAL LETTER H}",
             "\N{LATIN CAPITAL LETTER H}",
             "rm",
             "\N{BLACK-LETTER CAPITAL H}"),
            ("\N{LATIN CAPITAL LETTER I}",
             "\N{LATIN CAPITAL LETTER I}",
             "rm",
             "\N{BLACK-LETTER CAPITAL I}"),
            ("\N{LATIN CAPITAL LETTER J}",
             "\N{LATIN CAPITAL LETTER Q}",
             "rm",
             "\N{MATHEMATICAL FRAKTUR CAPITAL J}"),
            ("\N{LATIN CAPITAL LETTER R}",
             "\N{LATIN CAPITAL LETTER R}",
             "rm",
             "\N{BLACK-LETTER CAPITAL R}"),
            ("\N{LATIN CAPITAL LETTER S}",
             "\N{LATIN CAPITAL LETTER Y}",
             "rm",
             "\N{MATHEMATICAL FRAKTUR CAPITAL S}"),
            ("\N{LATIN CAPITAL LETTER Z}",
             "\N{LATIN CAPITAL LETTER Z}",
             "rm",
             "\N{BLACK-LETTER CAPITAL Z}"),
            ("\N{LATIN SMALL LETTER A}",
             "\N{LATIN SMALL LETTER Z}",
             "rm",
             "\N{MATHEMATICAL FRAKTUR SMALL A}"),
            ],
        "bf": [
            ("\N{LATIN CAPITAL LETTER A}",
             "\N{LATIN CAPITAL LETTER Z}",
             "bf",
             "\N{MATHEMATICAL BOLD FRAKTUR CAPITAL A}"),
            ("\N{LATIN SMALL LETTER A}",
             "\N{LATIN SMALL LETTER Z}",
             "bf",
             "\N{MATHEMATICAL BOLD FRAKTUR SMALL A}"),
        ],
    },
    'scr': [
        ("\N{LATIN CAPITAL LETTER A}",
         "\N{LATIN CAPITAL LETTER A}",
         "it",
         "\N{MATHEMATICAL SCRIPT CAPITAL A}"),
        ("\N{LATIN CAPITAL LETTER B}",
         "\N{LATIN CAPITAL LETTER B}",
         "it",
         "\N{SCRIPT CAPITAL B}"),
        ("\N{LATIN CAPITAL LETTER C}",
         "\N{LATIN CAPITAL LETTER D}",
         "it",
         "\N{MATHEMATICAL SCRIPT CAPITAL C}"),
        ("\N{LATIN CAPITAL LETTER E}",
         "\N{LATIN CAPITAL LETTER F}",
         "it",
         "\N{SCRIPT CAPITAL E}"),
        ("\N{LATIN CAPITAL LETTER G}",
         "\N{LATIN CAPITAL LETTER G}",
         "it",
         "\N{MATHEMATICAL SCRIPT CAPITAL G}"),
        ("\N{LATIN CAPITAL LETTER H}",
         "\N{LATIN CAPITAL LETTER H}",
         "it",
         "\N{SCRIPT CAPITAL H}"),
        ("\N{LATIN CAPITAL LETTER I}",
         "\N{LATIN CAPITAL LETTER I}",
         "it",
         "\N{SCRIPT CAPITAL I}"),
        ("\N{LATIN CAPITAL LETTER J}",
         "\N{LATIN CAPITAL LETTER K}",
         "it",
         "\N{MATHEMATICAL SCRIPT CAPITAL J}"),
        ("\N{LATIN CAPITAL LETTER L}",
         "\N{LATIN CAPITAL LETTER L}",
         "it",
         "\N{SCRIPT CAPITAL L}"),
        ("\N{LATIN CAPITAL LETTER M}",
         "\N{LATIN CAPITAL LETTER M}",
         "it",
         "\N{SCRIPT CAPITAL M}"),
        ("\N{LATIN CAPITAL LETTER N}",
         "\N{LATIN CAPITAL LETTER Q}",
         "it",
         "\N{MATHEMATICAL SCRIPT CAPITAL N}"),
        ("\N{LATIN CAPITAL LETTER R}",
         "\N{LATIN CAPITAL LETTER R}",
         "it",
         "\N{SCRIPT CAPITAL R}"),
        ("\N{LATIN CAPITAL LETTER S}",
         "\N{LATIN CAPITAL LETTER Z}",
         "it",
         "\N{MATHEMATICAL SCRIPT CAPITAL S}"),
        ("\N{LATIN SMALL LETTER A}",
         "\N{LATIN SMALL LETTER D}",
         "it",
         "\N{MATHEMATICAL SCRIPT SMALL A}"),
        ("\N{LATIN SMALL LETTER E}",
         "\N{LATIN SMALL LETTER E}",
         "it",
         "\N{SCRIPT SMALL E}"),
        ("\N{LATIN SMALL LETTER F}",
         "\N{LATIN SMALL LETTER F}",
         "it",
         "\N{MATHEMATICAL SCRIPT SMALL F}"),
        ("\N{LATIN SMALL LETTER G}",
         "\N{LATIN SMALL LETTER G}",
         "it",
         "\N{SCRIPT SMALL G}"),
        ("\N{LATIN SMALL LETTER H}",
         "\N{LATIN SMALL LETTER N}",
         "it",
         "\N{MATHEMATICAL SCRIPT SMALL H}"),
        ("\N{LATIN SMALL LETTER O}",
         "\N{LATIN SMALL LETTER O}",
         "it",
         "\N{SCRIPT SMALL O}"),
        ("\N{LATIN SMALL LETTER P}",
         "\N{LATIN SMALL LETTER Z}",
         "it",
         "\N{MATHEMATICAL SCRIPT SMALL P}"),
    ],
    'sf': {
        "rm": [
            ("\N{DIGIT ZERO}",
             "\N{DIGIT NINE}",
             "rm",
             "\N{MATHEMATICAL SANS-SERIF DIGIT ZERO}"),
            ("\N{LATIN CAPITAL LETTER A}",
             "\N{LATIN CAPITAL LETTER Z}",
             "rm",
             "\N{MATHEMATICAL SANS-SERIF CAPITAL A}"),
            ("\N{LATIN SMALL LETTER A}",
             "\N{LATIN SMALL LETTER Z}",
             "rm",
             "\N{MATHEMATICAL SANS-SERIF SMALL A}"),
            ("\N{GREEK CAPITAL LETTER ALPHA}",
             "\N{GREEK CAPITAL LETTER OMEGA}",
             "rm",
             0xe17d),
            ("\N{GREEK SMALL LETTER ALPHA}",
             "\N{GREEK SMALL LETTER OMEGA}",
             "rm",
             0xe196),
            ("\N{GREEK THETA SYMBOL}",
             "\N{GREEK THETA SYMBOL}",
             "rm",
             0xe1b0),
            ("\N{GREEK PHI SYMBOL}",
             "\N{GREEK PHI SYMBOL}",
             "rm",
             0xe1b1),
            ("\N{GREEK PI SYMBOL}",
             "\N{GREEK PI SYMBOL}",
             "rm",
             0xe1b3),
            ("\N{GREEK RHO SYMBOL}",
             "\N{GREEK RHO SYMBOL}",
             "rm",
             0xe1b2),
            ("\N{GREEK LUNATE EPSILON SYMBOL}",
             "\N{GREEK LUNATE EPSILON SYMBOL}",
             "rm",
             0xe1af),
            ("\N{PARTIAL DIFFERENTIAL}",
             "\N{PARTIAL DIFFERENTIAL}",
             "rm",
             0xe17c),
        ],
        "it": [
            # These numerals are actually upright.  We don't actually
            # want italic numerals ever.
            ("\N{DIGIT ZERO}",
             "\N{DIGIT NINE}",
             "rm",
             "\N{MATHEMATICAL SANS-SERIF DIGIT ZERO}"),
            ("\N{LATIN CAPITAL LETTER A}",
             "\N{LATIN CAPITAL LETTER Z}",
             "it",
             "\N{MATHEMATICAL SANS-SERIF ITALIC CAPITAL A}"),
            ("\N{LATIN SMALL LETTER A}",
             "\N{LATIN SMALL LETTER Z}",
             "it",
             "\N{MATHEMATICAL SANS-SERIF ITALIC SMALL A}"),
            ("\N{GREEK CAPITAL LETTER ALPHA}",
             "\N{GREEK CAPITAL LETTER OMEGA}",
             "rm",
             0xe17d),
            ("\N{GREEK SMALL LETTER ALPHA}",
             "\N{GREEK SMALL LETTER OMEGA}",
             "it",
             0xe1d8),
            ("\N{GREEK THETA SYMBOL}",
             "\N{GREEK THETA SYMBOL}",
             "it",
             0xe1f2),
            ("\N{GREEK PHI SYMBOL}",
             "\N{GREEK PHI SYMBOL}",
             "it",
             0xe1f3),
            ("\N{GREEK PI SYMBOL}",
             "\N{GREEK PI SYMBOL}",
             "it",
             0xe1f5),
            ("\N{GREEK RHO SYMBOL}",
             "\N{GREEK RHO SYMBOL}",
             "it",
             0xe1f4),
            ("\N{GREEK LUNATE EPSILON SYMBOL}",
             "\N{GREEK LUNATE EPSILON SYMBOL}",
             "it",
             0xe1f1),
        ],
        "bf": [
            ("\N{DIGIT ZERO}",
             "\N{DIGIT NINE}",
             "bf",
             "\N{MATHEMATICAL SANS-SERIF BOLD DIGIT ZERO}"),
            ("\N{LATIN CAPITAL LETTER A}",
             "\N{LATIN CAPITAL LETTER Z}",
             "bf",
             "\N{MATHEMATICAL SANS-SERIF BOLD CAPITAL A}"),
            ("\N{LATIN SMALL LETTER A}",
             "\N{LATIN SMALL LETTER Z}",
             "bf",
             "\N{MATHEMATICAL SANS-SERIF BOLD SMALL A}"),
            ("\N{GREEK CAPITAL LETTER ALPHA}",
             "\N{GREEK CAPITAL LETTER OMEGA}",
             "bf",
             "\N{MATHEMATICAL SANS-SERIF BOLD CAPITAL ALPHA}"),
            ("\N{GREEK SMALL LETTER ALPHA}",
             "\N{GREEK SMALL LETTER OMEGA}",
             "bf",
             "\N{MATHEMATICAL SANS-SERIF BOLD SMALL ALPHA}"),
            ("\N{GREEK THETA SYMBOL}",
             "\N{GREEK THETA SYMBOL}",
             "bf",
             "\N{MATHEMATICAL SANS-SERIF BOLD THETA SYMBOL}"),
            ("\N{GREEK PHI SYMBOL}",
             "\N{GREEK PHI SYMBOL}",
             "bf",
             "\N{MATHEMATICAL SANS-SERIF BOLD PHI SYMBOL}"),
            ("\N{GREEK PI SYMBOL}",
             "\N{GREEK PI SYMBOL}",
             "bf",
             "\N{MATHEMATICAL SANS-SERIF BOLD PI SYMBOL}"),
            ("\N{GREEK KAPPA SYMBOL}",
             "\N{GREEK KAPPA SYMBOL}",
             "bf",
             "\N{MATHEMATICAL SANS-SERIF BOLD KAPPA SYMBOL}"),
            ("\N{GREEK RHO SYMBOL}",
             "\N{GREEK RHO SYMBOL}",
             "bf",
             "\N{MATHEMATICAL SANS-SERIF BOLD RHO SYMBOL}"),
            ("\N{GREEK LUNATE EPSILON SYMBOL}",
             "\N{GREEK LUNATE EPSILON SYMBOL}",
             "bf",
             "\N{MATHEMATICAL SANS-SERIF BOLD EPSILON SYMBOL}"),
            ("\N{PARTIAL DIFFERENTIAL}",
             "\N{PARTIAL DIFFERENTIAL}",
             "bf",
             "\N{MATHEMATICAL SANS-SERIF BOLD PARTIAL DIFFERENTIAL}"),
            ("\N{NABLA}",
             "\N{NABLA}",
             "bf",
             "\N{MATHEMATICAL SANS-SERIF BOLD NABLA}"),
        ],
        "bfit": [
            ("\N{LATIN CAPITAL LETTER A}",
             "\N{LATIN CAPITAL LETTER Z}",
             "bfit",
             "\N{MATHEMATICAL BOLD ITALIC CAPITAL A}"),
            ("\N{LATIN SMALL LETTER A}",
             "\N{LATIN SMALL LETTER Z}",
             "bfit",
             "\N{MATHEMATICAL BOLD ITALIC SMALL A}"),
            ("\N{GREEK CAPITAL LETTER GAMMA}",
             "\N{GREEK CAPITAL LETTER OMEGA}",
             "bfit",
             "\N{MATHEMATICAL BOLD ITALIC CAPITAL GAMMA}"),
            ("\N{GREEK SMALL LETTER ALPHA}",
             "\N{GREEK SMALL LETTER OMEGA}",
             "bfit",
             "\N{MATHEMATICAL BOLD ITALIC SMALL ALPHA}"),
        ],
    },
    'tt': [
        ("\N{DIGIT ZERO}",
         "\N{DIGIT NINE}",
         "rm",
         "\N{MATHEMATICAL MONOSPACE DIGIT ZERO}"),
        ("\N{LATIN CAPITAL LETTER A}",
         "\N{LATIN CAPITAL LETTER Z}",
         "rm",
         "\N{MATHEMATICAL MONOSPACE CAPITAL A}"),
        ("\N{LATIN SMALL LETTER A}",
         "\N{LATIN SMALL LETTER Z}",
         "rm",
         "\N{MATHEMATICAL MONOSPACE SMALL A}")
    ],
}


@overload
def _normalize_stix_fontcodes(d: _EntryTypeIn) -> _EntryTypeOut: ...


@overload
def _normalize_stix_fontcodes(d: list[_EntryTypeIn]) -> list[_EntryTypeOut]: ...


@overload
def _normalize_stix_fontcodes(d: dict[str, list[_EntryTypeIn] |
                                      dict[str, list[_EntryTypeIn]]]
                              ) -> dict[str, list[_EntryTypeOut] |
                                        dict[str, list[_EntryTypeOut]]]: ...


def _normalize_stix_fontcodes(d):
    if isinstance(d, tuple):
        return tuple(ord(x) if isinstance(x, str) and len(x) == 1 else x for x in d)
    elif isinstance(d, list):
        return [_normalize_stix_fontcodes(x) for x in d]
    elif isinstance(d, dict):
        return {k: _normalize_stix_fontcodes(v) for k, v in d.items()}


stix_virtual_fonts: dict[str, Union[dict[str, list[_EntryTypeOut]],
                                    list[_EntryTypeOut]]]
stix_virtual_fonts = _normalize_stix_fontcodes(_stix_virtual_fonts)

# Free redundant list now that it has been normalized
del _stix_virtual_fonts

# Fix some incorrect glyphs.
stix_glyph_fixes = {
    # Cap and Cup glyphs are swapped.
    0x22d2: 0x22d3,
    0x22d3: 0x22d2,
}
