../../Scripts/f2py.exe,sha256=D3Enm30k5P8HKQj5NzC-3knj7JjwHQg03-wj8t4XsnM,108432
numpy-1.26.4-cp311-cp311-win_amd64.whl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy-1.26.4.dist-info/DELVEWHEEL,sha256=ONX7ZAdRxnVGftNuwgk9zr8XuU7sWrnN3CcXQO7xgcU,444
numpy-1.26.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
numpy-1.26.4.dist-info/LICENSE.txt,sha256=4mEiKnSt_yj4gWDawcXLMC2Ttn6cqYnrg78xHhGplwo,48688
numpy-1.26.4.dist-info/METADATA,sha256=Wieg4nKx9uWXrLv1jo2jmuNQsjGNuN9M9J1yUY5i7rw,61040
numpy-1.26.4.dist-info/RECORD,,
numpy-1.26.4.dist-info/WHEEL,sha256=JdLTWhc73oJ-lqTBYGgiVontr_vhzwzbpAOin_2bxTI,85
numpy-1.26.4.dist-info/entry_points.txt,sha256=zddyYJuUw9Uud7LeLfynXk62_ry0lGihDwCIgugBdZM,144
numpy.libs/libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll,sha256=V7h3cr9na1wtcYx53dyfA5157DMZ_uE5jMMFrf97aeU,38168576
numpy/__config__.py,sha256=FGRAYQbDARjAqmxSJPuMePY4W5uKXxC0aCO3AKi-fIU,5064
numpy/__init__.cython-30.pxd,sha256=XIBrhqgCOhBe0v8e8EAFGjxb4oSxYKQ2Aq6T5AL12FU,37740
numpy/__init__.pxd,sha256=0vQE3U-b90dPzuUZouJzmnRYpolJhCTDJe18mi-6n8E,36081
numpy/__init__.py,sha256=FQsZVYyveMDjzGmU7GUjmelyWVymSc6hppyAHvI-Sxw,17798
numpy/__init__.pyi,sha256=e4l3ey8Gz-8iDfre4ifognM6KoMgqhJuVgLXdQB589Y,158502
numpy/__pycache__/__config__.cpython-311.pyc,,
numpy/__pycache__/__init__.cpython-311.pyc,,
numpy/__pycache__/_distributor_init.cpython-311.pyc,,
numpy/__pycache__/_globals.cpython-311.pyc,,
numpy/__pycache__/_pytesttester.cpython-311.pyc,,
numpy/__pycache__/conftest.cpython-311.pyc,,
numpy/__pycache__/ctypeslib.cpython-311.pyc,,
numpy/__pycache__/dtypes.cpython-311.pyc,,
numpy/__pycache__/exceptions.cpython-311.pyc,,
numpy/__pycache__/matlib.cpython-311.pyc,,
numpy/__pycache__/version.cpython-311.pyc,,
numpy/_core/__init__.py,sha256=wjn9zLD_mLT3yMXn9BVYHuPOeAnA_VItL-vsnpkcbwc,140
numpy/_core/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/_core/__pycache__/__init__.cpython-311.pyc,,
numpy/_core/__pycache__/_dtype.cpython-311.pyc,,
numpy/_core/__pycache__/_dtype_ctypes.cpython-311.pyc,,
numpy/_core/__pycache__/_internal.cpython-311.pyc,,
numpy/_core/__pycache__/_multiarray_umath.cpython-311.pyc,,
numpy/_core/__pycache__/multiarray.cpython-311.pyc,,
numpy/_core/__pycache__/umath.cpython-311.pyc,,
numpy/_core/_dtype.py,sha256=gs8JJgZJbddndoVCGK5n0upmoM8NFUVQmB7gh0ydkhg,132
numpy/_core/_dtype_ctypes.py,sha256=mS3YR5vccjnkWXw0OnqeO4Lzeg4E7lx31C_EpkMR1FE,153
numpy/_core/_internal.py,sha256=c4Qi0r70I8DvvTD7c-YCH7jm_VEzNDtlluqPcJnAMck,141
numpy/_core/_multiarray_umath.py,sha256=CBm2cK0EjvSOVdNo2r9xdIejPYN6RM6lXXnySlgC3eg,165
numpy/_core/multiarray.py,sha256=BPYzDuhRLyDcIQTZvz18-geRNmaVQ_0kU-8rpqlRxPM,144
numpy/_core/umath.py,sha256=KFjcFfL7ukI7OrG_HTdDHNHfXDL15jAH6Ec8TKShHFI,129
numpy/_distributor_init.py,sha256=ahBbZPz-mGZrmwx35FHQ26AiinST78FxvupiBBKGFp4,422
numpy/_globals.py,sha256=0i_jgMMUETwhWf9glFtDawg8jcsRKgm3QCxp7HPaW4E,3189
numpy/_pyinstaller/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/_pyinstaller/__pycache__/__init__.cpython-311.pyc,,
numpy/_pyinstaller/__pycache__/hook-numpy.cpython-311.pyc,,
numpy/_pyinstaller/__pycache__/pyinstaller-smoke.cpython-311.pyc,,
numpy/_pyinstaller/__pycache__/test_pyinstaller.cpython-311.pyc,,
numpy/_pyinstaller/hook-numpy.py,sha256=fYa2F8R9QwOC98b-_2maFug4wfh18qcnydYf8B0127U,1446
numpy/_pyinstaller/pyinstaller-smoke.py,sha256=xt3dl_DjxuzVTPrqmVmMOZm5-24wBG2TxldQl78Xt1g,1175
numpy/_pyinstaller/test_pyinstaller.py,sha256=31zWlvlAC2sfhdew97x8aDvcYUaV3Tc_0CwFk8pgKaM,1170
numpy/_pytesttester.py,sha256=hvGgTNzVyVtmcX2gpP7GczCggyZyo3Nk1QchguLm4QI,6938
numpy/_pytesttester.pyi,sha256=naZg3QsbkOp4kcjmCJzs3A5vmkLp1WynIgD5hUKHLZI,507
numpy/_typing/__init__.py,sha256=13hGbK51DXG3wVEpQNz6QNaaMc8fhB7zoFaiJmkiPRY,7224
numpy/_typing/__pycache__/__init__.cpython-311.pyc,,
numpy/_typing/__pycache__/_add_docstring.cpython-311.pyc,,
numpy/_typing/__pycache__/_array_like.cpython-311.pyc,,
numpy/_typing/__pycache__/_char_codes.cpython-311.pyc,,
numpy/_typing/__pycache__/_dtype_like.cpython-311.pyc,,
numpy/_typing/__pycache__/_extended_precision.cpython-311.pyc,,
numpy/_typing/__pycache__/_nbit.cpython-311.pyc,,
numpy/_typing/__pycache__/_nested_sequence.cpython-311.pyc,,
numpy/_typing/__pycache__/_scalars.cpython-311.pyc,,
numpy/_typing/__pycache__/_shape.cpython-311.pyc,,
numpy/_typing/__pycache__/setup.cpython-311.pyc,,
numpy/_typing/_add_docstring.py,sha256=tZswighLHPv8KnZ4GPt_bqykzjETgq7U-2hP6i9bGqc,4074
numpy/_typing/_array_like.py,sha256=hjbBuc098_PYrZWxQdbNXEU9LwWk7apgpad-xSHpRug,4465
numpy/_typing/_callable.pyi,sha256=y776R2o3gjoA5PEBpxWS8BhDtEfaphmh6cCTi52qwW4,11459
numpy/_typing/_char_codes.py,sha256=DRzmblPgrH3RPdOr0Hu3M3HA7Zpp_00ZEH_f7Bg5FN4,6027
numpy/_typing/_dtype_like.py,sha256=A_nIEzjSwUKiGuh7h6Gs45v-nmO--JrVcWeOSDg15A8,5907
numpy/_typing/_extended_precision.py,sha256=5PhjET4NkRp-LSgffJqfcZ1C5Cp-xERB14FNXfUvRkU,804
numpy/_typing/_nbit.py,sha256=jo5eJF4zrw7QdDw1dIEKLIIPiLM-1tEn95YbDXrdSPs,361
numpy/_typing/_nested_sequence.py,sha256=NNECI_Lo3vAKF4GnuGsrGufoayUBG5sJv3RhM7yYAOk,2652
numpy/_typing/_scalars.py,sha256=ICWPij2_rV8ZXaqECfw1LE8JntUUsrDKE4vAjl7GI68,1010
numpy/_typing/_shape.py,sha256=EB2bP9KfO-MBYFspAFMqoMBmkonfPT7qNibTSa_7CTE,218
numpy/_typing/_ufunc.pyi,sha256=s1oqnjbiFVZQBXsF8t_ntsWsPp7ULJojQs1EN11OQS4,13083
numpy/_typing/setup.py,sha256=U68U8y7UUiNDVNYps2dY41DHWdyqeO1Uis0ByzlKPd4,347
numpy/_utils/__init__.py,sha256=u-v55NdYVeOPoJczCIAefpUY7U2LKy-Zjb_WirdvJBY,752
numpy/_utils/__pycache__/__init__.cpython-311.pyc,,
numpy/_utils/__pycache__/_convertions.cpython-311.pyc,,
numpy/_utils/__pycache__/_inspect.cpython-311.pyc,,
numpy/_utils/__pycache__/_pep440.cpython-311.pyc,,
numpy/_utils/_convertions.py,sha256=vetZFqC1qB-Z9jvc7RKuU_5ETOaSbjhbKa-sVwYV8TU,347
numpy/_utils/_inspect.py,sha256=4PWDVD-iE3lZGrBCWdiLMn2oSytssuFszubUkC0oruA,7638
numpy/_utils/_pep440.py,sha256=y5Oppq3Kxn2dH3EWBYSENv_j8XjGUXWvNAiNCEJ-euI,14556
numpy/array_api/__init__.py,sha256=qPUmbmuSYL7VCJjiszT5aywsiMFJyS8am1jq6oiNLHc,10762
numpy/array_api/__pycache__/__init__.cpython-311.pyc,,
numpy/array_api/__pycache__/_array_object.cpython-311.pyc,,
numpy/array_api/__pycache__/_constants.cpython-311.pyc,,
numpy/array_api/__pycache__/_creation_functions.cpython-311.pyc,,
numpy/array_api/__pycache__/_data_type_functions.cpython-311.pyc,,
numpy/array_api/__pycache__/_dtypes.cpython-311.pyc,,
numpy/array_api/__pycache__/_elementwise_functions.cpython-311.pyc,,
numpy/array_api/__pycache__/_indexing_functions.cpython-311.pyc,,
numpy/array_api/__pycache__/_manipulation_functions.cpython-311.pyc,,
numpy/array_api/__pycache__/_searching_functions.cpython-311.pyc,,
numpy/array_api/__pycache__/_set_functions.cpython-311.pyc,,
numpy/array_api/__pycache__/_sorting_functions.cpython-311.pyc,,
numpy/array_api/__pycache__/_statistical_functions.cpython-311.pyc,,
numpy/array_api/__pycache__/_typing.cpython-311.pyc,,
numpy/array_api/__pycache__/_utility_functions.cpython-311.pyc,,
numpy/array_api/__pycache__/linalg.cpython-311.pyc,,
numpy/array_api/__pycache__/setup.cpython-311.pyc,,
numpy/array_api/_array_object.py,sha256=8CLqb6L9YaLJ69nmjMBp4oaC1Z95Z0cyvK10yQuX9v0,44927
numpy/array_api/_constants.py,sha256=FnWiJpAsFB0Gf6zbFx4tAYIPEUn6RVz_tW9JML8A3z8,94
numpy/array_api/_creation_functions.py,sha256=WqPmEGSOQXxoqug_ew0iO1SmEsNjJzmOn4xMRfYT1lA,10401
numpy/array_api/_data_type_functions.py,sha256=pdBpB-4F3v5pMBGrUDajVtTQlvYm9QLgX0xYljaz-u8,6485
numpy/array_api/_dtypes.py,sha256=Nz0vvGJRs-gXOxZCCec1mAjVvVE4EYn0bm20ByUixSg,5003
numpy/array_api/_elementwise_functions.py,sha256=aRNeTjvsTUhXfpA3Qc2704WI27F7RMwYfQxdsO1FS_g,26757
numpy/array_api/_indexing_functions.py,sha256=PCpSSA0i1FhRfBmg8bzA5DHcPu-li0-y8a2hia3sePk,735
numpy/array_api/_manipulation_functions.py,sha256=UuUcwG5DJUBGoa8TBR4w-uUA2Kkh_UMa-VgMv51vF3I,3429
numpy/array_api/_searching_functions.py,sha256=ISVHT70vShe-mfylt8Dfy5jnGbZ4p37PQrO1aoM4WjM,1766
numpy/array_api/_set_functions.py,sha256=RlYmM18U-LClmWMBH_IRdpJE-ocffgwbiGzWxWDHzxU,3054
numpy/array_api/_sorting_functions.py,sha256=qs7CCN_P-2gsU-YGKO9CRUYmArqO6y-xcK2s_DXDKjY,2085
numpy/array_api/_statistical_functions.py,sha256=Uv3EkNXI5U1bPX30EFP15pd7pUmCrseVAxVRz8MP4Js,3706
numpy/array_api/_typing.py,sha256=Awbya4L6sicm6TF355BlQnOwWfldKsNO61Pbx3iG-gw,1423
numpy/array_api/_utility_functions.py,sha256=LjVxM__wjulID49MF5jeLkCDQuRB50KDUPdvHgAtnmg,861
numpy/array_api/linalg.py,sha256=YXo1XUOKitVgbfrKDLY9UHQugEawMhvWGyWrobjE5nk,18877
numpy/array_api/setup.py,sha256=MrEzBh9m4-EV4iekcvUESVM3MW0bHJ5VvXaaTzMFZOU,353
numpy/array_api/tests/__init__.py,sha256=afUKDkt_1ajE1TzYGn4cTp-jMMBfx8riogjk3AePPf0,289
numpy/array_api/tests/__pycache__/__init__.cpython-311.pyc,,
numpy/array_api/tests/__pycache__/test_array_object.cpython-311.pyc,,
numpy/array_api/tests/__pycache__/test_creation_functions.cpython-311.pyc,,
numpy/array_api/tests/__pycache__/test_data_type_functions.cpython-311.pyc,,
numpy/array_api/tests/__pycache__/test_elementwise_functions.cpython-311.pyc,,
numpy/array_api/tests/__pycache__/test_indexing_functions.cpython-311.pyc,,
numpy/array_api/tests/__pycache__/test_manipulation_functions.cpython-311.pyc,,
numpy/array_api/tests/__pycache__/test_set_functions.cpython-311.pyc,,
numpy/array_api/tests/__pycache__/test_sorting_functions.cpython-311.pyc,,
numpy/array_api/tests/__pycache__/test_validation.cpython-311.pyc,,
numpy/array_api/tests/test_array_object.py,sha256=BykbxQ4o_T53wiWfs8_aXiMY4gw5pEfoAAk_H77G8MA,17430
numpy/array_api/tests/test_creation_functions.py,sha256=0AuzJkFyCLw102ilOCE9lt6q24ERhYVJqqdUKfIhjoQ,5165
numpy/array_api/tests/test_data_type_functions.py,sha256=nnA7cI78gM9x4qu6rT3034SUz0qtyHE_blNXOIxiBuY,1049
numpy/array_api/tests/test_elementwise_functions.py,sha256=sl0hYnfoVgYwvG6ufdHuUhx59HbkrnSyNV4tDbR4vW0,3914
numpy/array_api/tests/test_indexing_functions.py,sha256=53y101VXu7vpMAkVZKbmWi2xifWlGZODnsgSNHNRYdQ,651
numpy/array_api/tests/test_manipulation_functions.py,sha256=dCTECxMlufSKrDjYyGtU6f2axWlX7ZcWy5vtiiYR3D8,1124
numpy/array_api/tests/test_set_functions.py,sha256=EDUenQPYY47kxAthUP36Vbm0Bi3cgUt2v1c_OiK5Dfg,565
numpy/array_api/tests/test_sorting_functions.py,sha256=_5tZrT-vhYGHDFQCJEj3VWM1CQ0lwbZdW4FVvGz4R_A,625
numpy/array_api/tests/test_validation.py,sha256=UkU6SXeUCkgTuL0GK1NmsK_BVOt6SNeDzUlKbWdr63o,703
numpy/compat/__init__.py,sha256=zFMERaDM8_4HxKfmphI_yKx0vlAwEHYJ3D9G1B2yHzY,467
numpy/compat/__pycache__/__init__.cpython-311.pyc,,
numpy/compat/__pycache__/py3k.cpython-311.pyc,,
numpy/compat/__pycache__/setup.cpython-311.pyc,,
numpy/compat/py3k.py,sha256=w5IMIIE6YlP3sQzhe-rCPC92moA6gsezl37nHq_Aq1E,3978
numpy/compat/setup.py,sha256=PmRas58NGR72H-7OsQj6kElSUeQHjN75qVh5jlQIJmc,345
numpy/compat/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/compat/tests/__pycache__/__init__.cpython-311.pyc,,
numpy/compat/tests/__pycache__/test_compat.cpython-311.pyc,,
numpy/compat/tests/test_compat.py,sha256=MQ7TFJFcnFc4tEu8Q2PvITkGeUfH-BExtYf4CuqQSHk,601
numpy/conftest.py,sha256=ym3IXm5i3YR3-lsstzWjbw4uHWTbvvAcivABmdvtwg4,4761
numpy/core/__init__.py,sha256=0qTZCHl3SEo0ZkftqEJspdSkmrahRXZrAkMZa5eUZIs,5960
numpy/core/__init__.pyi,sha256=nBhfHv0Vy8Cc37vQn3joQShGEvBy4fyo5aBahLSj5Xo,128
numpy/core/__pycache__/__init__.cpython-311.pyc,,
numpy/core/__pycache__/_add_newdocs.cpython-311.pyc,,
numpy/core/__pycache__/_add_newdocs_scalars.cpython-311.pyc,,
numpy/core/__pycache__/_asarray.cpython-311.pyc,,
numpy/core/__pycache__/_dtype.cpython-311.pyc,,
numpy/core/__pycache__/_dtype_ctypes.cpython-311.pyc,,
numpy/core/__pycache__/_exceptions.cpython-311.pyc,,
numpy/core/__pycache__/_internal.cpython-311.pyc,,
numpy/core/__pycache__/_machar.cpython-311.pyc,,
numpy/core/__pycache__/_methods.cpython-311.pyc,,
numpy/core/__pycache__/_string_helpers.cpython-311.pyc,,
numpy/core/__pycache__/_type_aliases.cpython-311.pyc,,
numpy/core/__pycache__/_ufunc_config.cpython-311.pyc,,
numpy/core/__pycache__/arrayprint.cpython-311.pyc,,
numpy/core/__pycache__/cversions.cpython-311.pyc,,
numpy/core/__pycache__/defchararray.cpython-311.pyc,,
numpy/core/__pycache__/einsumfunc.cpython-311.pyc,,
numpy/core/__pycache__/fromnumeric.cpython-311.pyc,,
numpy/core/__pycache__/function_base.cpython-311.pyc,,
numpy/core/__pycache__/getlimits.cpython-311.pyc,,
numpy/core/__pycache__/memmap.cpython-311.pyc,,
numpy/core/__pycache__/multiarray.cpython-311.pyc,,
numpy/core/__pycache__/numeric.cpython-311.pyc,,
numpy/core/__pycache__/numerictypes.cpython-311.pyc,,
numpy/core/__pycache__/overrides.cpython-311.pyc,,
numpy/core/__pycache__/records.cpython-311.pyc,,
numpy/core/__pycache__/shape_base.cpython-311.pyc,,
numpy/core/__pycache__/umath.cpython-311.pyc,,
numpy/core/__pycache__/umath_tests.cpython-311.pyc,,
numpy/core/_add_newdocs.py,sha256=grJ0KwcVxy4_IFMf8hdTys1ddlSjudK-11w-6qB_I3c,216052
numpy/core/_add_newdocs_scalars.py,sha256=J0eaNt6c-lrqSd6DNVeZm8AxK9GQ8agc_uCYB2sgY9w,12478
numpy/core/_asarray.py,sha256=Z6NX9yGZL83bw0mjNq4oNjWPfucdTUryF7mJvIVU6gY,4018
numpy/core/_asarray.pyi,sha256=9YvYuQKeKDYbI9nNDMezHEDaYsyildJ7WRx4A89qlQc,1128
numpy/core/_dtype.py,sha256=S7EVlBdxSclRcx7dB2wtElJuhuXyoYTD8wV9JNVa-KQ,10975
numpy/core/_dtype_ctypes.py,sha256=O8tYBqU1QzCG1CXviBe6jrgHYnyIPqpci9GEy9lXO08,3790
numpy/core/_exceptions.py,sha256=35d-to48ERMggcjK60hKzHYhZJUUAxWY1GcJWh9bPJE,5551
numpy/core/_internal.py,sha256=bJAzOrDPgwojO-14tjasgoHmzX5Sz_OvxIXUsTMVHIg,29283
numpy/core/_internal.pyi,sha256=HBjDwzplwD0lAEzYt4Xl4y2O_RjBLP1jcuT9XudVPLU,1062
numpy/core/_machar.py,sha256=vKrD1ynuKjmXqio0BAj3MeDmKY_3jwEGw0CsBsMJrfI,11921
numpy/core/_methods.py,sha256=jyN2d7fpRFpgA8WmDrsdVQH4VmeCQnt01CMIzKCWQ4A,8847
numpy/core/_multiarray_tests.cp311-win_amd64.lib,sha256=Ox9ejZ4BOAow1kO6hl8WOtPTmaXF75rRQ5OZtSGAtXQ,2418
numpy/core/_multiarray_tests.cp311-win_amd64.pyd,sha256=wx0avmNekAbKqf7domDdTk_box-9zIrAlpqwOWoMbE4,65024
numpy/core/_multiarray_umath.cp311-win_amd64.lib,sha256=WS57Tw7ua2z3A8UGYnBAEwGaQTQIEifHh4M-blbSV4M,2192
numpy/core/_multiarray_umath.cp311-win_amd64.pyd,sha256=x22BL6UTH-Ici_n_vZEPJ9-AhW-RD6YWmPI_YM_Z0T4,2836480
numpy/core/_operand_flag_tests.cp311-win_amd64.lib,sha256=719JqYffHw-qLhAnM12a5EV6PtZx4NRdW-VZecEHsks,2228
numpy/core/_operand_flag_tests.cp311-win_amd64.pyd,sha256=PJNEwJfpiUxCn2STVve1gGNG4dwyCjP5GLEsDi3enUQ,11264
numpy/core/_rational_tests.cp311-win_amd64.lib,sha256=oxbo43w1Lw7UgPBSRWArRqwkz-rs6ZIww3b8vjb-y9I,2156
numpy/core/_rational_tests.cp311-win_amd64.pyd,sha256=5qhe1qB6wIdewMR6UmkBvvmbCK6PUj5iAGOtHnOqAn4,40448
numpy/core/_simd.cp311-win_amd64.lib,sha256=3c8d9_Fu77ShpQENN3hY96lk5oNKpWCrDcWompVcdGQ,1976
numpy/core/_simd.cp311-win_amd64.pyd,sha256=DupwO1MVgCG9v5_roccSxYSEq9VFtds3zzmMyl46Dlg,2237952
numpy/core/_string_helpers.py,sha256=FGqm6GbAnNlGuQC8qOwNZaTUBVOs326-mFjiEoabqIw,2952
numpy/core/_struct_ufunc_tests.cp311-win_amd64.lib,sha256=0KGD1Jk5mleCmlqBD-PA57V2z3xph4lLkqGhmNxP9Qw,2228
numpy/core/_struct_ufunc_tests.cp311-win_amd64.pyd,sha256=WbSxIRYQLuBspyAj5RIQ9DEWzFGmkJwvFAL5fLpaRRo,13824
numpy/core/_type_aliases.py,sha256=8-qoE3t_63JD5GzGTCCGGBnIGYi7n_7Pc6niKnEedeo,7779
numpy/core/_type_aliases.pyi,sha256=8H9nh_yem7tnmQo9l08NDH0jNXKgRcXZVQ_JDwgtGQk,417
numpy/core/_ufunc_config.py,sha256=cbBciGpxfXQh2rkRB1jEcn20NsmHeaG93FhprMqztXA,14410
numpy/core/_ufunc_config.pyi,sha256=KgouzlKtzHKm3UquSj9kbLI9WAQqpL3bFMmAW6-V4yw,1103
numpy/core/_umath_tests.cp311-win_amd64.lib,sha256=x15dh7InsDqmHxF5AuoQnNk-6dnEeHQDuc0mmuO0PzQ,2104
numpy/core/_umath_tests.cp311-win_amd64.pyd,sha256=DyL_bxlywD_IPBNlICtXfe5FW-XYk6AnF5x2erNGpmY,32768
numpy/core/arrayprint.py,sha256=vj_-rz6R8WHqvwgZ5v9QJsExIOdv0w-A8uAC35IccKE,65333
numpy/core/arrayprint.pyi,sha256=8fKy1vuqDh5XbDUg9824pp1QKKf2fIIedlzwtU2DCko,4570
numpy/core/cversions.py,sha256=FISv1d4R917Bi5xJjKKy8Lo6AlFkV00WvSoB7l3acA4,360
numpy/core/defchararray.py,sha256=eei9dmZizVozCDDn6guOQEu7AjU53qVMFCl9Sj5FE20,76531
numpy/core/defchararray.pyi,sha256=e9Xi4m3OgIml9TftRkpSM6uctdLaNdEbqnZtkF8kSJ0,9637
numpy/core/einsumfunc.py,sha256=T8QSe2sVuZMBB56zD5NIdfEirq5xpdPwvILAQCOHdpo,53311
numpy/core/einsumfunc.pyi,sha256=zqdIuu2PegI6S1za6HhOZOXqH5PgKpuJWzCtTeixwvw,5047
numpy/core/fromnumeric.py,sha256=3EOXhoHiXxAdjFnf99ZxYZXMee9t4yCHmDG36UdqBY4,132741
numpy/core/fromnumeric.pyi,sha256=hVLW4hcNCdKJCiSjaN0wZ9bsPQ4PFvWLvmIys0Q7z1o,24786
numpy/core/function_base.py,sha256=pb0_W5pcNoBj_LLOV-2zYmJ-HRkWw9MvuC5r5vmkawo,20387
numpy/core/function_base.pyi,sha256=iqCNWDs25nUsCXqt2NBwxNqewlEJYfFiIraqBJXv1h4,4912
numpy/core/getlimits.py,sha256=__O9keUFS99cpxKuWMsoMIYoV6VXmFG4FB56RxD9cnc,26600
numpy/core/getlimits.pyi,sha256=rRrU4RZYBrsczGZq6_VRCBNwUKyrqPAHARXXLQuw950,88
numpy/core/include/numpy/__multiarray_api.c,sha256=gqbOxC3yUntTKDrv8V2OpxQZpU3u0iQi3akYm7ntDVE,12430
numpy/core/include/numpy/__multiarray_api.h,sha256=rmdhrSi4KTEUL-WHdF6hGH6A02cZJ7MjD-onGvXaBxg,63016
numpy/core/include/numpy/__ufunc_api.c,sha256=Sk5W_UjMsqTTGbBN84odhCpQgGVFX0DdAW11Kgf52ck,1764
numpy/core/include/numpy/__ufunc_api.h,sha256=aO2C2u_Xvuzj5cqiHYbBHLo8vGV_-CHxYkS1GAR942o,12770
numpy/core/include/numpy/_dtype_api.h,sha256=oOEdQyiAFnJ43Itd4krms72N1Er5aGQnycR_TCxSFcY,17105
numpy/core/include/numpy/_neighborhood_iterator_imp.h,sha256=s5TK2aPpClbw4CbVJCij__hzoh5IgHIIZK0k6FKtqfc,1947
numpy/core/include/numpy/_numpyconfig.h,sha256=ZbBe_ytFqNsgL5Cuc76Im0Q5nMNhG98w-9KEQ5tIN-g,825
numpy/core/include/numpy/arrayobject.h,sha256=f1YdhtzB7wAHDQwmClaFwOl3U-cxkm2UJqzpfQNyhOs,294
numpy/core/include/numpy/arrayscalars.h,sha256=qh5OcUEsasnFnLMRzopqzDN4s-FkWjtFSVtyRxqKVlU,4130
numpy/core/include/numpy/experimental_dtype_api.h,sha256=Vk5lAjTm-j17kb6edPmfNl-hjlKh3Lu31QUwsVEca0k,15897
numpy/core/include/numpy/halffloat.h,sha256=qYgX5iQfNzXICsnd0MCRq5ELhhfFjlRGm1xXGimQm44,2029
numpy/core/include/numpy/ndarrayobject.h,sha256=rAWNEuB2J1l1Y9cq7_z1a0hBqRLVN01onbv3s7ja65k,10434
numpy/core/include/numpy/ndarraytypes.h,sha256=6WIoCjdnBOk3O-opqfq-HConS3c-FHAFFCxduN4gLzs,69954
numpy/core/include/numpy/noprefix.h,sha256=QYUdrHLYEaooNNXnazQYo5WpqPxHedEVXlOAGP7oECo,7041
numpy/core/include/numpy/npy_1_7_deprecated_api.h,sha256=dX_2cI125SpW88dJEtryt-wO4pHxzKR4CcODfjFwtms,4451
numpy/core/include/numpy/npy_3kcompat.h,sha256=46fbR77JAL6H7fHA-YAmDyq3GOGP9QVsM0xWz2s5Wwk,16478
numpy/core/include/numpy/npy_common.h,sha256=23Rfn5aoAX8qjp5h-vGiKSLHnI5cGfUscUeqPOqDMdQ,38832
numpy/core/include/numpy/npy_cpu.h,sha256=6CVIqBgYWa75CDXif9WrKbsrz0Rw1P5_SqH8ltgerLA,4758
numpy/core/include/numpy/npy_endian.h,sha256=G3x4fuvRgY6_Y0AWiJaQ5ZbtmMRRt_QUnYCwkqrHhPE,2863
numpy/core/include/numpy/npy_interrupt.h,sha256=9KhrOhicl4TW3pP6wugWNz5pDDvp9SroisKE04Bbaew,2004
numpy/core/include/numpy/npy_math.h,sha256=CxoF-KkSlpEw-NvYkDm7XPvKYocZsvubTxPcZdHADvg,19508
numpy/core/include/numpy/npy_no_deprecated_api.h,sha256=jIcjEP2AbovDTfgE-qtvdP51_dVGjVnEGBX86rlGSKE,698
numpy/core/include/numpy/npy_os.h,sha256=j044vd1C1oCcW52r3htiVNhUaJSEqCjKrODwMHq3TU0,1298
numpy/core/include/numpy/numpyconfig.h,sha256=7t2kGZNPaYoCoSuqHBh-ugudCpoQ52Dft-O8J5n3uu4,5446
numpy/core/include/numpy/old_defines.h,sha256=jGkDx_FahMvHMTuoWFvx5g5bCV8btUj9pgYNoo_PtwA,6592
numpy/core/include/numpy/random/LICENSE.txt,sha256=1UR2FVi1EIZsIffootVxb8p24LmBF-O2uGMU23JE0VA,1039
numpy/core/include/numpy/random/bitgen.h,sha256=_H0uXqmnub4PxnJWdMWaNqfpyFDu2KB0skf2wc5vjUc,508
numpy/core/include/numpy/random/distributions.h,sha256=GLURa3sFESZE0_0RK-3Gqmfa96itBHw8LlsNyy9EPt4,10070
numpy/core/include/numpy/random/libdivide.h,sha256=F9PLx6TcOk-sd0dObe0nWLyz4HhbHv2K7voR_kolpGU,82217
numpy/core/include/numpy/ufuncobject.h,sha256=JqVIVKsAqJ1stNWKivPTXut3N_Q-C9NpuvjffMVJ3lU,12429
numpy/core/include/numpy/utils.h,sha256=vzJAbatJYfxHmX2yL_xBirmB4mEGLOhJ92JlV9s8yPs,1222
numpy/core/lib/npy-pkg-config/mlib.ini,sha256=hYWFyoBxE036dh19si8UPka01H2cv64qlc4ZtgoA_7A,156
numpy/core/lib/npy-pkg-config/npymath.ini,sha256=5dwvhvbX3a_9toniEDvGPDGChbXIfFiLa36H4YOR-vw,380
numpy/core/lib/npymath.lib,sha256=B1ei1oDfB3VG-YnW9LIE1Ju9RU2sBdMWqVCGyExpSUg,149674
numpy/core/memmap.py,sha256=8ja5vC2MflVu9TP8Omys7bVJs7c6ZuciPGm4DWmmIYs,12109
numpy/core/memmap.pyi,sha256=pwOLGTEk20Z2Tao7hqxLDqfCHrO10otgozEsUO-bPeo,58
numpy/core/multiarray.py,sha256=9GKp_4S-00G1Woay_2uJdLo8_hjBrhvUSqITCTHgspo,57812
numpy/core/multiarray.pyi,sha256=Dhuf-7II3P7XVJccw5YOfB5y_zGgjH-v8QEdsl-Odhs,25790
numpy/core/numeric.py,sha256=MGj7LuF1ai_iW-1x7KIOdPQlgk3THIy7T35c5V1lO4o,79544
numpy/core/numeric.pyi,sha256=X_60pvo7HWT_3llp2CzCnHE7SCEimfgn6PMYppn62BU,14975
numpy/core/numerictypes.py,sha256=qtXdIPDpsEC4S5ZWolESQNymYJqet6vPs1jYItj6jfE,18787
numpy/core/numerictypes.pyi,sha256=klZ11TDhsyd3pnEqdg_yIyp4jYmNKUCe-vbVS5fnpKc,3423
numpy/core/overrides.py,sha256=ZP67G_BJBx94lKoLK2TGkuzSpKMekLjoGqSmA6XGg-k,7274
numpy/core/records.py,sha256=HXrvv-b53OCgy4wj0UhGATNSr45x0u53sNYALuz6jlE,38632
numpy/core/records.pyi,sha256=6qSYt2FKZytTswMFqcTrrqVi4HEbLXh0_gB2Graq78o,5926
numpy/core/shape_base.py,sha256=VUnHZIoZ-14oQ28_4Im_cWKRo-HrK8AM2M5r8HoPz54,30666
numpy/core/shape_base.pyi,sha256=mR9_E24Qll2NT_80QX2TiRkSozB_y6ZyvcNwSyi5-QQ,2897
numpy/core/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/core/tests/__pycache__/__init__.cpython-311.pyc,,
numpy/core/tests/__pycache__/_locales.cpython-311.pyc,,
numpy/core/tests/__pycache__/test__exceptions.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_abc.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_api.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_argparse.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_array_coercion.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_array_interface.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_arraymethod.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_arrayprint.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_casting_floatingpoint_errors.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_casting_unittests.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_conversion_utils.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_cpu_dispatcher.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_cpu_features.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_custom_dtypes.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_cython.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_datetime.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_defchararray.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_deprecations.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_dlpack.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_dtype.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_einsum.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_errstate.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_extint128.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_function_base.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_getlimits.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_half.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_hashtable.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_indexerrors.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_indexing.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_item_selection.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_limited_api.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_longdouble.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_machar.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_mem_overlap.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_mem_policy.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_memmap.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_multiarray.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_nditer.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_nep50_promotions.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_numeric.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_numerictypes.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_numpy_2_0_compat.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_overrides.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_print.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_protocols.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_records.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_regression.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_scalar_ctors.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_scalar_methods.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_scalarbuffer.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_scalarinherit.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_scalarmath.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_scalarprint.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_shape_base.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_simd.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_simd_module.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_strings.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_ufunc.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_umath.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_umath_accuracy.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_umath_complex.cpython-311.pyc,,
numpy/core/tests/__pycache__/test_unicode.cpython-311.pyc,,
numpy/core/tests/_locales.py,sha256=esGp_wCqPpxFxy3eUF-r_Wk-yjFjrQEwkgSzolRzUr0,2280
numpy/core/tests/data/astype_copy.pkl,sha256=lWSzCcvzRB_wpuRGj92spGIw-rNPFcd9hwJaRVvfWdk,716
numpy/core/tests/data/generate_umath_validation_data.cpp,sha256=cNj5I5nfRJiRhOCVFPcGebVTPwTzjwomrlWeViHn2a0,6012
numpy/core/tests/data/numpy_2_0_array.pkl,sha256=Vh02tdyCypa8Nb4QzdVhnDAiXEO2WQrcwcvOdDDFF5w,718
numpy/core/tests/data/recarray_from_file.fits,sha256=NA0kliz31FlLnYxv3ppzeruONqNYkuEvts5wzXEeIc4,8640
numpy/core/tests/data/umath-validation-set-README.txt,sha256=GfrkmU_wTjpLkOftWDuGayEDdV3RPpN2GRVQX61VgWI,982
numpy/core/tests/data/umath-validation-set-arccos.csv,sha256=8mDga_qwTZoPMm1UHPAqjLfBKTHTW5PT5sTeSQhg8pI,62794
numpy/core/tests/data/umath-validation-set-arccosh.csv,sha256=RN30zA_HBqlPb4UwCfk3gQMYNSopV765CQWnhG2Lx0g,62794
numpy/core/tests/data/umath-validation-set-arcsin.csv,sha256=TMvZI0veNaOHupfGPvS_pTRfX0yH33SoaQWT6Q9Epsc,62768
numpy/core/tests/data/umath-validation-set-arcsinh.csv,sha256=GFRD_4CZTEH47C71CWC6pVSWkJFMgxdii3rJXV3RAkw,61718
numpy/core/tests/data/umath-validation-set-arctan.csv,sha256=EFyJjE5dr5VBPLKlFf_7ZVI_s8Wx7FswdHEzs1mpYr8,61734
numpy/core/tests/data/umath-validation-set-arctanh.csv,sha256=0_cOGarj-biMitr6L1ZsBafWfDpecSOf-pk96wVOpIA,62768
numpy/core/tests/data/umath-validation-set-cbrt.csv,sha256=FFi_XxEnGrfJd7OxtjVFT6WFC2tUqKhVV8fmQfb0z8o,62275
numpy/core/tests/data/umath-validation-set-cos.csv,sha256=ccDri5_jQ84D_kAmSwZ_ztNUPIhzhgycDtNsPB7m8dc,60497
numpy/core/tests/data/umath-validation-set-cosh.csv,sha256=FoMRNGCkmjaAspkoZ6EOTRkcUCUxnWdcq2NHfMyaPXg,62298
numpy/core/tests/data/umath-validation-set-exp.csv,sha256=q7AFuKS3D9HRm01wby5b0aZhbBF-eFmniO-NIyuHEpo,17903
numpy/core/tests/data/umath-validation-set-exp2.csv,sha256=ruPfs9R5l8NU43eP3HSiJYMzJMQkD0W5XwpkFRcVZNI,60053
numpy/core/tests/data/umath-validation-set-expm1.csv,sha256=onF_9BcC7wV_dkSRgazWVe9WaEOijyoBGUYSmaEc7OM,61728
numpy/core/tests/data/umath-validation-set-log.csv,sha256=vp1nbu--u7rV8dg9bDLUteLOfZBe5s4Uwyhll15g4AY,11963
numpy/core/tests/data/umath-validation-set-log10.csv,sha256=Gy6aRCYcWMBxTLIOLY9_zWymevNOGlc8cy5fjo1NnCg,70551
numpy/core/tests/data/umath-validation-set-log1p.csv,sha256=5hnT1xXhP9lCmLx_qZ3FMFrujTKdJS-5SZCsKX3yke0,61732
numpy/core/tests/data/umath-validation-set-log2.csv,sha256=ihQyfW16BQYldFbg2v7HErkm1efgGuops7tv7pwVCPI,70546
numpy/core/tests/data/umath-validation-set-sin.csv,sha256=GvPrQUEYMX1iB2zjbfK26JUJOxtqbfiRUgXuAO1QcP0,59981
numpy/core/tests/data/umath-validation-set-sinh.csv,sha256=RI_UkXTgvcO2VAbFFZZSmR--RQbxSdQePc8gnfkLICE,61722
numpy/core/tests/data/umath-validation-set-tan.csv,sha256=H2Z3jO3nV6u0rXYPes3MnI4OSdaKcStfjjKoMiKsyFQ,61728
numpy/core/tests/data/umath-validation-set-tanh.csv,sha256=xSY5fgfeBXN6fal4XDed-VUcgFIy9qKOosa7vQ5v1-U,61728
numpy/core/tests/examples/cython/__pycache__/setup.cpython-311.pyc,,
numpy/core/tests/examples/cython/checks.pyx,sha256=s6gk8EKu_2IqvCamRf0gaiJUQ0TJlkfoyB30NOGIj0M,697
numpy/core/tests/examples/cython/meson.build,sha256=Y4SSfbNmUcOwwErU09k8q2UVSbh2-cqfEw-9kgcFBNo,1124
numpy/core/tests/examples/cython/setup.py,sha256=JvbvFHHdkZx_eajlwMTtqHijLD-TZwXCZlRySAe-y94,521
numpy/core/tests/examples/limited_api/__pycache__/setup.cpython-311.pyc,,
numpy/core/tests/examples/limited_api/limited_api.c,sha256=znFZtXQ0y9tOcovvZSjq9GrxBfx3st_s67Fru4owwUg,361
numpy/core/tests/examples/limited_api/setup.py,sha256=N7kqsVp4iIE20IebigEJUW3nW2F0l6Vthb5qNvKHBmM,457
numpy/core/tests/test__exceptions.py,sha256=PmFZh81gSxCUKkNs2wDaMICLM5uGp6lI92OynAd-KtM,2934
numpy/core/tests/test_abc.py,sha256=K04DCye4i4M5rAdoHxsaGHSKDdP4CyFRQuxP1FeE4OY,2274
numpy/core/tests/test_api.py,sha256=q0hlOZCU_oogNtF4MFH_pEsq9xgLUTU8K9tinqgMSoc,23610
numpy/core/tests/test_argparse.py,sha256=OH9o4gBzveTd3iqVDpkUJ5JODgBXOvZanaVwyghADMI,2031
numpy/core/tests/test_array_coercion.py,sha256=JTBYrJrCtnQ87K73hFi-BGTqIZV4yR4lJ3YJR7iJLp0,35277
numpy/core/tests/test_array_interface.py,sha256=SSrpXTRCu8TC-Ei3Dk3gfHG0yua22-fjkmJczpMnAI0,7993
numpy/core/tests/test_arraymethod.py,sha256=Yl-1mmL3sDyJdGWLgm_EiCguw18nbYjeUu_8sik0O4s,3329
numpy/core/tests/test_arrayprint.py,sha256=vV9S3-0EvAmkfm1LUUB4RC0Gq0jDhx7L9Mm6LG-GLRY,41509
numpy/core/tests/test_casting_floatingpoint_errors.py,sha256=qC5FimCIGwxklVy5VeD9WNIBD2EZBos4JdiOy0e6H5I,5217
numpy/core/tests/test_casting_unittests.py,sha256=T6PCbjLs-75z0TSC2KDjkzZf_zXxizHPlvs577x50Hs,35117
numpy/core/tests/test_conversion_utils.py,sha256=4PJfmBFJ0N-K0Xqf1SBRxryyP0bfu5UF_p4el1YcPI4,6767
numpy/core/tests/test_cpu_dispatcher.py,sha256=9fMimw7aURqqt1cEgMIEn_y0kuyC34fSCy-MfSEI70o,1585
numpy/core/tests/test_cpu_features.py,sha256=qcel4Zl2AZIqM8qBRx-WKeTczanc4mUBK70PDtbp6cw,15296
numpy/core/tests/test_custom_dtypes.py,sha256=jEKD6jt0myQJ-6I-mzkCtQUZE-SHcCxdV5xxUrDZvtY,9654
numpy/core/tests/test_cython.py,sha256=s_oHWhdL2UglTWe6s4305oIcGM1ImBGv0nspKRsakBs,3890
numpy/core/tests/test_datetime.py,sha256=fmIhYl_Nm9SrD5HNyEPO0PLM5QYRyaYO_VkZXzzlX18,118780
numpy/core/tests/test_defchararray.py,sha256=VD11wN-zun5l6ZkX2Eygvt1MRfqMeI1MIJJTFdd3NwQ,25683
numpy/core/tests/test_deprecations.py,sha256=DSMN6-frWWdnWSMDdLLWntRI1GhqtjwkAJphlBudHCk,31893
numpy/core/tests/test_dlpack.py,sha256=IKz5g2yVx6bq9whiHCyXCUJxAO_VkjNOQVtWgQD1Aww,3646
numpy/core/tests/test_dtype.py,sha256=4-GeipDf7ncuKwqgcYy2THgvPCjH5vSZsmbsC_R4HQs,77608
numpy/core/tests/test_einsum.py,sha256=63Oz2r4R4X6mrXGATK36QiTD4BMtsI8wXnKgRag9T0g,54960
numpy/core/tests/test_errstate.py,sha256=z5nadQobXL6skDWH_7cY_0FkCtG5-p3tRPE62wfwouo,2280
numpy/core/tests/test_extint128.py,sha256=b8vP_hDPltdHoxWBn2vAmOgjJe2NVW_vjnatdCOtxu8,5862
numpy/core/tests/test_function_base.py,sha256=IXpiIS_lev-6-GdKa3cokptdlerISZth6Oj6zEfkx70,16041
numpy/core/tests/test_getlimits.py,sha256=DG6ZR8JV0cLkuO5iq6-Wj5Et-6CAxn6GqHCmJplLTNw,6912
numpy/core/tests/test_half.py,sha256=Y31VvCfWPLDZU8yy3Riy9ocWAMQF2tjb3QGSAyy1gW0,25165
numpy/core/tests/test_hashtable.py,sha256=6RRLuIzT9HAAJIOpLQijbv0PTdOEyWzZJ0mTH8lhJqs,1041
numpy/core/tests/test_indexerrors.py,sha256=iJu4EorQks1MmiwTV-fda-vd4HfCEwv9_Ba_rVea7xw,5263
numpy/core/tests/test_indexing.py,sha256=h-MSMHH4vj9VoaTusgCTmol1mH2xjix1K_gSgfOBdF0,55731
numpy/core/tests/test_item_selection.py,sha256=zaGuMcTDsbCpQO1k9c9xuc4jUWhbArfn_1INfilf9hk,6623
numpy/core/tests/test_limited_api.py,sha256=Op5yY8Mi9UzowMQKyXEAS37IL6tcztHMoNKUZPaFnMQ,1216
numpy/core/tests/test_longdouble.py,sha256=c6Ta_MZglggYerglxWkilq6LU3_XqCI7QNEfWVu-P_M,14300
numpy/core/tests/test_machar.py,sha256=pauEGVLnT_k8tD0Meu_s_uxutPvSwpOPywlFoizehMg,1097
numpy/core/tests/test_mem_overlap.py,sha256=rwK-0ztRVmnZU1gpH1Jhawo6WNL8-iy3aG5MUFm9uxs,30017
numpy/core/tests/test_mem_policy.py,sha256=qxyKJ1jfChed2uEiQGrk15iw1suXGu208zRuWpTyo6E,17244
numpy/core/tests/test_memmap.py,sha256=_Hl2YntpQzFa2iKXV11IiXb7SSi6_nveoTXaTrZXdGs,7692
numpy/core/tests/test_multiarray.py,sha256=1t29z9zzDGs_Y42S8hX2zbe9MqIUlA92qRNAJrumxQo,390160
numpy/core/tests/test_nditer.py,sha256=9PzIF2itjlo6X5JFnAW2Jzldb6FiDGSLDdYw_mcMfIA,134166
numpy/core/tests/test_nep50_promotions.py,sha256=2Buz0v5C4CZXMeYUbrs2O44Us2S9tmQ6OYQJiTJAGWE,9086
numpy/core/tests/test_numeric.py,sha256=dho27k6ZhimOuiqAGWp6eEVv60s7ga6yp6ojvuhc9K0,140887
numpy/core/tests/test_numerictypes.py,sha256=WGOdaW1eKvR1ou3WBnw5VBqkEpCV1vp6GxfQOah0o0Y,22257
numpy/core/tests/test_numpy_2_0_compat.py,sha256=0RzFRK2594uzFQKplmJO5uMsRDU5vrCUD4_A7NwAc28,1678
numpy/core/tests/test_overrides.py,sha256=DNx_pFclkTABEEQ4auQXlFQX8bSzHQN4GZeZsxfMwhQ,26839
numpy/core/tests/test_print.py,sha256=w6E6ba8HlrkteyZf6g5wuLiI7g348bq3JXJkzJ_nhp0,7039
numpy/core/tests/test_protocols.py,sha256=Etu0M6T-xFJjAyXy2EcCH48Tbe5VRnZCySjMy0RhbPY,1212
numpy/core/tests/test_records.py,sha256=xYbtYTeGctOKfU49aEkfx47IMPuyh_YJ-UGU0PqhBr8,20789
numpy/core/tests/test_regression.py,sha256=3iGbAsQuOfocyapvWL8zimwP3z42qqwheT1X8K0Hnhg,94163
numpy/core/tests/test_scalar_ctors.py,sha256=8bqWTbtclndxzw-FTkcn3nVJkR7yB1XKCSdp4c9OETs,6301
numpy/core/tests/test_scalar_methods.py,sha256=wwFo-vFznM1bWV-FhSA3c6rlSlsrPdlD2Dmv6JBSV3g,7745
numpy/core/tests/test_scalarbuffer.py,sha256=6LVnSr4moR2L5a4awXE_6U_-qNNkMOCONK1mlApOK24,5733
numpy/core/tests/test_scalarinherit.py,sha256=xXhqy8Dt7qhq3qC5MdVTj4VvSj0YQKPOBiLB0tR8Nl8,2466
numpy/core/tests/test_scalarmath.py,sha256=IGtOMvYZAsey2TqkhWn7CsgW5NndV0f9M2WTwZYEH48,44347
numpy/core/tests/test_scalarprint.py,sha256=qcJz1IeM80XPorjzwR1daYwyVLh4WMAaEDfuBnXYoe4,19153
numpy/core/tests/test_shape_base.py,sha256=22FQE3-udL8qFKsdGzK54vcwGBgu9arUA61WePiFnH0,30548
numpy/core/tests/test_simd.py,sha256=R5hHQNBZXgDqk-Rq7nUAm_3VKTydA9Fnw94g5kHIs2M,50029
numpy/core/tests/test_simd_module.py,sha256=fZ-y4u2fvMMtHISEog8sU6ZmaCZ4GhiqJK3xqAh3Y2U,4003
numpy/core/tests/test_strings.py,sha256=LMYNdCJOvhSAIVqnz8iD-L7PskpmHSSx46yHnx4hpNw,3934
numpy/core/tests/test_ufunc.py,sha256=4XZlHQs2mdsj4IfyiTYQuHMLMKFzpanwu1BbdobaQyo,127903
numpy/core/tests/test_umath.py,sha256=2sakgS17ZE39ovndyJdIEjqeAwjD5jpmDl1ncnlBarY,191225
numpy/core/tests/test_umath_accuracy.py,sha256=K18zKG1jqi1EWf89jN9NR9-GCzoN1VHBgA5CtHIMkdo,3972
numpy/core/tests/test_umath_complex.py,sha256=J-LHDr8fhGGU9LjU02e7kondo_Qq3XtJvIfUpFefoIE,23869
numpy/core/tests/test_unicode.py,sha256=VegPA3vlYiaA0IVc6aFI6lJ-p_vZkX0A5AiglQe2NU0,13143
numpy/core/umath.py,sha256=IE9whDRUf3FOx3hdo6bGB0X_4OOJn_Wk6ajnbrc542A,2076
numpy/core/umath_tests.py,sha256=IuFDModusxI6j5Qk-VWYHRZDIE806dzvju0qYlvwmfY,402
numpy/ctypeslib.py,sha256=nW4WZdd0_JjA4Z0u5b4bxq1YQ1LzGjsv7zc9jsVGWHc,17792
numpy/ctypeslib.pyi,sha256=FVn5H088-hedo0cWBJZc_a5PtgsTW8_EdrDVhd7e__U,8223
numpy/distutils/__init__.py,sha256=sh1TV9_aW0YWvmHfBPtbZKCRcZTN6BnxKV-mIAG2vuY,2138
numpy/distutils/__init__.pyi,sha256=6KiQIH85pUXaIlow3KW06e1_ZJBocVY6lIGghNaW33A,123
numpy/distutils/__pycache__/__init__.cpython-311.pyc,,
numpy/distutils/__pycache__/_shell_utils.cpython-311.pyc,,
numpy/distutils/__pycache__/armccompiler.cpython-311.pyc,,
numpy/distutils/__pycache__/ccompiler.cpython-311.pyc,,
numpy/distutils/__pycache__/ccompiler_opt.cpython-311.pyc,,
numpy/distutils/__pycache__/conv_template.cpython-311.pyc,,
numpy/distutils/__pycache__/conv_template.cpython-311.pyc,sha256=SFH5kLH_HfGVsJzOgwS-q6QFas7tVhSQm4q3jfcK-ks,14210
numpy/distutils/__pycache__/core.cpython-311.pyc,,
numpy/distutils/__pycache__/cpuinfo.cpython-311.pyc,,
numpy/distutils/__pycache__/exec_command.cpython-311.pyc,,
numpy/distutils/__pycache__/extension.cpython-311.pyc,,
numpy/distutils/__pycache__/from_template.cpython-311.pyc,,
numpy/distutils/__pycache__/fujitsuccompiler.cpython-311.pyc,,
numpy/distutils/__pycache__/intelccompiler.cpython-311.pyc,,
numpy/distutils/__pycache__/lib2def.cpython-311.pyc,,
numpy/distutils/__pycache__/line_endings.cpython-311.pyc,,
numpy/distutils/__pycache__/log.cpython-311.pyc,,
numpy/distutils/__pycache__/mingw32ccompiler.cpython-311.pyc,,
numpy/distutils/__pycache__/misc_util.cpython-311.pyc,,
numpy/distutils/__pycache__/msvc9compiler.cpython-311.pyc,,
numpy/distutils/__pycache__/msvccompiler.cpython-311.pyc,,
numpy/distutils/__pycache__/npy_pkg_config.cpython-311.pyc,,
numpy/distutils/__pycache__/numpy_distribution.cpython-311.pyc,,
numpy/distutils/__pycache__/pathccompiler.cpython-311.pyc,,
numpy/distutils/__pycache__/setup.cpython-311.pyc,,
numpy/distutils/__pycache__/system_info.cpython-311.pyc,,
numpy/distutils/__pycache__/unixccompiler.cpython-311.pyc,,
numpy/distutils/_shell_utils.py,sha256=9pI0lXlRJxB22TPVBNUhWe7EnE-V6xIhMNQSR8LOw40,2704
numpy/distutils/armccompiler.py,sha256=6sKNp543q_4NafErHoFOPKz8R3YJR9soDCr1WeFr5Xk,988
numpy/distutils/ccompiler.py,sha256=UgTgCtW-JRfmIcA1ZgVbyUAocYEkMnFgffEC21Vvt60,29444
numpy/distutils/ccompiler_opt.py,sha256=Nd9S8Ywp88HjhlMYgbg6Rm0AWtNsP-CEHz_UtQveKBE,103058
numpy/distutils/checks/cpu_asimd.c,sha256=Nit4NvYvo3XWtBKeV6rmIszdNLu9AY81sqMFCTkKXBE,845
numpy/distutils/checks/cpu_asimddp.c,sha256=bQP32IzQZANu9aFu3qkovLYJXKCm0bJ6srsO5Ho2GKI,448
numpy/distutils/checks/cpu_asimdfhm.c,sha256=xJjmEakgtmK9zlx2fIT6UZ4eZreLzdCoOVkkGPyzXFA,548
numpy/distutils/checks/cpu_asimdhp.c,sha256=0eTZ2E1Gyk3G5XfkpSN32yI9AC3SUwwFetyAOtEp5u4,394
numpy/distutils/checks/cpu_avx.c,sha256=69aCE28EArV-BmdFKhCA5djgNZAZtQg2zdea3VQD-co,799
numpy/distutils/checks/cpu_avx2.c,sha256=207hFoh4ojzMAPQ53ug_Y5qCFIgZ1e8SdI1-o2jzdB4,769
numpy/distutils/checks/cpu_avx512_clx.c,sha256=CfPjudkRZ9_xygLVOySSEjoAfkjjfu4ipkWK4uCahbU,864
numpy/distutils/checks/cpu_avx512_cnl.c,sha256=eKCPRk6p1B0bPAyOY0oWRKZMfa-c5g-skvJGGlG5I4Y,972
numpy/distutils/checks/cpu_avx512_icl.c,sha256=Zt8XOXZL85Ds5HvZlAwUVilT6mGbPU44Iir44ul6y2Y,1030
numpy/distutils/checks/cpu_avx512_knl.c,sha256=0itGNg9s9gFjsj79qQvsZR-xceTTcpw4qa0OOAmq_Sg,984
numpy/distutils/checks/cpu_avx512_knm.c,sha256=iVdJnZ5HY59XhUv4GzwqYRwz2E_jWJnk1uSz97MvxY0,1162
numpy/distutils/checks/cpu_avx512_skx.c,sha256=aOHpYdGPEx2FcnC7TKe9Nr7wQ0QWW20Uq3xRVSb4U90,1036
numpy/distutils/checks/cpu_avx512_spr.c,sha256=ziSmzNQZ_k3j5FrAWSKfAAW_g3l8tq8t6InVPWEUx9Y,930
numpy/distutils/checks/cpu_avx512cd.c,sha256=zIl7AJXfxqnquZyHQvUAGr9M-vt62TIlylhdlrg-qkE,779
numpy/distutils/checks/cpu_avx512f.c,sha256=ibW0zon6XGYkdfnYETuPfREmE5OtO0HfuLTqXMsoqNA,775
numpy/distutils/checks/cpu_f16c.c,sha256=QxxI3vimUAkJ4eJ83va2mZzTJOk3yROI05fVY07H5To,890
numpy/distutils/checks/cpu_fma3.c,sha256=Cq0F_UpVJ4SYHcxXfaYoqHSYvWRJzZsB8IkOVl8K2ro,839
numpy/distutils/checks/cpu_fma4.c,sha256=Xy0YfVpQDCiFOOrCWH-RMkv7ms5ZAbSauwm2xEOT94o,314
numpy/distutils/checks/cpu_neon.c,sha256=I-R8DHE6JfzqmPpaF4NTdWxq5hEW-lJZPjSjW8ynFgo,619
numpy/distutils/checks/cpu_neon_fp16.c,sha256=6hdykX7cRL3ruejgK3bf_IXGQWol8OUITPEjvbz_1Hc,262
numpy/distutils/checks/cpu_neon_vfpv4.c,sha256=IY4cT03GTrzEZKLd7UInKtYC0DlgugFGGrkSTfwwvmU,630
numpy/distutils/checks/cpu_popcnt.c,sha256=Jkslm5DiuxbI-fBcCIgJjxjidm-Ps_yfAb_jJIZonE8,1081
numpy/distutils/checks/cpu_sse.c,sha256=XitLZu_qxXDINNpbfcUAL7iduT1I63HjNgtyE72SCEo,706
numpy/distutils/checks/cpu_sse2.c,sha256=OJpQzshqCS6Cp9X1I1yqh2ZPa0b2AoSmJn6HdApOzYk,717
numpy/distutils/checks/cpu_sse3.c,sha256=AmZkvTpXcoCAfVckXgvwloutI5CTHkwHJD86pYsntgk,709
numpy/distutils/checks/cpu_sse41.c,sha256=5GvpgxPcDL39iydUjKyS6WczOiXTs14KeXvlWVOr6LQ,695
numpy/distutils/checks/cpu_sse42.c,sha256=8eYzhquuXjRRGp3isTX0cNUV3pXATEPc-J-CDYTgTaU,712
numpy/distutils/checks/cpu_ssse3.c,sha256=QXWKRz5fGQv5bn282bJL4h_92-yqHFG_Gp5uLKvcA34,725
numpy/distutils/checks/cpu_vsx.c,sha256=gxWpdnkMeoaBCzlU_j56brB38KFo4ItFsjyiyo3YrKk,499
numpy/distutils/checks/cpu_vsx2.c,sha256=ycKoKXszrZkECYmonzKd7TgflpZyVc1Xq-gtJqyPKxs,276
numpy/distutils/checks/cpu_vsx3.c,sha256=pNA4w2odwo-mUfSnKnXl5SVY1z2nOxPZZcNC-L2YX1w,263
numpy/distutils/checks/cpu_vsx4.c,sha256=SROYYjVVc8gPlM4ERO--9Dk2MzvAecZzJxGKO_RTvPM,319
numpy/distutils/checks/cpu_vx.c,sha256=v1UZMj78POCN7sbFmW6N0GM_qQSUwHxiF15LQYADIUs,477
numpy/distutils/checks/cpu_vxe.c,sha256=1w8AvS6x8s_zTgcrDEGMKQmSqpJRX2NLprdSu_ibyjk,813
numpy/distutils/checks/cpu_vxe2.c,sha256=fY9P2fWo-b08dy4dmuNNc_xX3E0ruPRU9zLPzzgD-Z8,645
numpy/distutils/checks/cpu_xop.c,sha256=sPhOvyT-mdlbf6RlbZvMrslRwHnTFgP-HXLjueS7nwU,246
numpy/distutils/checks/extra_avx512bw_mask.c,sha256=7IRO24mpcuXRhm3refGWP91sy0e6RmSkmUQCWyxy__0,654
numpy/distutils/checks/extra_avx512dq_mask.c,sha256=jFtOKEtZl3iTpfbmFNB-u4DQNXXBST2toKCpxFIjEa0,520
numpy/distutils/checks/extra_avx512f_reduce.c,sha256=hIcCLMm_aXPfrhzCsoFdQiryIrntPqfDxz0tNOR985w,1636
numpy/distutils/checks/extra_vsx3_half_double.c,sha256=GU-E6yQLdzmOdvO06D0KCkvU4YHyuwFvyydirU_1Clk,366
numpy/distutils/checks/extra_vsx4_mma.c,sha256=-Pz_qQ55WfWmTWGTH0hvKrFTU2S2kjsVBfIK3w5sciE,520
numpy/distutils/checks/extra_vsx_asm.c,sha256=anSZskhKZImNk0lsSJJY_8GJQ0h3dDrkrmrGitlS7Fw,981
numpy/distutils/checks/test_flags.c,sha256=7rgVefVOKOBaefG_6riau_tT2IqI4MFrbSMGNFnqUBQ,17
numpy/distutils/command/__init__.py,sha256=DCxnKqTLrauOD3Fc8b7qg9U3gV2k9SADevE_Q3H78ng,1073
numpy/distutils/command/__pycache__/__init__.cpython-311.pyc,,
numpy/distutils/command/__pycache__/autodist.cpython-311.pyc,,
numpy/distutils/command/__pycache__/bdist_rpm.cpython-311.pyc,,
numpy/distutils/command/__pycache__/build.cpython-311.pyc,,
numpy/distutils/command/__pycache__/build_clib.cpython-311.pyc,,
numpy/distutils/command/__pycache__/build_ext.cpython-311.pyc,,
numpy/distutils/command/__pycache__/build_py.cpython-311.pyc,,
numpy/distutils/command/__pycache__/build_scripts.cpython-311.pyc,,
numpy/distutils/command/__pycache__/build_src.cpython-311.pyc,,
numpy/distutils/command/__pycache__/config.cpython-311.pyc,,
numpy/distutils/command/__pycache__/config_compiler.cpython-311.pyc,,
numpy/distutils/command/__pycache__/develop.cpython-311.pyc,,
numpy/distutils/command/__pycache__/egg_info.cpython-311.pyc,,
numpy/distutils/command/__pycache__/install.cpython-311.pyc,,
numpy/distutils/command/__pycache__/install_clib.cpython-311.pyc,,
numpy/distutils/command/__pycache__/install_data.cpython-311.pyc,,
numpy/distutils/command/__pycache__/install_headers.cpython-311.pyc,,
numpy/distutils/command/__pycache__/sdist.cpython-311.pyc,,
numpy/distutils/command/autodist.py,sha256=i2ip0Zru8_AFx3lNQhlZfj6o_vg-RQ8yu1WNstcIYhE,3866
numpy/distutils/command/bdist_rpm.py,sha256=9uZfOzdHV0_PRUD8exNNwafc0qUqUjHuTDxQcZXLIbg,731
numpy/distutils/command/build.py,sha256=6IbYgycGcCRrrWENUBqzAEhgtUhCGLnXNVnTCu3hxWc,2675
numpy/distutils/command/build_clib.py,sha256=x8CjWbraTjai7wdSwq16VBWMQw5w20BmCj_iHdzDc14,19786
numpy/distutils/command/build_ext.py,sha256=XfbdWZdqQKwqibpb8VT2ODlrcftrigfFVneLl97P3Zk,33735
numpy/distutils/command/build_py.py,sha256=xBHZCtx91GqucanjIBETPeXmR-gyUKPDyr1iMx1ARWE,1175
numpy/distutils/command/build_scripts.py,sha256=AEQLNmO2v5N-GXl4lwd8v_nHlrauBx9Y-UudDcdCs_A,1714
numpy/distutils/command/build_src.py,sha256=njEPAEftbBAQ8K6uARjA1N_CkbCDwlB59p3wue5IfZg,31951
numpy/distutils/command/config.py,sha256=ID-DxagfYScPFQeyISNBqOQZOx875SOOI67tnymPPnY,21186
numpy/distutils/command/config_compiler.py,sha256=I-xAL3JxaGFfpR4lg7g0tDdA_t7zCt-D4JtOACCP_Ak,4495
numpy/distutils/command/develop.py,sha256=5ro-Sudt8l58JpKvH9FauH6vIfYRv2ohHLz-9eHytbc,590
numpy/distutils/command/egg_info.py,sha256=n6trbjRfD1qWc_hRtMFkOJsg82BCiLvdl-NeXyuceGc,946
numpy/distutils/command/install.py,sha256=iK5ls63o6WqVOreU-mG5HZSkx90qYhMQvlo2FaaQWWg,3152
numpy/distutils/command/install_clib.py,sha256=q3yrfJY9EBaxOIYUQoiu2-juNKLKAKKfXC0nrd4t6z0,1439
numpy/distutils/command/install_data.py,sha256=r8EVbIaXyN3aOmRugT3kp_F4Z03PsVX2l_x4RjTOWU4,872
numpy/distutils/command/install_headers.py,sha256=g5Ag2H3j3dz-qSwWegxiZSAnvAf0thYYFwfPVHf9rxc,944
numpy/distutils/command/sdist.py,sha256=XQM39b-MMO08bfE3SJrrtDWwX0XVnzCZqfAoVuuaFuE,760
numpy/distutils/conv_template.py,sha256=hL0DDy7tMJ-5I-63BmkWkoLNX2c5GiQdQhj-XNG3Tm8,9865
numpy/distutils/core.py,sha256=blsz2AX74fLXR741nylRvRabLQ84Fouy0LQMPL7Jjko,8416
numpy/distutils/cpuinfo.py,sha256=l5G7myXNwEOTynBIEitH-ghaF8Zw5pHQAjaYpPKNtTQ,23322
numpy/distutils/exec_command.py,sha256=ZnPon3CxIP1kCznPhTyPnCSOLS7sXAot4TeTPcqVQdw,10598
numpy/distutils/extension.py,sha256=gho-x1rzPK16ca8zakRKHvbZL4Gvp1VFTEToE2-2k4M,3675
numpy/distutils/fcompiler/__init__.py,sha256=UncOSqwlhHdNNSViIibqy51Prrkd589e1C06sTtnYww,41660
numpy/distutils/fcompiler/__pycache__/__init__.cpython-311.pyc,,
numpy/distutils/fcompiler/__pycache__/absoft.cpython-311.pyc,,
numpy/distutils/fcompiler/__pycache__/arm.cpython-311.pyc,,
numpy/distutils/fcompiler/__pycache__/compaq.cpython-311.pyc,,
numpy/distutils/fcompiler/__pycache__/environment.cpython-311.pyc,,
numpy/distutils/fcompiler/__pycache__/fujitsu.cpython-311.pyc,,
numpy/distutils/fcompiler/__pycache__/g95.cpython-311.pyc,,
numpy/distutils/fcompiler/__pycache__/gnu.cpython-311.pyc,,
numpy/distutils/fcompiler/__pycache__/hpux.cpython-311.pyc,,
numpy/distutils/fcompiler/__pycache__/ibm.cpython-311.pyc,,
numpy/distutils/fcompiler/__pycache__/intel.cpython-311.pyc,,
numpy/distutils/fcompiler/__pycache__/lahey.cpython-311.pyc,,
numpy/distutils/fcompiler/__pycache__/mips.cpython-311.pyc,,
numpy/distutils/fcompiler/__pycache__/nag.cpython-311.pyc,,
numpy/distutils/fcompiler/__pycache__/none.cpython-311.pyc,,
numpy/distutils/fcompiler/__pycache__/nv.cpython-311.pyc,,
numpy/distutils/fcompiler/__pycache__/pathf95.cpython-311.pyc,,
numpy/distutils/fcompiler/__pycache__/pg.cpython-311.pyc,,
numpy/distutils/fcompiler/__pycache__/sun.cpython-311.pyc,,
numpy/distutils/fcompiler/__pycache__/vast.cpython-311.pyc,,
numpy/distutils/fcompiler/absoft.py,sha256=J5Nn8PXD0VNUjCI5Vj6PD8JRS6Dxi5Zz5LCa8fkPZIY,5672
numpy/distutils/fcompiler/arm.py,sha256=Bpftt3HnmJc3Iyt8-nwsNv86JqdFYK0JMwh3CC8nP_k,2161
numpy/distutils/fcompiler/compaq.py,sha256=yyReqFAq42dy1zscMAV0GqVaYW7Iao1HtAUpnv5XTec,4023
numpy/distutils/fcompiler/environment.py,sha256=PVS1al3wahDNnneNVSl1sQhMPfz2dUXaIDVJfy0wZBU,3168
numpy/distutils/fcompiler/fujitsu.py,sha256=g4dTLDFfLRAzhYayIwyHGBw1Y36DKtPOCYfA823ldNA,1379
numpy/distutils/fcompiler/g95.py,sha256=1TJe4IynWYqqYBy8gJ-nz8WQ_TaSbv8k2UzUIY5Erqc,1372
numpy/distutils/fcompiler/gnu.py,sha256=6V_Ly_lwEEsfUDSz0vCDg86EhWlajHuyBy_ioLqKCdM,21057
numpy/distutils/fcompiler/hpux.py,sha256=SLbDOPYgiixqE32GgUrAJjpDLFy9g7E01vGNZCGv6Pc,1394
numpy/distutils/fcompiler/ibm.py,sha256=P8NMedMGxlCvVRoVIj4GKF65IP1TUe7jmlt-1KscVYo,3631
numpy/distutils/fcompiler/intel.py,sha256=rlm017cVcyjIy1_s8a4lNHJ8ilo6TiYcIA_tuPojapY,6781
numpy/distutils/fcompiler/lahey.py,sha256=EV3Zhwq-iowWAu4BFBPv_UGJ-IB-qxlxmi6WU1qHDOs,1372
numpy/distutils/fcompiler/mips.py,sha256=mlUNgGrRSLnNhtxQXWVfC9l4_OP2GMvOkgbZQwBon0A,1768
numpy/distutils/fcompiler/nag.py,sha256=FpoDQWW_Y3Anm9-Psml-eNySCGzCp9_jP2Ej4_AwDy8,2864
numpy/distutils/fcompiler/none.py,sha256=auMK2ou1WtJ20LeMbwCZJ3XofpT9A0YYbMVd-62Mi_E,786
numpy/distutils/fcompiler/nv.py,sha256=jRyTlRE57lFJq659Xi-oUIy79nXYucyHawspR_D8c44,1613
numpy/distutils/fcompiler/pathf95.py,sha256=ipbaZIO8sqPJ1lUppOurnboiTwRzIasWNAJvKmktvv4,1094
numpy/distutils/fcompiler/pg.py,sha256=cVcSFM9oR0KmO5AIb4Odw9OGslW6zvDGP88n-uEwxvQ,3696
numpy/distutils/fcompiler/sun.py,sha256=JMdFfKldTYlfW1DxV7nR09k5PZypKLWpP7wmQzmlnH0,1628
numpy/distutils/fcompiler/vast.py,sha256=JUGP68JGOUOBS9WbXftE-qCVUD13fpLyPnhpHfTL5y0,1719
numpy/distutils/from_template.py,sha256=BL-vypfI0GNJrTo-nKs445liTW2Qdfvrsu8RMjATL5A,8174
numpy/distutils/fujitsuccompiler.py,sha256=JWVPhI1oH4v2iKzDP8VjcnJIKYXZFYcYCwdpDxhURvw,862
numpy/distutils/intelccompiler.py,sha256=77BDCj7_6Nnf92ZDeFQgA6aDKJGkzDQ7u0nuQGw1v8g,4345
numpy/distutils/lib2def.py,sha256=KnWZJaOsxmx57MEJxrsdPAlZbQBgu-27bSCjwO8cI6k,3746
numpy/distutils/line_endings.py,sha256=hlI71r840mhfu8lmzdHPVZ4NFm-kJDDUMV3lETblVTY,2109
numpy/distutils/log.py,sha256=a5-sPwcZei7kSP0ZQZH4tTrlRWHnL8jtzLCeUSPA_04,2990
numpy/distutils/mingw/gfortran_vs2003_hack.c,sha256=FDTA53KYTIhil9ytvZlocOqghQVp9LacLHn1IurV0wI,83
numpy/distutils/mingw32ccompiler.py,sha256=7QUElsXFImtGavf50mEx8CItn2EsltALWmQWm1NZNLk,22658
numpy/distutils/misc_util.py,sha256=_7ZB-2pNR94SUB_4_hPSX8MOXNDST9YRFUwvhsn6CcA,91852
numpy/distutils/msvc9compiler.py,sha256=bCtCVJmGrBHPm9sOoxa3oSrdrEVCNQFEM5O5hdqX8Hc,2255
numpy/distutils/msvccompiler.py,sha256=gqQySO-P6Egk3qgrNlyCF3ze_U47lIO9SrbFJrCQCO8,2723
numpy/distutils/npy_pkg_config.py,sha256=q-ASkO8wZ5HmiTEH_6gzO2bPV4i5dz3bTu4EuSxFQJM,13409
numpy/distutils/numpy_distribution.py,sha256=nrdp8rlyjEBBV1tzzi5cE-aYeXB5U3X8T5-G0akXSoY,651
numpy/distutils/pathccompiler.py,sha256=a5CYDXilCaIC85v0fVh-wrb0fClv0A7mPS87aF1inUc,734
numpy/distutils/setup.py,sha256=zd5_kD7uTEOTgC8Fe93g9A_5AOBEySWwr-2OxHsBBEc,651
numpy/distutils/system_info.py,sha256=NJ9YM0C2CtXCmQsRA7OOMjQjoAAmbWtZEgLqnrG8UXM,117293
numpy/distutils/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/distutils/tests/__pycache__/__init__.cpython-311.pyc,,
numpy/distutils/tests/__pycache__/test_build_ext.cpython-311.pyc,,
numpy/distutils/tests/__pycache__/test_ccompiler_opt.cpython-311.pyc,,
numpy/distutils/tests/__pycache__/test_ccompiler_opt_conf.cpython-311.pyc,,
numpy/distutils/tests/__pycache__/test_exec_command.cpython-311.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler.cpython-311.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler_gnu.cpython-311.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler_intel.cpython-311.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler_nagfor.cpython-311.pyc,,
numpy/distutils/tests/__pycache__/test_from_template.cpython-311.pyc,,
numpy/distutils/tests/__pycache__/test_log.cpython-311.pyc,,
numpy/distutils/tests/__pycache__/test_mingw32ccompiler.cpython-311.pyc,,
numpy/distutils/tests/__pycache__/test_misc_util.cpython-311.pyc,,
numpy/distutils/tests/__pycache__/test_npy_pkg_config.cpython-311.pyc,,
numpy/distutils/tests/__pycache__/test_shell_utils.cpython-311.pyc,,
numpy/distutils/tests/__pycache__/test_system_info.cpython-311.pyc,,
numpy/distutils/tests/test_build_ext.py,sha256=qx3jyRQ1NoTiIeEZU8z48aE113fUdwmT7foyO00QugM,2843
numpy/distutils/tests/test_ccompiler_opt.py,sha256=YAR76iKLsRIpRfS2XmKunsyHaiDzyGK-T47oNI7WmyE,29586
numpy/distutils/tests/test_ccompiler_opt_conf.py,sha256=3KyqLepj3nC2C1UYm8nv1Ne5O6KtufD-7DlvAYJuvOo,6523
numpy/distutils/tests/test_exec_command.py,sha256=EVipBhoXEJjlSwtQRptWJC1LNJc6wfYzu_81V2jdAL8,7612
numpy/distutils/tests/test_fcompiler.py,sha256=SS5HOLIg0eqkmZTRKeWq9_ahW2tmV9c9piwYfzcBPmc,1320
numpy/distutils/tests/test_fcompiler_gnu.py,sha256=RlRHZbyazgKGY17NmdYSF3ehO0M0xXN4UkbsJzJz4i8,2191
numpy/distutils/tests/test_fcompiler_intel.py,sha256=4cppjLugoa8P4bjzYdiPxmyCywmP9plXOkfsklhnYsQ,1088
numpy/distutils/tests/test_fcompiler_nagfor.py,sha256=ntyr8f-67dNI0OF_l6-aeTwu9wW-vnxpheqrc4cXAUI,1124
numpy/distutils/tests/test_from_template.py,sha256=ZzUSEPyZIG4Zak3-TFqmRGXHMp58aKTuLKb0t-5XpDg,1147
numpy/distutils/tests/test_log.py,sha256=ylfdL0kBkbjj_Tgqx47UGykAtpE_mJkLndL40p11AYc,902
numpy/distutils/tests/test_mingw32ccompiler.py,sha256=7X8V4hLMtsNj1pYoLkSSla04gJu66e87E_k-6ce3PrA,1651
numpy/distutils/tests/test_misc_util.py,sha256=YKK2WrJqVJ5o71mWL5oP0l-EVQmqKlf3XU8y7co0KYc,3300
numpy/distutils/tests/test_npy_pkg_config.py,sha256=1pQh-mApHjj0y9Ba2tqns79U8dsfDpJ9zcPdsa2qbps,2641
numpy/distutils/tests/test_shell_utils.py,sha256=aKtyXpHEYARNsAq9q5SeVC0qqMfm1gzvlN6-nXOVlac,2193
numpy/distutils/tests/test_system_info.py,sha256=-j438GufVq6Vicimybm1XxndwwiXGKuYTEb78gfY5Ws,11739
numpy/distutils/unixccompiler.py,sha256=ED_e7yHVNj4oXMze6KY8TbPxjyvHDC6o4VNGAkFA5ZQ,5567
numpy/doc/__init__.py,sha256=llSbqjSXybPuXqt6WJFZhgYnscgYl4m1tUBy_LhfCE0,534
numpy/doc/__pycache__/__init__.cpython-311.pyc,,
numpy/doc/__pycache__/constants.cpython-311.pyc,,
numpy/doc/__pycache__/ufuncs.cpython-311.pyc,,
numpy/doc/constants.py,sha256=8jSZxoMlAwNxDbAdJQfiNvx5RgDDfp_ISjWKbpTqhsM,9567
numpy/doc/ufuncs.py,sha256=ERF8YNwda32wM_OH6-n56zECahjpH3bcGKv4gYA0txc,5494
numpy/dtypes.py,sha256=RgZOflpzacGmelqi7JbIvUutdnre9hUeKZ2UE3QmWx8,2306
numpy/dtypes.pyi,sha256=D2JM4dwkSaMoQAUkDqKr3SWl_g21U4dod9B1-liwcLM,1358
numpy/exceptions.py,sha256=cPcauKVX8s4dB4mdZB-r97T8It4eomcLx34hsVGZvwg,7570
numpy/exceptions.pyi,sha256=T9fKWgHFDbiODYTmdu0_KRoGN00VunG9Rm00YLpvQOg,618
numpy/f2py/__init__.py,sha256=LeHiMOnhZ42bCx68npp4v7rehCwkwgqq4-_SQW7rc9I,5759
numpy/f2py/__init__.pyi,sha256=Hca1LGxioFoPddmOcqwkexbH4E1TSw_UTzA4Xs8bxXY,1129
numpy/f2py/__main__.py,sha256=TDesy_2fDX-g27uJt4yXIXWzSor138R2t2V7HFHwqAk,135
numpy/f2py/__pycache__/__init__.cpython-311.pyc,,
numpy/f2py/__pycache__/__main__.cpython-311.pyc,,
numpy/f2py/__pycache__/__version__.cpython-311.pyc,,
numpy/f2py/__pycache__/_isocbind.cpython-311.pyc,,
numpy/f2py/__pycache__/_src_pyf.cpython-311.pyc,,
numpy/f2py/__pycache__/auxfuncs.cpython-311.pyc,,
numpy/f2py/__pycache__/capi_maps.cpython-311.pyc,,
numpy/f2py/__pycache__/cb_rules.cpython-311.pyc,,
numpy/f2py/__pycache__/cfuncs.cpython-311.pyc,,
numpy/f2py/__pycache__/common_rules.cpython-311.pyc,,
numpy/f2py/__pycache__/crackfortran.cpython-311.pyc,,
numpy/f2py/__pycache__/diagnose.cpython-311.pyc,,
numpy/f2py/__pycache__/f2py2e.cpython-311.pyc,,
numpy/f2py/__pycache__/f90mod_rules.cpython-311.pyc,,
numpy/f2py/__pycache__/func2subr.cpython-311.pyc,,
numpy/f2py/__pycache__/rules.cpython-311.pyc,,
numpy/f2py/__pycache__/setup.cpython-311.pyc,,
numpy/f2py/__pycache__/symbolic.cpython-311.pyc,,
numpy/f2py/__pycache__/use_rules.cpython-311.pyc,,
numpy/f2py/__version__.py,sha256=TisKvgcg4vh5Fptw2GS1JB_3bAQsWZIKhclEX6ZcAho,35
numpy/f2py/_backends/__init__.py,sha256=xIVHiF-velkBDPKwFS20PSg-XkFW5kLAVj5CSqNLddM,308
numpy/f2py/_backends/__pycache__/__init__.cpython-311.pyc,,
numpy/f2py/_backends/__pycache__/_backend.cpython-311.pyc,,
numpy/f2py/_backends/__pycache__/_distutils.cpython-311.pyc,,
numpy/f2py/_backends/__pycache__/_meson.cpython-311.pyc,,
numpy/f2py/_backends/_backend.py,sha256=9RZDu4FCwCM7G39EX2YEt-Vnaz0U2WSp-QSAfz11BGE,1233
numpy/f2py/_backends/_distutils.py,sha256=XfwV6_yLFmIedfFXznCuhRfeAn4_5DIr3yLKQkM4L9g,2458
numpy/f2py/_backends/_meson.py,sha256=o8fF-Gw1YD8FvZ99dxcPRO4S-yXuZBEEtlkCxA_0x7w,7121
numpy/f2py/_backends/meson.build.template,sha256=342GTt6n71K1dytVFLCdk_JbOXTgXYYdJmNeXAmTKPg,1633
numpy/f2py/_isocbind.py,sha256=QVoR_pD_bY9IgTaSHHUw_8EBg0mkaf3JZfwhLfHbz1Q,2422
numpy/f2py/_src_pyf.py,sha256=dmgZsLgl8vbN8C-VYCXxzamkjDJ4TDQbeL8--NJMeqQ,7893
numpy/f2py/auxfuncs.py,sha256=JCfFTTx1jhPFhcV9z5C52O4_uTtpuqymcvWxqafqZRk,27527
numpy/f2py/capi_maps.py,sha256=U2Tv2hn1SN8GPC4TYIf_cwDH2NrOHNqr4O3JBLB2PTg,31382
numpy/f2py/cb_rules.py,sha256=zhOAGrwsYpVX6XzEY2H5GooswQwxfyKAxf37ZIPSCzg,25636
numpy/f2py/cfuncs.py,sha256=ImRjW2U8VF-aCSXNCRtvlvvyJCEiq0ofpYefv1OgI-4,53449
numpy/f2py/common_rules.py,sha256=19VDEPQ9-Pzzknv03U23gWYesmDAzJrGxwdXqn7CxhQ,5277
numpy/f2py/crackfortran.py,sha256=d0Rhu3LHsKzKmN6Hgde3-G2KRFToDtyAF-38GihlZbo,152320
numpy/f2py/diagnose.py,sha256=-t3VpQqke6qEjxpIrV1OA3VFuQRANimc0irdjGyO8RA,5351
numpy/f2py/f2py2e.py,sha256=6rKe3YsP9Zs0ZSvPq1tFlYazvj5ZHZaWhr8PUjO6Pa8,28485
numpy/f2py/f90mod_rules.py,sha256=7ssIBNVRN9sbxa5Z2LvjZj3TiFquPSJmHwmhnVcnv9M,9858
numpy/f2py/func2subr.py,sha256=Wro0C3NGSO-1g2zxBI8qg_Tl6KyczrCtCTJvhN4KtUQ,10621
numpy/f2py/rules.py,sha256=jJs5uAI3Hpsaqzrk6rshTzRMbUNT_SGwunmDkJnNabo,64295
numpy/f2py/setup.cfg,sha256=828sy3JvJmMzVxLkC-y0lxcEMaDTnMc3l9dWqP4jYng,50
numpy/f2py/setup.py,sha256=0un1L3stw13BYc_pVixdNVisgygl2ZR64Nml6zs4yIw,2496
numpy/f2py/src/fortranobject.c,sha256=AuZWSnAe9Gulc74QJGpLt4Vqgby---lHe9p-F0aO2fM,47440
numpy/f2py/src/fortranobject.h,sha256=OpPpUroaM_DF3dZx_3j2rICdqbdu6eEm4OTeHm-xXIM,6008
numpy/f2py/symbolic.py,sha256=4kLvSp62i7GAWxZeIBXyNaL7aDrW4YqELNotB2od9JY,54787
numpy/f2py/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/f2py/tests/__pycache__/__init__.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_abstract_interface.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_array_from_pyobj.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_assumed_shape.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_block_docstring.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_callback.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_character.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_common.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_compile_function.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_crackfortran.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_data.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_docs.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_f2cmap.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_f2py2e.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_isoc.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_kind.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_mixed.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_module_doc.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_parameter.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_pyf_src.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_quoted_character.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_regression.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_return_character.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_return_complex.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_return_integer.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_return_logical.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_return_real.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_semicolon_split.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_size.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_string.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_symbolic.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/test_value_attrspec.cpython-311.pyc,,
numpy/f2py/tests/__pycache__/util.cpython-311.pyc,,
numpy/f2py/tests/src/abstract_interface/foo.f90,sha256=aCaFEqfXp79pVXnTFtjZBWUY_5pu8wsehp1dEauOkSE,692
numpy/f2py/tests/src/abstract_interface/gh18403_mod.f90,sha256=y3R2dDn0BUz-0bMggfT1jwXbhz_gniz7ONMpureEQew,111
numpy/f2py/tests/src/array_from_pyobj/wrapmodule.c,sha256=meFXuH32wgcHdmO3pOy34qyjUjpMEVAVG0ThTF0sd3c,7529
numpy/f2py/tests/src/assumed_shape/.f2py_f2cmap,sha256=zfuOShmuotzcLIQDnVFaARwvM66iLrOYzpquIGDbiKU,30
numpy/f2py/tests/src/assumed_shape/foo_free.f90,sha256=fqbSr7VlKfVrBulFgQtQA9fQf0mQvVbLi94e4FTST3k,494
numpy/f2py/tests/src/assumed_shape/foo_mod.f90,sha256=9pbi88-uSNP5IwS49Kim982jDAuopo3tpEhg2SOU7no,540
numpy/f2py/tests/src/assumed_shape/foo_use.f90,sha256=9Cl1sdrihB8cCSsjoQGmOO8VRv9ni8Fjr0Aku1UdEWM,288
numpy/f2py/tests/src/assumed_shape/precision.f90,sha256=3L_F7n5ju9F0nxw95uBUaPeuiDOw6uHvB580eIj7bqI,134
numpy/f2py/tests/src/block_docstring/foo.f,sha256=KVTeqSFpI94ibYIVvUW6lOQ9T2Bx5UzZEayP8Maf2H0,103
numpy/f2py/tests/src/callback/foo.f,sha256=rLqaaaUpWFTaGVxNoGERtDKGCa5dLCTW5DglsFIx-wU,1316
numpy/f2py/tests/src/callback/gh17797.f90,sha256=-_NvQK0MzlSR72PSuUE1FeUzzsMBUcPKsbraHIF7O24,155
numpy/f2py/tests/src/callback/gh18335.f90,sha256=n_Rr99cI7iHBEPV3KGLEt0QKZtItEUKDdQkBt0GKKy4,523
numpy/f2py/tests/src/callback/gh25211.f,sha256=ejY_ssadbZQfD5_-Xnx_ayzWXWLjkdy7DGp6C_uCUCY,189
numpy/f2py/tests/src/callback/gh25211.pyf,sha256=nrzvt2QHZRCcugg0R-4FDMMl1MJmWCOAjR7Ta-pXz7Y,465
numpy/f2py/tests/src/cli/gh_22819.pyf,sha256=e3zYjFmiOxzdXoxzgkaQ-CV6sZ1t4aKugyhqRXmBNdQ,148
numpy/f2py/tests/src/cli/hi77.f,sha256=bgBERF4EYxHlzJCvZCJOlEmUE1FIvipdmj4LjdmL_dE,74
numpy/f2py/tests/src/cli/hiworld.f90,sha256=RncaEqGWmsH9Z8BMV-UmOTUyo3-e9xOQGAmNgDv6SfY,54
numpy/f2py/tests/src/common/block.f,sha256=tcGKa42S-6bfA6fybpM0Su_xjysEVustkEJoF51o_pE,235
numpy/f2py/tests/src/common/gh19161.f90,sha256=Vpb34lRVC96STWaJerqkDQeZf7mDOwWbud6pW62Tvm4,203
numpy/f2py/tests/src/crackfortran/accesstype.f90,sha256=3ONHb4ZNx0XISvp8fArnUwR1W9rzetLFILTiETPUd80,221
numpy/f2py/tests/src/crackfortran/data_common.f,sha256=rP3avnulWqJbGCFLWayjoFKSspGDHZMidPTurjz33Tc,201
numpy/f2py/tests/src/crackfortran/data_multiplier.f,sha256=LaPXVuo5lX0gFZVh76Hc7LM1sMk9EBPALuXBnHAGdOA,202
numpy/f2py/tests/src/crackfortran/data_stmts.f90,sha256=MAZ3gstsPqECk3nWQ5Ql-C5udrIv3sAciW1ZGTtHLts,713
numpy/f2py/tests/src/crackfortran/data_with_comments.f,sha256=FUPluNth5uHgyKqjQW7HKmyWg4wDXj3XPJCIC9ZZuOs,183
numpy/f2py/tests/src/crackfortran/foo_deps.f90,sha256=D9FT8Rx-mK2p8R6r4bWxxqgYhkXR6lNmPj2RXOseMpw,134
numpy/f2py/tests/src/crackfortran/gh15035.f,sha256=0G9bmfVafpuux4-ZgktYZ6ormwrWDTOhKMK4wmiSZlQ,391
numpy/f2py/tests/src/crackfortran/gh17859.f,sha256=acknjwoWYdA038oliYLjB4T1PHhXkKRLeJobIgB_Lbo,352
numpy/f2py/tests/src/crackfortran/gh22648.pyf,sha256=xPnKx4RcT1568q-q_O83DYpCgVYJ8z4WQ-yLmHPchJA,248
numpy/f2py/tests/src/crackfortran/gh23533.f,sha256=k2xjRpRaajMYpi5O-cldYPTZGFGB12PUGcj5Fm9joyk,131
numpy/f2py/tests/src/crackfortran/gh23598.f90,sha256=20ukdZXq-qU0Zxzt4W6cO8tRxlNlQ456zgD09zdozCE,105
numpy/f2py/tests/src/crackfortran/gh23598Warn.f90,sha256=FvnIxy5fEOvzNb5WSkWzPk7yZ9yIv0yPZk9vNnS-83w,216
numpy/f2py/tests/src/crackfortran/gh23879.f90,sha256=jELVfEGEF66z_Pv_iBHp3yGsGhadB0dnKCDtPcaz_CM,352
numpy/f2py/tests/src/crackfortran/gh2848.f90,sha256=-IpkeTz0j9_lkQeN9mT7w3U1cAJjQxSMdAmyHdF8oVg,295
numpy/f2py/tests/src/crackfortran/operators.f90,sha256=cb1JO2hIMCQejZO_UJWluBCP8LdXQbBJw2XN6YHB3JA,1233
numpy/f2py/tests/src/crackfortran/privatemod.f90,sha256=9O2oWEquIUcbDB1wIzNeae3hx4gvXAoYW5tGfBt3KWk,185
numpy/f2py/tests/src/crackfortran/publicmod.f90,sha256=nU_VXCKiniiUq_78KAWkXiN6oiMQh39emMxbgOVf9cg,177
numpy/f2py/tests/src/crackfortran/pubprivmod.f90,sha256=-uz75kquU4wobaAPZ1DLKXJg6ySCZoDME1ce6YZ2q5Y,175
numpy/f2py/tests/src/crackfortran/unicode_comment.f90,sha256=wDMoF7F7VFYdeocfTyWIh7noniEwExVb364HrhUSbSg,102
numpy/f2py/tests/src/f2cmap/.f2py_f2cmap,sha256=fwszymaWhcWO296u5ThHW5yMAkFhB6EtHWqqpc9FAVI,83
numpy/f2py/tests/src/f2cmap/isoFortranEnvMap.f90,sha256=rphN_mmzjCCCkdPM0HjsiJV7rmxpo4GoCNp5qmBzv8U,307
numpy/f2py/tests/src/isocintrin/isoCtests.f90,sha256=Oir0PfE3mErnUQ42aFxiqAkcYn3B6b1FHIPGipDdekg,1032
numpy/f2py/tests/src/kind/foo.f90,sha256=6_zq3OAWsuNJ5ftGTQAEynkHy-MnuLgBXmMIgbvL7yU,367
numpy/f2py/tests/src/mixed/foo.f,sha256=Zgn0xDhhzfas3HrzgVSxIL1lGEF2mFRVohrvXN1thU0,90
numpy/f2py/tests/src/mixed/foo_fixed.f90,sha256=6eEEYCH71gPp6lZ6e2afLrfS6F_fdP7GZDbgGJJ_6ns,187
numpy/f2py/tests/src/mixed/foo_free.f90,sha256=UC6iVRcm0-aVXAILE5jZhivoGQbKU-prqv59HTbxUJA,147
numpy/f2py/tests/src/module_data/mod.mod,sha256=EkjrU7NTZrOH68yKrz6C_eyJMSFSxGgC2yMQT9Zscek,412
numpy/f2py/tests/src/module_data/module_data_docstring.f90,sha256=-asnMH7vZMwVIeMU2YiLWgYCUUUxZgPTpbAomgWByHs,236
numpy/f2py/tests/src/negative_bounds/issue_20853.f90,sha256=IxBGWem-uv9eHgDhysEdGTmNKHR1gAiU7YJPo20eveM,164
numpy/f2py/tests/src/parameter/constant_both.f90,sha256=L0rG6-ClvHx7Qsch46BUXRi_oIEL0uw5dpRHdOUQuv0,1996
numpy/f2py/tests/src/parameter/constant_compound.f90,sha256=lAT76HcXGMgr1NfKof-RIX3W2P_ik1PPqkRdJ6EyBmM,484
numpy/f2py/tests/src/parameter/constant_integer.f90,sha256=42jROArrG7vIag9wFa_Rr5DBnnNvGsrEUgpPU14vfIo,634
numpy/f2py/tests/src/parameter/constant_non_compound.f90,sha256=u9MRf894Cw0MVlSOUbMSnFSHP4Icz7RBO21QfMkIl-Q,632
numpy/f2py/tests/src/parameter/constant_real.f90,sha256=QoPgKiHWrwI7w5ctYZugXWzaQsqSfGMO7Jskbg4CLTc,633
numpy/f2py/tests/src/quoted_character/foo.f,sha256=0zXQbdaqB9nB8R4LF07KDMFDbxlNdiJjVdR8Nb3nzIM,496
numpy/f2py/tests/src/regression/gh25337/data.f90,sha256=EqMEuEV0_sx4XbFzftbU_6VfGtOw9Tbs0pm0eVEp2cA,188
numpy/f2py/tests/src/regression/gh25337/use_data.f90,sha256=DChVLgD7qTOpbYNmfGjPjfOx5YsphMIYwdwnF12X4xM,185
numpy/f2py/tests/src/regression/inout.f90,sha256=TlMxJjhjjiuLI--Tg2LshLnbfZpiKz37EpR_tPKKSx8,286
numpy/f2py/tests/src/return_character/foo77.f,sha256=tRyQSu9vNWtMRi7gjmMN-IZnS7ogr5YS0n38uax_Eo0,1025
numpy/f2py/tests/src/return_character/foo90.f90,sha256=WPQZC6CjXLbUYpzy5LItEoHmRDFxW0ABB3emRACsjZU,1296
numpy/f2py/tests/src/return_complex/foo77.f,sha256=7-iKoamJ-VObPFR-Tslhiw9E-ItIvankWMyxU5HqxII,1018
numpy/f2py/tests/src/return_complex/foo90.f90,sha256=_GOKOZeooWp3pEaTBrZNmPmkgGodj33pJnJmySnp7aE,1286
numpy/f2py/tests/src/return_integer/foo77.f,sha256=EKs1KeAOQBkIO99tMCx0H7_lpqvqpjie8zWZ6T_bAR4,1234
numpy/f2py/tests/src/return_integer/foo90.f90,sha256=0aYWcaAVs7Lw3Qbf8hupfLC8YavRuPZVIwjHecIlMOo,1590
numpy/f2py/tests/src/return_logical/foo77.f,sha256=Ax3tBVNAlxFtHhV8fziFcsTnoa8YJdapecMr6Qj7fLk,1244
numpy/f2py/tests/src/return_logical/foo90.f90,sha256=IZXCerFecYT24zTQ_spIoPr6n-fRncaM0tkTs8JqO1E,1590
numpy/f2py/tests/src/return_real/foo77.f,sha256=3nAY1YtzGk4osR2jZkHMVIUHxFoOtF1OLfWswpcV7kA,978
numpy/f2py/tests/src/return_real/foo90.f90,sha256=38ZCnBGWb9arlJdnVWvZjVk8uesrQN8wG2GrXGcSIJs,1242
numpy/f2py/tests/src/size/foo.f90,sha256=nK_767f1TtqVr-dMalNkXmcKbSbLCiabhRkxSDCzLz0,859
numpy/f2py/tests/src/string/char.f90,sha256=X_soOEV8cKsVZefi3iLT7ilHljjvJJ_i9VEHWOt0T9Y,647
numpy/f2py/tests/src/string/fixed_string.f90,sha256=tCN5sA6e7M1ViZtBNvTnO7_efk7BHIjyhFKBoLC3US0,729
numpy/f2py/tests/src/string/gh24008.f,sha256=Z6cq8SFGvmaA72qeH9tu1rP8pYjqm0ONpHn7nGbhoLA,225
numpy/f2py/tests/src/string/gh24662.f90,sha256=xJkiYvrMT9Ipb9Cq7OXl1Ev6TISl8pq1MGemySzfGd0,204
numpy/f2py/tests/src/string/gh25286.f90,sha256=lqEl81Iu9GIDTAbOfkkNGcGgDyyGnPB44mJw2iK1kng,318
numpy/f2py/tests/src/string/gh25286.pyf,sha256=wYkkr5gEN9_RtGjpqh28X1k8KCgh0-Ds9XAt8IC9j4A,393
numpy/f2py/tests/src/string/gh25286_bc.pyf,sha256=ZRvgSzRlaPEx8GyNt97FrRhtCg-r4ZTEDsHNBfit4m8,396
numpy/f2py/tests/src/string/scalar_string.f90,sha256=U1QqVgbF1DbxdFekRjchyDlFRPnXwzG72kuE8A44Za8,185
numpy/f2py/tests/src/string/string.f,sha256=JCwLuH21Ltag5cw_9geIQQJ4Hv_39NqG8Dzbqj1eDKE,260
numpy/f2py/tests/src/value_attrspec/gh21665.f90,sha256=MbbSUQI5Enzq46KWFHRzQbY7q6ZHJH_9NRL-C9i13Wg,199
numpy/f2py/tests/test_abstract_interface.py,sha256=fpwfzU3PRqNEhWNSvkNu0eLoA6rzyFdNZ4NMIUR2hGI,857
numpy/f2py/tests/test_array_from_pyobj.py,sha256=P1NLvTkXSDnqK86L0Qdb0BgSriqmNleBFULFxMJ0QPk,24735
numpy/f2py/tests/test_assumed_shape.py,sha256=IyqJPGpGVv_RaRCwrko_793jLxJC1495tR9gAbmTlR8,1515
numpy/f2py/tests/test_block_docstring.py,sha256=0UP4PhKCiF6vgaczLt3tHijL4sbP6DIn6NMTVfukkOI,581
numpy/f2py/tests/test_callback.py,sha256=PCsctFHwVWQ3S7y0Wzeg_Q0R4E5doocT0po5fQduS-Q,6737
numpy/f2py/tests/test_character.py,sha256=Ms2MoKoRAt8QVXlxtdclWvqM7FCyF04bM1_23IdC1QA,22504
numpy/f2py/tests/test_common.py,sha256=4nfrNOEXMl8mlnzIo8ojmTn33BwAm5FwmburbGMUZS8,923
numpy/f2py/tests/test_compile_function.py,sha256=-i-zIVJzI-H3mmikdW9cINNmJ35JbuTTlFq5Ld9kswY,4303
numpy/f2py/tests/test_crackfortran.py,sha256=rO3bB0CLBLROtqX9EOTOvQ6Pc5u6sq-saHJQBZzO44w,13791
numpy/f2py/tests/test_data.py,sha256=LLgnt45XZqzVHNUtJ1CIqC-jYsE43mrayedZB8erjTg,2946
numpy/f2py/tests/test_docs.py,sha256=vfRa7D_I_GsaROqENe1XzhkivcBixEjbR8G_vByBEi4,1717
numpy/f2py/tests/test_f2cmap.py,sha256=bqUqqq1DJz9k2v2TnE9YKN13TS2l8kL1Yxw87J4jpVk,406
numpy/f2py/tests/test_f2py2e.py,sha256=PbXEACMU2_aZitgjRD8B-PlUmNZryE4zY6MNqoOw31E,26319
numpy/f2py/tests/test_isoc.py,sha256=0swG5OKql1jyLA3XiDgYls2QPupDHQB_DJlj5pvRrGw,1458
numpy/f2py/tests/test_kind.py,sha256=MSxoWKI-TJ9OG_Ys_GKWkJHeon_RUCoP1t3Uf823jXA,1718
numpy/f2py/tests/test_mixed.py,sha256=XmGaJC4Nf1OZfOxVX7TYp6KlaeZeb8ZytmiKIql3VvI,881
numpy/f2py/tests/test_module_doc.py,sha256=vYP-TM5D21z2igbKcR9WVNSOLSXQWyQ9yVa6l9W2k90,890
numpy/f2py/tests/test_parameter.py,sha256=E3SfDemPom95ZpBjBq59Z98ztghPf6biwLrU-kmc56M,4053
numpy/f2py/tests/test_pyf_src.py,sha256=RLm95aANGakQYCzk_UJjUcq0mOQH0LtD6HoZYkEiIrU,1179
numpy/f2py/tests/test_quoted_character.py,sha256=0rwaANREvKwA0Rz65_B5S2xKy52kx7xzdijO5qFz4ac,470
numpy/f2py/tests/test_regression.py,sha256=plNmsEFYAGIXyV6Dyiqh-y7qhtHG9wbzn5J0aq-H4O0,2609
numpy/f2py/tests/test_return_character.py,sha256=TlDsBmECSXhrBynUEGBFv0J9K7WnesQe2FlHXaHYgMM,1538
numpy/f2py/tests/test_return_complex.py,sha256=KZJbNjttZwzaVxPJhg7a_SvQ_bxm_wyua_9UfoaDYzA,2462
numpy/f2py/tests/test_return_integer.py,sha256=EIMnaAonNMxNDG7EiZ0kiH7mBKHriJ7PQIIITOd_HA8,1811
numpy/f2py/tests/test_return_logical.py,sha256=gPBO6zxmwek0fUIvCDgybiltiNqiMwaIqqsY2o0PXtg,2081
numpy/f2py/tests/test_return_real.py,sha256=FVi1BODlqgJFH4O6KTEbdagTdlRUAeKOoxvy10klHpc,3342
numpy/f2py/tests/test_semicolon_split.py,sha256=XRRffHS0K82Kls91xtLcSzAIoWpHHpii8ZaUkJYSSH4,1709
numpy/f2py/tests/test_size.py,sha256=Qy8KH9Co1IL6GbnDJ5IDGRPD1HKQ3HL6zXCkN2wpuUY,1209
numpy/f2py/tests/test_string.py,sha256=vSMQKo1SK4Y1xpgVw8iquHHH2kmelFsmphMMKYhnAaM,3062
numpy/f2py/tests/test_symbolic.py,sha256=Zk4h3WC2etMrIEyMrayPpGthpWfuS35Yz-4XzzGFcY4,18835
numpy/f2py/tests/test_value_attrspec.py,sha256=wxF8WE9BQR4vj3ooVH0Z8Tnkse6GIUbU1CoMeyeqjF8,337
numpy/f2py/tests/util.py,sha256=9MKHChAFvd4AJ3IJQPGkC0FNrE3panXySeQJJkSnDLU,11607
numpy/f2py/use_rules.py,sha256=xSi4D11ZN6_O7kQZ_v_dD-043gTeD1y7YvqKBq58FYg,3633
numpy/fft/__init__.py,sha256=efF5_sqvdBfwIBHcEvkIb0a7YWZvR5oa4jh_aelUTpk,8387
numpy/fft/__init__.pyi,sha256=wMurHg76HViKOj8Nt6YveOJ-zt_a4DOGuDu2TRmshvY,579
numpy/fft/__pycache__/__init__.cpython-311.pyc,,
numpy/fft/__pycache__/_pocketfft.cpython-311.pyc,,
numpy/fft/__pycache__/helper.cpython-311.pyc,,
numpy/fft/_pocketfft.py,sha256=Eig0b3LbIFwSRFVcB6XHbBpAbDFIjO5gijB5_cY8_1o,54321
numpy/fft/_pocketfft.pyi,sha256=W-WyKPRKzA0JwHwaDhkJ7uMNeXxv1-n1-kvqnBdj94g,2479
numpy/fft/_pocketfft_internal.cp311-win_amd64.lib,sha256=H-uFChKpqaKjKEcyyw1dbfJGA3QLMMNF2MeH2jHU0Yo,2228
numpy/fft/_pocketfft_internal.cp311-win_amd64.pyd,sha256=IHyJTUqX1erDKKh5NrHFoWDPEWPYs_WbPEN5LZtSJKQ,110080
numpy/fft/helper.py,sha256=divqfKoOb0p-Zojqokhj3fQ5HzgWTigxDxZIQnN_TUQ,6375
numpy/fft/helper.pyi,sha256=sqSWM32SxhTzjUZkiAcwC0lkrI2pPcXRR0cJVjVxQfU,1223
numpy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/fft/tests/__pycache__/__init__.cpython-311.pyc,,
numpy/fft/tests/__pycache__/test_helper.cpython-311.pyc,,
numpy/fft/tests/__pycache__/test_pocketfft.cpython-311.pyc,,
numpy/fft/tests/test_helper.py,sha256=iopdl7-SHA5E1Ex13rYdceskgoDI5T8_Kg12LfqK1HA,6315
numpy/fft/tests/test_pocketfft.py,sha256=QouqHEUFE3Yxz2aPj_zZiq2HcW2SqNWh5f2Vjj6b_K4,13203
numpy/lib/__init__.py,sha256=3f_FRVF6ivfy_WBXebkylyyf6a893MO2LBvSNL1awNs,2805
numpy/lib/__init__.pyi,sha256=QAbawYSE6XWhTkWtjEb2XUgn18a4kCvUrjqw6QWGe64,5841
numpy/lib/__pycache__/__init__.cpython-311.pyc,,
numpy/lib/__pycache__/_datasource.cpython-311.pyc,,
numpy/lib/__pycache__/_iotools.cpython-311.pyc,,
numpy/lib/__pycache__/_version.cpython-311.pyc,,
numpy/lib/__pycache__/arraypad.cpython-311.pyc,,
numpy/lib/__pycache__/arraysetops.cpython-311.pyc,,
numpy/lib/__pycache__/arrayterator.cpython-311.pyc,,
numpy/lib/__pycache__/format.cpython-311.pyc,,
numpy/lib/__pycache__/function_base.cpython-311.pyc,,
numpy/lib/__pycache__/histograms.cpython-311.pyc,,
numpy/lib/__pycache__/index_tricks.cpython-311.pyc,,
numpy/lib/__pycache__/mixins.cpython-311.pyc,,
numpy/lib/__pycache__/nanfunctions.cpython-311.pyc,,
numpy/lib/__pycache__/npyio.cpython-311.pyc,,
numpy/lib/__pycache__/polynomial.cpython-311.pyc,,
numpy/lib/__pycache__/recfunctions.cpython-311.pyc,,
numpy/lib/__pycache__/scimath.cpython-311.pyc,,
numpy/lib/__pycache__/setup.cpython-311.pyc,,
numpy/lib/__pycache__/shape_base.cpython-311.pyc,,
numpy/lib/__pycache__/stride_tricks.cpython-311.pyc,,
numpy/lib/__pycache__/twodim_base.cpython-311.pyc,,
numpy/lib/__pycache__/type_check.cpython-311.pyc,,
numpy/lib/__pycache__/ufunclike.cpython-311.pyc,,
numpy/lib/__pycache__/user_array.cpython-311.pyc,,
numpy/lib/__pycache__/utils.cpython-311.pyc,,
numpy/lib/_datasource.py,sha256=CV1h_ndJHlw67YhB42aevDsIOOfDOaYQnOImJsnDiUg,23335
numpy/lib/_iotools.py,sha256=v_ADGzQZKdQ0OHSAijHLZ76L2gNNHxqIeD7VhIZacBU,31765
numpy/lib/_version.py,sha256=IjsC8gQRuFo_Ns0fSuF0BW7ndA3h9jQV5mClyWe9A8s,5010
numpy/lib/_version.pyi,sha256=rw_2q4KJ-dfpqJuj1e3PtVqK4Yh2FdJa5AHdE5IRaWM,650
numpy/lib/arraypad.py,sha256=w3t-jqHP2gEyKrmha6ImQRTcbh3pbKOprNq7BnHNLcc,32685
numpy/lib/arraypad.pyi,sha256=8z4FY3lJ1Xde2FHkoxmpz94aYDk5MDFoMvto8KALxvM,1813
numpy/lib/arraysetops.py,sha256=saP9Q02Td6b4ozUJp5LwbCNPBRPKj7cXUFNYwNC9aeA,34636
numpy/lib/arraysetops.pyi,sha256=iLJmAw0wEyGktjJn-hV9vNwF4gc_8Giw-r9I1tZqu-8,8734
numpy/lib/arrayterator.py,sha256=29pO5S0ciEZwt1402Q0-5cRbyKspV4tlPX1-m_D_Hgc,7282
numpy/lib/arrayterator.pyi,sha256=GlJgmZGbY7vomeNHcX6jvmc5hVtguq4-fYFsw3zj2Zs,1586
numpy/lib/format.py,sha256=DW3dqc1vHTDhuvGD9Hv-B4bMoq4UaRjm3TjXdL3CudY,35745
numpy/lib/format.pyi,sha256=dAlF-kNz-H-Vtn9H7Cs8J1l1xUkc3A2e7oWK1Qy17bs,770
numpy/lib/function_base.py,sha256=3P6CKoZsMgpN9OEZTtJcgQj5QuUkt9qPkb3l-SEmiw8,194905
numpy/lib/function_base.pyi,sha256=IBsKaOFLLr1lGbozKo6yvC8gXC0BjKuNy8Sd_DchORA,17282
numpy/lib/histograms.py,sha256=CVrEPtEz67O0IpNS1AxyY89ACNIoDOWu0oT2o_e9ctA,38850
numpy/lib/histograms.pyi,sha256=iNMKj6DEO-7r5G5bH2ZPTnivyre7Xy5T2y7h6Knurig,1042
numpy/lib/index_tricks.py,sha256=uGotKYcDbahlrLNDnP8UP1jvv1VBwtthl6AQXT1fyB4,32392
numpy/lib/index_tricks.pyi,sha256=9cuKQlYhxiObpnernl1Dzoo9xxs-l4Eb8qXmJiJiwaQ,4413
numpy/lib/mixins.py,sha256=GmKcfKzr_Tx4Rv4vMqidma5cRVxG-s9Zp-HUJbsv0m0,7248
numpy/lib/mixins.pyi,sha256=311dfgGno6FFV1knGjcJob3GqML80FxBaYOTZuYnC_A,3191
numpy/lib/nanfunctions.py,sha256=oP9EJzbur7ndXEiO_8ejEKLy7hBJeT4oNwutVV0I-GM,67662
numpy/lib/nanfunctions.pyi,sha256=_KUODwVSfoDYVb8Qp2FDnLfmusNM690WpSVuMrFwvw8,644
numpy/lib/npyio.py,sha256=BPU4l5bQrEl8A4ouMNQDn1G8NQpD0Kmi8x_ag-yAroA,99863
numpy/lib/npyio.pyi,sha256=9COqY09qFVRFXNuEpYv8hZAsShR3XVaZapPMXn3vn54,10058
numpy/lib/polynomial.py,sha256=VIIXHODR9_vipluNjOGeAT18dqeLtbI057ZT7G8KEms,45586
numpy/lib/polynomial.pyi,sha256=E-leT7vdwHnTkLa2CeQ4Ntnr6oNgVy89sUXQOW50qlU,7261
numpy/lib/recfunctions.py,sha256=7NVvwtHT-FTiKamXtIu_11V75jwqY8Ylio20fGhuVWA,61096
numpy/lib/scimath.py,sha256=mVVANvK9ChJ3myB9ktfnOrh936ilNey9Eq8StSOYzbc,15662
numpy/lib/scimath.pyi,sha256=bcW3wCbYG_cQpWyMAQ9dRY5JenhnGt8RiBjCTewaxag,2977
numpy/lib/setup.py,sha256=1s3corE4egZnDZ6I8au_mx7muRPgb3ZxL4Ko2SHt_2Q,417
numpy/lib/shape_base.py,sha256=lgujlSCVEXYo8oY61Tx5S1uvrmqLSvAnJPV7XIR2a-o,40221
numpy/lib/shape_base.pyi,sha256=bjL1OHeRt2KEi74Kpev3aIgqp8Y5um8d24wtdojX5A0,5561
numpy/lib/stride_tricks.py,sha256=fVtBjoR_zpOKBYsGkFPH8QtKBzpTo3kVi-01HW5tMzg,18458
numpy/lib/stride_tricks.pyi,sha256=papM2ENge9VNzvNApZ3a2VApgrIkytrzr_Oy-ia0iwM,1827
numpy/lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/lib/tests/__pycache__/__init__.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test__datasource.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test__iotools.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test__version.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_arraypad.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_arraysetops.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_arrayterator.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_financial_expired.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_format.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_function_base.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_histograms.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_index_tricks.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_io.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_loadtxt.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_mixins.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_nanfunctions.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_packbits.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_polynomial.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_recfunctions.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_regression.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_shape_base.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_stride_tricks.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_twodim_base.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_type_check.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_ufunclike.cpython-311.pyc,,
numpy/lib/tests/__pycache__/test_utils.cpython-311.pyc,,
numpy/lib/tests/data/py2-objarr.npy,sha256=F4cyUC-_TB9QSFLAo2c7c44rC6NUYIgrfGx9PqWPSKk,258
numpy/lib/tests/data/py2-objarr.npz,sha256=xo13HBT0FbFZ2qvZz0LWGDb3SuQASSaXh7rKfVcJjx4,366
numpy/lib/tests/data/py3-objarr.npy,sha256=pTTVh8ezp-lwAK3fkgvdKU8Arp5NMKznVD-M6Ex_uA0,341
numpy/lib/tests/data/py3-objarr.npz,sha256=qQR0gS57e9ta16d_vCQjaaKM74gPdlwCPkp55P-qrdw,449
numpy/lib/tests/data/python3.npy,sha256=X0ad3hAaLGXig9LtSHAo-BgOvLlFfPYMnZuVIxRmj-0,96
numpy/lib/tests/data/win64python2.npy,sha256=agOcgHVYFJrV-nrRJDbGnUnF4ZTPYXuSeF-Mtg7GMpc,96
numpy/lib/tests/test__datasource.py,sha256=H6PZKQ0tY6r1bhrcLRKMjWdWop5P4Rj_SYvrU9ukDzc,10921
numpy/lib/tests/test__iotools.py,sha256=q44VFSi9VzWaf_dJ-MGBtYA7z7TFR0j0AF-rbzhLXoo,14096
numpy/lib/tests/test__version.py,sha256=v2TOlH4f1Pmzxn1HWby3eBgLO9tGnhwH2LvBXlXtHP4,2063
numpy/lib/tests/test_arraypad.py,sha256=1kYK0VAcYToDyl2mQAZqvBLDspyuowwYXGtbFooS0D8,56210
numpy/lib/tests/test_arraysetops.py,sha256=OypEN5etwXLZpXAZ3399DxS3beQAWTiEs08VPGohID8,36856
numpy/lib/tests/test_arrayterator.py,sha256=IRVmzxbr9idboJjOHKuX_8NQhMAKs7pD1xWqmU3ZERw,1337
numpy/lib/tests/test_financial_expired.py,sha256=D4d0h5E49b8wfIRr_mYdh_VfSU1cFS8VWy5NHc7nBYM,258
numpy/lib/tests/test_format.py,sha256=yoftjX4616Lj8Lqxz-3mJjE0q36sou_6rt3eUBgn0ng,42056
numpy/lib/tests/test_function_base.py,sha256=RU4GYpyjbRzR-lvAAmtMpZtvLLhtBVsuC8BWZQwLJWo,162031
numpy/lib/tests/test_histograms.py,sha256=iZ3wP6f97rO0cKmpEaGjwrPRfFMI1ojgOp1H8SxOPbk,33631
numpy/lib/tests/test_index_tricks.py,sha256=NhkCiQoGBliuNrpK3wOq4-g_Yh_He57v0A3DS9jEGsw,20807
numpy/lib/tests/test_io.py,sha256=2tCiYQRpgk9kct_CUdmc3k9Tj6CyXHFtVJEPjhWSvGc,110666
numpy/lib/tests/test_loadtxt.py,sha256=PKQqIY9_K_vOGwbZO2FIG3tj9YoNGrfQSFH_9SKzHKk,39608
numpy/lib/tests/test_mixins.py,sha256=nIec_DZIDx7ONnlpq_Y2TLkIULAPvQ7LPqtMwEHuV4U,7246
numpy/lib/tests/test_nanfunctions.py,sha256=169LPPzIA_Iueq9UTGHZj0D1_k5HopwPpqmNYkWc0cg,48877
numpy/lib/tests/test_packbits.py,sha256=XpFIaL8mOWfzD3WQaxd6WrDFWh4Mc51EPXIOqxt3wS0,17922
numpy/lib/tests/test_polynomial.py,sha256=x2h1teRr7d-gcu17w0s3G4NlZt1jgOjKTiXP_7lPqVI,11698
numpy/lib/tests/test_recfunctions.py,sha256=OBrCGHSH3wAPVj0hVjMX05ev97LVqeAUm1bTTZZoEMU,45044
numpy/lib/tests/test_regression.py,sha256=NXKpFka0DPE7z0-DID-FGh0ITFXi8nEj6Pg_-uBcDIg,8504
numpy/lib/tests/test_shape_base.py,sha256=BHZcDSf_33stUn6eSyNr5T3kfet2u3ldK1YgoNH0ybQ,27604
numpy/lib/tests/test_stride_tricks.py,sha256=1zeBcvhltePbeE6SBoF4gomAYaZzwaHjvbWqcgI2JiY,23494
numpy/lib/tests/test_twodim_base.py,sha256=mNNXsDKT3hPpz-HB_1k8YTWpwdx7dnvmrWWS_Lkew30,19382
numpy/lib/tests/test_type_check.py,sha256=RQaOWcN0eAiTJVBwca5NqK-NBVO1oQ93rIOVTCYUDF4,15592
numpy/lib/tests/test_ufunclike.py,sha256=DKQPcdbFzM8gf0-8x3z62Onf39L-HhmuqUDrptmMiDc,3080
numpy/lib/tests/test_utils.py,sha256=i1AEjln5gW62hTlG0PFVAmCoovxnWHkhAx52C5BmXv0,6446
numpy/lib/twodim_base.py,sha256=8W-guIdgC7ppc-Eom6Wh4cnAQLKj3LldTtD8CWF56kI,34130
numpy/lib/twodim_base.pyi,sha256=NVoc6yoeJlKC0zKvUfu9qXi2e-MA7vex6DPqGzOlgmg,5609
numpy/lib/type_check.py,sha256=905dhd2YY8MhxdAHuM73uOIhVYLp33TRdibHSXYiFBE,20689
numpy/lib/type_check.pyi,sha256=btZZOeVEcX7J7igi9d4bjgToV2CWoWPIl0ZDX9J84WU,5793
numpy/lib/ufunclike.py,sha256=DMbggdSf9cKoGYPmW-BdyGaY9oWSyB4IhRvc7DCzpiE,6535
numpy/lib/ufunclike.pyi,sha256=iTi6kfrbWzcrKfAfChC7CY48U8CDVxghnb8wofcbpuw,1359
numpy/lib/user_array.py,sha256=5yqkyjCmUIASGNx2bt7_ZMWJQJszkbD1Kn06qqv7POA,8007
numpy/lib/utils.py,sha256=ofK3j9Z3Lk4C_k-vj5HkObANxBNmu72CdJF0I3pkI9Y,39015
numpy/lib/utils.pyi,sha256=doryLj8MvJLMas-01JahMyivjINE_upXWK1bEJxzlHE,2451
numpy/linalg/__init__.py,sha256=EefgbxHkx8sLgXXLtpD8i8A_SCx-V4uz1Z_aQkmW6Ec,1893
numpy/linalg/__init__.pyi,sha256=94TdIMqJcc1OMeDpom2Zv-QxKL81BQ55Otcc-wyOUeo,650
numpy/linalg/__pycache__/__init__.cpython-311.pyc,,
numpy/linalg/__pycache__/linalg.cpython-311.pyc,,
numpy/linalg/_umath_linalg.cp311-win_amd64.lib,sha256=yND6Lo4S0z-sep_Z58FUQs-lbDLXNC-Y-IaJSng40UE,2120
numpy/linalg/_umath_linalg.cp311-win_amd64.pyd,sha256=BZIqK-gj7C5NI3inOwW7N_KBau6oa2E6nIDiV2SshzY,106496
numpy/linalg/lapack_lite.cp311-win_amd64.lib,sha256=TUrKGykMsKMmqiSYkQFHx0WUbUIqeuPbca1zinLbxw8,2084
numpy/linalg/lapack_lite.cp311-win_amd64.pyd,sha256=iWt0PrwPQHMgyliHfyEnVzmnSjoV_BVov75GDryrCKQ,17920
numpy/linalg/linalg.py,sha256=9nR25aE_joNZHqxkm6qSOyzYTlqzloNK8oscI45wPWQ,93759
numpy/linalg/linalg.pyi,sha256=nQl8TWgTYx3Ffd3izYuWbpxUTqVBZ36yFCIhQfRm84c,7802
numpy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/linalg/tests/__pycache__/__init__.cpython-311.pyc,,
numpy/linalg/tests/__pycache__/test_deprecations.cpython-311.pyc,,
numpy/linalg/tests/__pycache__/test_linalg.cpython-311.pyc,,
numpy/linalg/tests/__pycache__/test_regression.cpython-311.pyc,,
numpy/linalg/tests/test_deprecations.py,sha256=GaeE3JnQlJLoAfbY93LmgCFUlV5M8IFmQ7EhF4WbqwU,660
numpy/linalg/tests/test_linalg.py,sha256=b4mDm3FlTUOnYSu3xQN5iKyPTVA5PK3T71UIgVyHg4c,80283
numpy/linalg/tests/test_regression.py,sha256=fmBZi38DdSomvfaRsgX9JQ2jGfZsuT3VKvGK6lkKqIo,5579
numpy/ma/API_CHANGES.txt,sha256=U39zA87aM_OIJhEKvHgL1RY1lhMJZc1Yj3DGLwbPbF0,3540
numpy/ma/LICENSE,sha256=1427IIuA2StNMz5BpLquUNEkRPRuUxmfp3Jqkd5uLac,1616
numpy/ma/README.rst,sha256=Fba64rqteKleBv3JsU9SAKHTSzoLnN4SwSd9_6zV87k,10108
numpy/ma/__init__.py,sha256=9i-au2uOZ_K9q2t9Ezc9nEAS74Y4TXQZMoP9601UitU,1458
numpy/ma/__init__.pyi,sha256=tjaYqdOwraZRAf-_PjYY-xCUezJaowDWi2JVkmca_s4,6297
numpy/ma/__pycache__/__init__.cpython-311.pyc,,
numpy/ma/__pycache__/core.cpython-311.pyc,,
numpy/ma/__pycache__/extras.cpython-311.pyc,,
numpy/ma/__pycache__/mrecords.cpython-311.pyc,,
numpy/ma/__pycache__/setup.cpython-311.pyc,,
numpy/ma/__pycache__/testutils.cpython-311.pyc,,
numpy/ma/__pycache__/timer_comparison.cpython-311.pyc,,
numpy/ma/core.py,sha256=GC2ROmsWDTM9-fTtldRJdK3jkiENJRKTK8KTUhtDZJA,286778
numpy/ma/core.pyi,sha256=0JNhL-_kxcxXQGKQ_-GwR-oxGN0ejQp4xz2BxuZmmsQ,14776
numpy/ma/extras.py,sha256=zWh56j-b6HNx2_zv26Bjh1chyQhKO9YnIrVD3NLlrLA,66516
numpy/ma/extras.pyi,sha256=C_OGRvFlzoEsfwX6SjhaIWLqhsJgljhzRkkWy5D9mko,2731
numpy/ma/mrecords.py,sha256=zy-LVXMJnyDCbsBfWyFCC8Z2-y6ApeXw9OfQJNiiWZg,28015
numpy/ma/mrecords.pyi,sha256=nMx2BRyVzU_7AnAKrF3QoBwQH9TxxQYqBrrv6WhVI_I,2024
numpy/ma/setup.py,sha256=DCi5FtZTlkhR3ByJH5Sp5B962bfcWpaOjA-y7ueyoog,430
numpy/ma/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/ma/tests/__pycache__/__init__.cpython-311.pyc,,
numpy/ma/tests/__pycache__/test_core.cpython-311.pyc,,
numpy/ma/tests/__pycache__/test_deprecations.cpython-311.pyc,,
numpy/ma/tests/__pycache__/test_extras.cpython-311.pyc,,
numpy/ma/tests/__pycache__/test_mrecords.cpython-311.pyc,,
numpy/ma/tests/__pycache__/test_old_ma.cpython-311.pyc,,
numpy/ma/tests/__pycache__/test_regression.cpython-311.pyc,,
numpy/ma/tests/__pycache__/test_subclassing.cpython-311.pyc,,
numpy/ma/tests/test_core.py,sha256=vd9ctsfAMFwjFT43_RWStwf-H-XgHuKCak57ETLoc2M,220787
numpy/ma/tests/test_deprecations.py,sha256=WurKSuN6hsXmWxRoxstdVBXcKCTvYxlYz-ntSkW6qKc,2650
numpy/ma/tests/test_extras.py,sha256=1pW0MhlH1i0KksBo6Ole_L2GhliTZ2eEMtf_BCgAw3Q,76728
numpy/ma/tests/test_mrecords.py,sha256=mY5kv1FEn9KTgm2bukb0F74HsoenEAoJlvp1zzRTSBs,20383
numpy/ma/tests/test_old_ma.py,sha256=TA4ktKsx_aco2e-7bg0gb9SOYan4uj4lU6kkJkWYYT0,33564
numpy/ma/tests/test_regression.py,sha256=J1ftHDKfIF3SUIgQlxJplCsYTrPpAyN4rf5K1Uw5T8w,3384
numpy/ma/tests/test_subclassing.py,sha256=XlO_g1BnKLN35V8rz1IyScSqAyH5kwfiNm0POuq0Sdk,17427
numpy/ma/testutils.py,sha256=e2OGkdewldyNeqotXNbGiHw2pfn2uunPalH_Z4pnHk0,10523
numpy/ma/timer_comparison.py,sha256=xhRDTkkqvVLvB5HeFKIQqGuicRerabKKX3VmBUGc4Zs,16101
numpy/matlib.py,sha256=lW1bZKbPVVim348f5rtVlPjLLAMVJaclCdiXG7hsFHM,10843
numpy/matrixlib/__init__.py,sha256=9-DMlmdLxOk5HSGJ20AuTjKkGZ3MUPHCFjhE6sb4NMo,253
numpy/matrixlib/__init__.pyi,sha256=w70nB0WHow4AVG5tw3Rl9zv4S2n63-68T8AhVJlNup0,267
numpy/matrixlib/__pycache__/__init__.cpython-311.pyc,,
numpy/matrixlib/__pycache__/defmatrix.cpython-311.pyc,,
numpy/matrixlib/__pycache__/setup.cpython-311.pyc,,
numpy/matrixlib/defmatrix.py,sha256=q_71JVw9lYxlId_y8iftzkznpLj9EvJXC4sORp4lReE,31770
numpy/matrixlib/defmatrix.pyi,sha256=i7medmOD8aL6_PMJSiGSnWmld_YOxsoP67Kh-SR_QLo,467
numpy/matrixlib/setup.py,sha256=DEY5vWe-ReFP31junY0nZ7HwDpRIVuHLUtiTXL_Kr3A,438
numpy/matrixlib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/matrixlib/tests/__pycache__/__init__.cpython-311.pyc,,
numpy/matrixlib/tests/__pycache__/test_defmatrix.cpython-311.pyc,,
numpy/matrixlib/tests/__pycache__/test_interaction.cpython-311.pyc,,
numpy/matrixlib/tests/__pycache__/test_masked_matrix.cpython-311.pyc,,
numpy/matrixlib/tests/__pycache__/test_matrix_linalg.cpython-311.pyc,,
numpy/matrixlib/tests/__pycache__/test_multiarray.cpython-311.pyc,,
numpy/matrixlib/tests/__pycache__/test_numeric.cpython-311.pyc,,
numpy/matrixlib/tests/__pycache__/test_regression.cpython-311.pyc,,
numpy/matrixlib/tests/test_defmatrix.py,sha256=nbY_HkwzoJbhYhACiEN-cZmR644sVJvMKWUcsANPayQ,15435
numpy/matrixlib/tests/test_interaction.py,sha256=C1YtIubO6Qh8RR-XONzo8Mle4bu4SvwsvBnB0x0Gy4g,12229
numpy/matrixlib/tests/test_masked_matrix.py,sha256=pJBlAnKEourHnqNk9b23j_traRp978pAF5uzdQrtuZU,9163
numpy/matrixlib/tests/test_matrix_linalg.py,sha256=9S9Zrk8PMLfEEo9wBx5LyrV_TbXhI6r-Hc5t594lQFY,2152
numpy/matrixlib/tests/test_multiarray.py,sha256=E5jvWX9ypWYNHH7iqAW3xz3tMrEV-oNgjN3_oPzZzws,570
numpy/matrixlib/tests/test_numeric.py,sha256=l-LFBKPoP3_O1iea23MmaACBLx_tSSdPcUBBRTiTbzk,458
numpy/matrixlib/tests/test_regression.py,sha256=FgYV3hwkpO0qyshDzG7n1JfQ-kKwnSZnA68jJHS7TeM,958
numpy/polynomial/__init__.py,sha256=3X2b_24V61Nb5Zs00ITyX4XCfGvxiOjgbiC6-FlASOs,6966
numpy/polynomial/__init__.pyi,sha256=ng3gyC_49Isv2zjbUFrZOxRd0zltgfqz0EXXS2NLN8o,723
numpy/polynomial/__pycache__/__init__.cpython-311.pyc,,
numpy/polynomial/__pycache__/_polybase.cpython-311.pyc,,
numpy/polynomial/__pycache__/chebyshev.cpython-311.pyc,,
numpy/polynomial/__pycache__/hermite.cpython-311.pyc,,
numpy/polynomial/__pycache__/hermite_e.cpython-311.pyc,,
numpy/polynomial/__pycache__/laguerre.cpython-311.pyc,,
numpy/polynomial/__pycache__/legendre.cpython-311.pyc,,
numpy/polynomial/__pycache__/polynomial.cpython-311.pyc,,
numpy/polynomial/__pycache__/polyutils.cpython-311.pyc,,
numpy/polynomial/__pycache__/setup.cpython-311.pyc,,
numpy/polynomial/_polybase.py,sha256=s7CMmSnyWByuTAtIn9rvoOb6H-3TPWiD5NEs4GTIdvc,40477
numpy/polynomial/_polybase.pyi,sha256=PLK_DYLWFhBfrNcgFPUGdSYYuJKUNl0pOll_ZmlBXgk,2392
numpy/polynomial/chebyshev.py,sha256=8FYY9dz4j2NMQStk76zMzqZe1jwJ4V05eQ_l9MdW6BM,64878
numpy/polynomial/chebyshev.pyi,sha256=UKyHeff6dp8DV3J5e4-iT31lu4-pwIXSu_oy35AsQRU,1438
numpy/polynomial/hermite.py,sha256=ar2OAs01deEV1DEs5viBjYlxkVuVTbFWrs1dEXjxclg,54217
numpy/polynomial/hermite.pyi,sha256=kpbjKXoOW1QSHFtPTxLx7LGtIY540CcpaBF_YTO1TVY,1263
numpy/polynomial/hermite_e.py,sha256=SNEBPPz5O94c113MOHsl7P2sANdCUoqFScSpdQv9yXg,54337
numpy/polynomial/hermite_e.pyi,sha256=AP6dm9PsnEox2i5MyVbJRZQMd0mgaqw5IasrCFtPOoo,1284
numpy/polynomial/laguerre.py,sha256=Dfm6FaMRIWAd6HxREPqiW-SHYYvXpaT82BMZnIata5o,52509
numpy/polynomial/laguerre.pyi,sha256=v5RTMhLIRsGxx9vKAAs6WyQqBjxjNK-YrTFKEJcJFaw,1224
numpy/polynomial/legendre.py,sha256=f9Jy0f8Ia0lI80Roqd_04a4F_Q-jP9tc88iKJC6eCjk,53214
numpy/polynomial/legendre.pyi,sha256=IOzJVF0pQefwXsuBNhE7h5Ecib5SxKBJJ-Au2T4laU8,1224
numpy/polynomial/polynomial.py,sha256=oHeu-NnHthpJSer3Gjz9IdRwwFeX-nY8jhBfAmfIth4,50654
numpy/polynomial/polynomial.pyi,sha256=w4ocQ2Wf3wQWHSO2gklfPS1JVq-56Q_XUCxLLcjIces,1173
numpy/polynomial/polyutils.py,sha256=o08Hwi6uhw092NN2Z82f-p-yLP8QcGq6vjP-RMW9yu0,24026
numpy/polynomial/polyutils.pyi,sha256=_06GfKjfZuDuX0bgXqxWkx54FazIbwUQlNWZYjnnZpM,275
numpy/polynomial/setup.py,sha256=3MP1VT1AVy8_MdlhErP-lS0Rq5fActYCkxYeHJU88Vg,383
numpy/polynomial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/polynomial/tests/__pycache__/__init__.cpython-311.pyc,,
numpy/polynomial/tests/__pycache__/test_chebyshev.cpython-311.pyc,,
numpy/polynomial/tests/__pycache__/test_classes.cpython-311.pyc,,
numpy/polynomial/tests/__pycache__/test_hermite.cpython-311.pyc,,
numpy/polynomial/tests/__pycache__/test_hermite_e.cpython-311.pyc,,
numpy/polynomial/tests/__pycache__/test_laguerre.cpython-311.pyc,,
numpy/polynomial/tests/__pycache__/test_legendre.cpython-311.pyc,,
numpy/polynomial/tests/__pycache__/test_polynomial.cpython-311.pyc,,
numpy/polynomial/tests/__pycache__/test_polyutils.cpython-311.pyc,,
numpy/polynomial/tests/__pycache__/test_printing.cpython-311.pyc,,
numpy/polynomial/tests/__pycache__/test_symbol.cpython-311.pyc,,
numpy/polynomial/tests/test_chebyshev.py,sha256=PI2XwvGGqQKEB1RxbsYRgeTG0cunB_8Otd9SBJozq-8,21141
numpy/polynomial/tests/test_classes.py,sha256=TIoMhOcCJd7bAAs-LVNUToLFtYORWRU1H5CSwaPdySU,18931
numpy/polynomial/tests/test_hermite.py,sha256=zGYN24ia2xx4IH16D6sfAxIipnZrGrIe7D8QMJZPw4Y,19132
numpy/polynomial/tests/test_hermite_e.py,sha256=5ZBtGi2gkeldYVSh8xlQOLUDW6fcT4YdZiTrB6AaGJU,19467
numpy/polynomial/tests/test_laguerre.py,sha256=hBgo8w_3iEQosX2CqjTkUstTiuTPLZmfQNQtyKudZLo,18048
numpy/polynomial/tests/test_legendre.py,sha256=v3ajjp0sg1o7njoLhbPftxaIWaxpY0pBp1suImZqJMw,19241
numpy/polynomial/tests/test_polynomial.py,sha256=fE6-pAr1z4P1InHWUmekx7E_Mqqi8lJR43nIwgG_qKc,21140
numpy/polynomial/tests/test_polyutils.py,sha256=AQZZzxBCymhT1MTv8zCF_NC-nP0d9ybMNebT2d8P3j0,3700
numpy/polynomial/tests/test_printing.py,sha256=u2Ky3dkZQB_H_pwF2dVZY4jYVDlXhJBTjtnoUIvI-vc,21055
numpy/polynomial/tests/test_symbol.py,sha256=JHjxWjLYw3aOdjoOPendZyoXDbQMJ9D4lPiHBkdjddg,5587
numpy/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/LICENSE.md,sha256=tLwvT6HJV3jx7T3Y8UcGvs45lHW5ePnzS1081yUhtIo,3582
numpy/random/__init__.pxd,sha256=g3EaMi3yfmnqT-KEWj0cp6SWIxVN9ChFjEYXGOfOifE,445
numpy/random/__init__.py,sha256=W_hFzGsKVQfdh3-U15gzsOKKAk8uZgioDkxKyuou4WA,7721
numpy/random/__init__.pyi,sha256=DmuNxZkaSCfwkSXlC6FmXFTNk2WO3evediDFOAeozto,2215
numpy/random/__pycache__/__init__.cpython-311.pyc,,
numpy/random/__pycache__/_pickle.cpython-311.pyc,,
numpy/random/_bounded_integers.cp311-win_amd64.lib,sha256=jF2uV91QcG5LblZTuiBaaq07wY9kQlfi4sHqiknqAgA,18000
numpy/random/_bounded_integers.cp311-win_amd64.pyd,sha256=BdX-ilp5sdWDblgwev3QyFcKfE4e0LamKU85eNsNxsQ,257024
numpy/random/_bounded_integers.pxd,sha256=1PHg-kssdTSLrNm1P4rVlFpLcCkpOuRPeAnLDbvtzck,1754
numpy/random/_common.cp311-win_amd64.lib,sha256=3W_g4M-Fz36um07iKkYNn8woDFGbtdXcJhgVOV3QsDg,2012
numpy/random/_common.cp311-win_amd64.pyd,sha256=9BgJwD0TSH-olAzDD1_yElFD6_BxvKEOCB0CYCjENf0,175104
numpy/random/_common.pxd,sha256=nqoB92Xxen6tBTPP-NoXRH8NaUF1cWFKS24Px2CySys,5045
numpy/random/_examples/cffi/__pycache__/extending.cpython-311.pyc,,
numpy/random/_examples/cffi/__pycache__/parse.cpython-311.pyc,,
numpy/random/_examples/cffi/extending.py,sha256=BgydYEYBb6hDghMF-KQFVc8ssUU1F5Dg-3GyeilT3Vg,920
numpy/random/_examples/cffi/parse.py,sha256=rbi3NF6bhyk35yhgK1j8fFeqlfO9Om8vjS84Jg8GX20,1825
numpy/random/_examples/cython/extending.pyx,sha256=RmpxvFfGsAGZwCY78LWrfpa307NG7vrE64TIiIpKEA4,2368
numpy/random/_examples/cython/extending_distributions.pyx,sha256=1zrMvPbKi0RinyZ93Syyy4OXGEOzAAKHSzTmDtN09ZY,3987
numpy/random/_examples/cython/meson.build,sha256=lkS04wufbIxBitsQ-IKixMPPfLqCHhzdC6baXFjI620,1488
numpy/random/_examples/numba/__pycache__/extending.cpython-311.pyc,,
numpy/random/_examples/numba/__pycache__/extending_distributions.cpython-311.pyc,,
numpy/random/_examples/numba/extending.py,sha256=vnqUqQRvlAI-3VYDzIxSQDlb-smBAyj8fA1-M2IrOQw,2041
numpy/random/_examples/numba/extending_distributions.py,sha256=tU62JEW13VyNuBPhSpDWqd9W9ammHJCLv61apg90lMc,2101
numpy/random/_generator.cp311-win_amd64.lib,sha256=9HdZ5eROKqmeI3ik7aMof2t0jjPZnQX4-wKbDEJegMA,18400
numpy/random/_generator.cp311-win_amd64.pyd,sha256=ADx68mmaNw78GpC_Qqw7RJwn_5wksRE23SRb9Q7SJA8,695808
numpy/random/_generator.pyi,sha256=jjpnmhTQHylz5WbC1s8NSpdESaS0loditFPsEt-HHBw,23123
numpy/random/_mt19937.cp311-win_amd64.lib,sha256=i_d-EtJeqrr4ICVeDAGN-RVnMsnYuz4ldTg0UJFD22g,2032
numpy/random/_mt19937.cp311-win_amd64.pyd,sha256=NrvTdnpO_B5d3U-Wt7cF_GZOlaYpq75-O15ZUc3q0_A,75264
numpy/random/_mt19937.pyi,sha256=moJQemI0KlSzUyLJKy4d3lq6-yzWe-eKe0-ocybBjmY,746
numpy/random/_pcg64.cp311-win_amd64.lib,sha256=XegnHbwvwme6ZSkgpB9kGQPB_rCGgT17nNpgSDK9CYo,1996
numpy/random/_pcg64.cp311-win_amd64.pyd,sha256=4JpTszoZCKpOtYoHFm1b6v_iByzt7S-A31mDGt9_uN8,83456
numpy/random/_pcg64.pyi,sha256=Q-QetvAEmjzguUzTFe9WyNjouYT4AdB3t4TP7Rv_h9A,1133
numpy/random/_philox.cp311-win_amd64.lib,sha256=lcvef_ywOb3CXF6c8k26iKTVvq4wJQOHoEl2p-njkXo,2012
numpy/random/_philox.cp311-win_amd64.pyd,sha256=mnd_P5pZ89iN6E3D5JkTgzXDps877huHXZYm1s324Jg,70144
numpy/random/_philox.pyi,sha256=ON7UZsb8vsIHC8LnnEAe_pe5RNvs8AxY6QoyuGshArc,1014
numpy/random/_pickle.py,sha256=6dDxQu_mwdXdk1TXWi2-gYDLamILdfkB71oHl2Y2Wjc,2398
numpy/random/_sfc64.cp311-win_amd64.lib,sha256=nc32AOXArKfAOOn0iOPXAR5JCqYE0l7pSDoAO9fc4Lk,1996
numpy/random/_sfc64.cp311-win_amd64.pyd,sha256=GARTr-_v9kX5_bLeVKPLcti-y4eTbqguLXpWWSrKMGg,50688
numpy/random/_sfc64.pyi,sha256=P4SSo1zmzgVONfY79sLS0fASLZJ4hMwFz97akUzXLzs,737
numpy/random/bit_generator.cp311-win_amd64.lib,sha256=wvD7M6R-METNRWBt4uBxCei0FVtcjcj43N58WnCf_iY,2120
numpy/random/bit_generator.cp311-win_amd64.pyd,sha256=mtcT9qk8JrtzOpD4d7UNUdfyLu8WGqWOQHNaXOwUlQE,163840
numpy/random/bit_generator.pxd,sha256=LJpeB-EKeVV8_JO69sS33XJLZQ3DAhrUCNzs_ei7AoI,1042
numpy/random/bit_generator.pyi,sha256=oei_ZGpGtpjAIObqCMR-rfcaec45vwqsAekYGSVKoKE,3622
numpy/random/c_distributions.pxd,sha256=VnYwdkMQmLp2rU-fT0Dvj0AhLirSpE5EirMe7iNcTTQ,6464
numpy/random/lib/npyrandom.lib,sha256=1qZG6K4yMPYrogUdXPYBUupWLwcZruHGGhnPXJ2XKJY,147862
numpy/random/mtrand.cp311-win_amd64.lib,sha256=4mgqbksv8FJMzimvEbsmzlpklojYH1GFmnb32tDWYSQ,17122
numpy/random/mtrand.cp311-win_amd64.pyd,sha256=fdo8YNJXkcU8LsqZ6raWNHtqjuIPP4MH1--_CGy7xb8,596992
numpy/random/mtrand.pyi,sha256=EHiCds5Gki61nKeK9gchVPI6FnPQmv5B35TeEHtUeHk,20324
numpy/random/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/__pycache__/__init__.cpython-311.pyc,,
numpy/random/tests/__pycache__/test_direct.cpython-311.pyc,,
numpy/random/tests/__pycache__/test_extending.cpython-311.pyc,,
numpy/random/tests/__pycache__/test_generator_mt19937.cpython-311.pyc,,
numpy/random/tests/__pycache__/test_generator_mt19937_regressions.cpython-311.pyc,,
numpy/random/tests/__pycache__/test_random.cpython-311.pyc,,
numpy/random/tests/__pycache__/test_randomstate.cpython-311.pyc,,
numpy/random/tests/__pycache__/test_randomstate_regression.cpython-311.pyc,,
numpy/random/tests/__pycache__/test_regression.cpython-311.pyc,,
numpy/random/tests/__pycache__/test_seed_sequence.cpython-311.pyc,,
numpy/random/tests/__pycache__/test_smoke.cpython-311.pyc,,
numpy/random/tests/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/data/__pycache__/__init__.cpython-311.pyc,,
numpy/random/tests/data/mt19937-testset-1.csv,sha256=bA5uuOXgLpkAwJjfV8oUePg3-eyaH4-gKe8AMcl2Xn0,16845
numpy/random/tests/data/mt19937-testset-2.csv,sha256=SnOL1nyRbblYlC254PBUSc37NguV5xN-0W_B32IxDGE,16826
numpy/random/tests/data/pcg64-testset-1.csv,sha256=wHoS7fIR3hMEdta7MtJ8EpIWX-Bw1yfSaVxiC15vxVs,24840
numpy/random/tests/data/pcg64-testset-2.csv,sha256=6vlnVuW_4i6LEsVn6b40HjcBWWjoX5lboSCBDpDrzFs,24846
numpy/random/tests/data/pcg64dxsm-testset-1.csv,sha256=Fhha5-jrCmRk__rsvx6CbDFZ7EPc8BOPDTh-myZLkhM,24834
numpy/random/tests/data/pcg64dxsm-testset-2.csv,sha256=mNYzkCh0NMt1VvTrN08BbkpAbfkFxztNcsofgeW_0ns,24840
numpy/random/tests/data/philox-testset-1.csv,sha256=QvpTynWHQjqTz3P2MPvtMLdg2VnM6TGTpXgp-_LeJ5g,24853
numpy/random/tests/data/philox-testset-2.csv,sha256=-BNO1OCYtDIjnN5Q-AsQezBCGmVJUIs3qAMyj8SNtsA,24839
numpy/random/tests/data/sfc64-testset-1.csv,sha256=sgkemW0lbKJ2wh1sBj6CfmXwFYTqfAk152P0r8emO38,24841
numpy/random/tests/data/sfc64-testset-2.csv,sha256=mkp21SG8eCqsfNyQZdmiV41-xKcsV8eutT7rVnVEG50,24834
numpy/random/tests/test_direct.py,sha256=hI5cb5DnudObAWAoEoWAxEKd3WwMRE-kz9RrKpw2lU8,18297
numpy/random/tests/test_extending.py,sha256=MJ0_VpRPtak2Hl6hP5RXYIg6USBciHGPq6Ou8fWNTxA,4156
numpy/random/tests/test_generator_mt19937.py,sha256=_Sr01mNobOiO60n8VsHHWo9inQC0lpxj6BaZ-kwiUMs,118223
numpy/random/tests/test_generator_mt19937_regressions.py,sha256=v2MTG1vGv0Iuj91cLjSBNuWmPjGvkpLuJtH55YHh-No,6552
numpy/random/tests/test_random.py,sha256=ZAc1w0pL48xBqVY-zvBJrtdRfDZpnk4HpCYwNI51Iho,71837
numpy/random/tests/test_randomstate.py,sha256=XWTDAoxFERWUD2555aO1juxRcciSZSSo6kdZptWAC5I,87152
numpy/random/tests/test_randomstate_regression.py,sha256=MgTKKmJlf_U9I_rmCRpIvPjPsq9ZMW1qGomnIcWr6cw,8133
numpy/random/tests/test_regression.py,sha256=QZc3xP9sPfihkUF7LAzW8zE4AaJ0nionJoYO3M6pAdk,5588
numpy/random/tests/test_seed_sequence.py,sha256=zWUvhWDxBmTN2WteSFQeJ29W0-2k3ZUze_3YtL4Kgms,3391
numpy/random/tests/test_smoke.py,sha256=KggwK88hgKJyDscYQaODUCS7jLEk1QE9JW-jJRmvxig,29001
numpy/testing/__init__.py,sha256=ENc09IN_D74xNvH33Z65Q2dkaSEvljHF_tz-BV-g_dU,617
numpy/testing/__init__.pyi,sha256=b9A-EquujQt5BfM-Qim0Fqi1NGFpe5mkR4NcnMDDH68,1724
numpy/testing/__pycache__/__init__.cpython-311.pyc,,
numpy/testing/__pycache__/overrides.cpython-311.pyc,,
numpy/testing/__pycache__/print_coercion_tables.cpython-311.pyc,,
numpy/testing/__pycache__/setup.cpython-311.pyc,,
numpy/testing/_private/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/_private/__pycache__/__init__.cpython-311.pyc,,
numpy/testing/_private/__pycache__/extbuild.cpython-311.pyc,,
numpy/testing/_private/__pycache__/utils.cpython-311.pyc,,
numpy/testing/_private/extbuild.py,sha256=wjFcgZsEkqOhvGIV3ZW7B4lvrHDF63760al2c1cCCWQ,8265
numpy/testing/_private/utils.py,sha256=FaZ76g1azU8i00XkTQZVErBtFFZjneXC0QCiUname4w,87751
numpy/testing/_private/utils.pyi,sha256=Ijm2tURkVj8Xw4L_8v93PpYS3w2lWbiK6n1aU64xHMI,10525
numpy/testing/overrides.py,sha256=ohKSOjdHQFvsbpGZJuRtvM8WnFANtI1VBHfAzy9aejw,2206
numpy/testing/print_coercion_tables.py,sha256=1fOhWxSwwHCF6QrX76PCjfHguignkTKCyvpRTq4Gcwo,6380
numpy/testing/setup.py,sha256=wQPOZ8LI1xqkSe7vfY0apJHJwEyJ490uSveFh1wmTuk,730
numpy/testing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/tests/__pycache__/__init__.cpython-311.pyc,,
numpy/testing/tests/__pycache__/test_utils.cpython-311.pyc,,
numpy/testing/tests/test_utils.py,sha256=o55mKVoozlSagVrec_RVTdxrXWBzsj9t1oh-VL2hPJQ,57366
numpy/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/tests/__pycache__/__init__.cpython-311.pyc,,
numpy/tests/__pycache__/test__all__.cpython-311.pyc,,
numpy/tests/__pycache__/test_ctypeslib.cpython-311.pyc,,
numpy/tests/__pycache__/test_lazyloading.cpython-311.pyc,,
numpy/tests/__pycache__/test_matlib.cpython-311.pyc,,
numpy/tests/__pycache__/test_numpy_config.cpython-311.pyc,,
numpy/tests/__pycache__/test_numpy_version.cpython-311.pyc,,
numpy/tests/__pycache__/test_public_api.cpython-311.pyc,,
numpy/tests/__pycache__/test_reloading.cpython-311.pyc,,
numpy/tests/__pycache__/test_scripts.cpython-311.pyc,,
numpy/tests/__pycache__/test_warnings.cpython-311.pyc,,
numpy/tests/test__all__.py,sha256=JziA96KUyXwWCPExbQcJBqe_RU1xQVrVwi1xhO8tzqM,230
numpy/tests/test_ctypeslib.py,sha256=MtNBL_R6bwYFRl22mrTQQBsX62tEsooQXr8JMUHU1Dg,12627
numpy/tests/test_lazyloading.py,sha256=Z01x6jxk94e2HPoHdlHBgmgHjm9tNDE09kuJmT-DYFo,1200
numpy/tests/test_matlib.py,sha256=TUaQmGoz9fvQQ8FrooTq-g9BFiViGWjoTIGQSUUF6-Y,1910
numpy/tests/test_numpy_config.py,sha256=xp036ZX3-R20FjGn4-okdkFPjkThQhYdFtGt5MO65sI,1285
numpy/tests/test_numpy_version.py,sha256=NBiLZWngp4KhtoOaHjHjEIokzli8FBPcDvyNlNtP_Bs,1520
numpy/tests/test_public_api.py,sha256=q2DUDZol8j1fFkiRZ6MyTjeOBWWRXLdnxouWyrElVXw,18721
numpy/tests/test_reloading.py,sha256=iLpy0aMaCVIvGdBFXFmSbsyCR3eTSd3BJm34htq8RRw,2426
numpy/tests/test_scripts.py,sha256=6rZN5bnGpeR4vEjLBiKEUMXJiE2NVnbY1Q8xKPlOqA8,1692
numpy/tests/test_warnings.py,sha256=imaLQur-8UB-66AzEFwE0SvteAucxPIlNZe3mu1Y9JY,2358
numpy/typing/__init__.py,sha256=WIxbUYD8B-3grzzQVqfgYgipThc1NQSBpeNikv1Euaw,5409
numpy/typing/__pycache__/__init__.cpython-311.pyc,,
numpy/typing/__pycache__/mypy_plugin.cpython-311.pyc,,
numpy/typing/__pycache__/setup.cpython-311.pyc,,
numpy/typing/mypy_plugin.py,sha256=SWk6V6slaIfOTsZlV869uhe8vjWLHyVsK2VHnHL7pfc,6572
numpy/typing/setup.py,sha256=NpNqwxDwSxWBInw-7TJHIqEf3scDAczZwmzGxI_Ftn0,385
numpy/typing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/typing/tests/__pycache__/__init__.cpython-311.pyc,,
numpy/typing/tests/__pycache__/test_isfile.cpython-311.pyc,,
numpy/typing/tests/__pycache__/test_runtime.cpython-311.pyc,,
numpy/typing/tests/__pycache__/test_typing.cpython-311.pyc,,
numpy/typing/tests/data/fail/arithmetic.pyi,sha256=W3Uqw-5lzAqlYUh3h9SBZqr4HC4xmRxMuNaQyRMVUPw,3971
numpy/typing/tests/data/fail/array_constructors.pyi,sha256=xmYGgCnxTHZ0nv5C8y-uuPsOnsQKXQjahE7muldp5cM,1122
numpy/typing/tests/data/fail/array_like.pyi,sha256=MUIx6Oc5bJeebr-TC4FhZFXnX9pJ5gQDv8moHmPek10,471
numpy/typing/tests/data/fail/array_pad.pyi,sha256=JGCMd_sRBYlsPQ2d7EfLaNooTsg1P0jBuD5Ds2MeXAg,138
numpy/typing/tests/data/fail/arrayprint.pyi,sha256=tNk1nyxQq45G-87GS_QiTlNJSxP7kdItvBqzVD2bCKY,563
numpy/typing/tests/data/fail/arrayterator.pyi,sha256=u1pNRrRUM8Q0o131lqGjvTQRy8-eHeazSvRArhnAyOo,494
numpy/typing/tests/data/fail/bitwise_ops.pyi,sha256=VBOUHgafxOWJQ_tXLb0Phpu93FbLZIgo7mF1-EVUZ0c,535
numpy/typing/tests/data/fail/char.pyi,sha256=xCEKTpdp5Al-Qn-hxkp6i-Cgw7HEk53pt2XAzvpd58Y,2681
numpy/typing/tests/data/fail/chararray.pyi,sha256=_a695QkkSHZ9utlqUYwVUumC2QGhGxcZteB0iGlrFug,2358
numpy/typing/tests/data/fail/comparisons.pyi,sha256=VjriRnjoRGFUsyCheiGo1s5qErQ5ajQW7fCBltxp3Zc,915
numpy/typing/tests/data/fail/constants.pyi,sha256=esbeTGlTUSu3v3jMJ6GBZ_j7Q7RsyJRB47MaKNZaCOk,293
numpy/typing/tests/data/fail/datasource.pyi,sha256=B05CkL35mBUyKmuvi5MF3vTkZRwc0SOJo_r0cKVBBuA,410
numpy/typing/tests/data/fail/dtype.pyi,sha256=ltT4BFaX_KTVdRLw2dMg3_OiSNYjDSNrXsxby6eeLTw,354
numpy/typing/tests/data/fail/einsumfunc.pyi,sha256=WUzQB9DhN9Fgd14cWq5BXLqXeHgQn5KD7fSKwDPPa3g,551
numpy/typing/tests/data/fail/false_positives.pyi,sha256=TKmRWDjlfVP2rgZczUMXcm9l0maPLDf7bSBon4Xfakw,377
numpy/typing/tests/data/fail/flatiter.pyi,sha256=zVjvKxKlUr1T4PUWCZg1PsQEBmfLFtX5eH3Q50390oI,868
numpy/typing/tests/data/fail/fromnumeric.pyi,sha256=XoM1AY9Ys2_XyN-xEF9css9vV3FZsF78jsSecmIEwtQ,5752
numpy/typing/tests/data/fail/histograms.pyi,sha256=JteTXgK_kXD8UPdihMZ_T2VcM3rTBj6t-MMRP8UHvhw,379
numpy/typing/tests/data/fail/index_tricks.pyi,sha256=63ADYRCVtf0Dapc2dJpYJZDSIXK3MhhW_1lG30d3-RY,523
numpy/typing/tests/data/fail/lib_function_base.pyi,sha256=A_fZIktpi_2Q9zanq-Hq0zdKvMXyCfE6PpgUQNYILHY,2134
numpy/typing/tests/data/fail/lib_polynomial.pyi,sha256=PM1TD9h4tFNeMp4y6HlXHKuAHDW0bfNHw0UWLUHnLVk,928
numpy/typing/tests/data/fail/lib_utils.pyi,sha256=UGzb3HzY1lUdiwLcT_myFfL-nWedrkhxDt9mt68eokQ,289
numpy/typing/tests/data/fail/lib_version.pyi,sha256=JWtuTLcjkZpGfXshlFpJO5vINxawn9S-mxLGH0-7kcw,164
numpy/typing/tests/data/fail/linalg.pyi,sha256=j6GGpOENz0nuZsza0Dyfy6MtjfRltqrbY8K_7g5H92I,1370
numpy/typing/tests/data/fail/memmap.pyi,sha256=eAX-nEKtOb06mL8EPECukmL8MwrehSVRu5TBlHiSBaQ,164
numpy/typing/tests/data/fail/modules.pyi,sha256=xkoJ-zYQtGdeeUTjlOuBmLRkxFgGk_YGD74skrXWQtI,688
numpy/typing/tests/data/fail/multiarray.pyi,sha256=I4uoKVR-gsRtM577IrbEZbjyL0iGpRFPIKk3udj1xYM,1748
numpy/typing/tests/data/fail/ndarray.pyi,sha256=2I4smD6MlUD23Xx8Rt1gCtjj_m-tI5JEma-Z0unrgas,416
numpy/typing/tests/data/fail/ndarray_misc.pyi,sha256=ez2Wux0McE_cYgG4-6P5JgQCBV6-GthuhC9ywYdYAPY,1415
numpy/typing/tests/data/fail/nditer.pyi,sha256=We6p5_nmfUdd_4CtwYZc5O7MTSMyM-Xw7mEUzdKPcP4,333
numpy/typing/tests/data/fail/nested_sequence.pyi,sha256=7E1zJ2SZIF0ldbEmjtA_Bp6cV4Q-cS4Op0BJN3Vi3rc,444
numpy/typing/tests/data/fail/npyio.pyi,sha256=cSV87Xpy2bal531K81ZAyBqM0nZWZh7bvVu2yS6G5Tc,647
numpy/typing/tests/data/fail/numerictypes.pyi,sha256=exM5AUPCX3zYFlXvd-tG-_Riu1pmEfmhD44MdhvRx20,287
numpy/typing/tests/data/fail/random.pyi,sha256=d2JzeDW9mG4CKlF4S-foBmtB00y42toTDFcCw09bdKg,2891
numpy/typing/tests/data/fail/rec.pyi,sha256=BxH41lR1wLvLrlash9mzkPFngDAXSPQQXvuHxYylHAI,721
numpy/typing/tests/data/fail/scalars.pyi,sha256=Y5GCkuGb9qUDMbXrXGcG65W5R45Lo8ppVV75trzf6IY,3044
numpy/typing/tests/data/fail/shape_base.pyi,sha256=ZU1KSP0k-i-npwIMUhp42-EMzrdZhOqPEnV8ah-ZJ6U,160
numpy/typing/tests/data/fail/stride_tricks.pyi,sha256=L0fJGun6CDq24yNdw2zeNVGGcIpEOyP2dmWj1pEbMz8,324
numpy/typing/tests/data/fail/testing.pyi,sha256=O1nk5xnSvKn7aAHNi3mMLYIr75ym5WIT-BvZemEnayQ,1398
numpy/typing/tests/data/fail/twodim_base.pyi,sha256=jPdRkTn8fm_YTnZwOUZ-yNYE2fJT5X5rp56rlhjjovw,936
numpy/typing/tests/data/fail/type_check.pyi,sha256=0KG0c2LNUbUFChTYtbJ38eJUmfvUJl4Cn5G0vh1Bkrw,392
numpy/typing/tests/data/fail/ufunc_config.pyi,sha256=V5R7wY_P7KWn4IRxbLx42b1YF3wbIYzHEMr9BC41JHE,754
numpy/typing/tests/data/fail/ufunclike.pyi,sha256=wU7hNwN9SMRC8pMxOyDteWoCC4pDOdwfvk_ZCyhAY-c,700
numpy/typing/tests/data/fail/ufuncs.pyi,sha256=TiIj3qjjbAgNR0IahyYUGXDTA8AlSJLIKhDrfyzAHFw,1388
numpy/typing/tests/data/fail/warnings_and_errors.pyi,sha256=I96g0czQxXry3qxXChQ3BMwJJc79W6HXgBvYiyRmSXI,179
numpy/typing/tests/data/misc/extended_precision.pyi,sha256=RTsXUAM9iKX_L-iviwFVuUwKcqX9N8sRW5ZHAXjYtjc,909
numpy/typing/tests/data/mypy.ini,sha256=NS3L7RuRTKn_p2FwrL3tT4BCpmLxP7uLE_DGb0GfG_g,113
numpy/typing/tests/data/pass/__pycache__/arithmetic.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/array_constructors.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/array_like.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/arrayprint.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/arrayterator.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/bitwise_ops.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/comparisons.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/dtype.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/einsumfunc.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/flatiter.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/fromnumeric.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/index_tricks.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/lib_utils.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/lib_version.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/literal.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/mod.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/modules.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/multiarray.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_conversion.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_misc.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_shape_manipulation.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/numeric.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/numerictypes.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/random.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/scalars.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/simple.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/simple_py3.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufunc_config.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufunclike.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufuncs.cpython-311.pyc,,
numpy/typing/tests/data/pass/__pycache__/warnings_and_errors.cpython-311.pyc,,
numpy/typing/tests/data/pass/arithmetic.py,sha256=G88j1QuZwNp5DeCsZjXKRPpYVx60EVlBS-VSdZuB2-Q,8049
numpy/typing/tests/data/pass/array_constructors.py,sha256=lIy-MTR2YZZpVBTKndCEUHj-BtWGf21fN2yc2MVwSvE,2556
numpy/typing/tests/data/pass/array_like.py,sha256=mVIaUxN1HXw8Me_bK-TFFfRQRTPGj-TGakMG6NbCIso,957
numpy/typing/tests/data/pass/arrayprint.py,sha256=NTw1gJ9v3TDVwRov4zsg_27rI-ndKuG4mDidBWEKVyc,803
numpy/typing/tests/data/pass/arrayterator.py,sha256=z4o0H08T7tbzzMWhu5ZXdVqbivjBicuFgRHBk_lpOck,420
numpy/typing/tests/data/pass/bitwise_ops.py,sha256=zYz-ZNXpxjXDdo4fYWv_RQZq5af581dnaZrlu0-sSC8,1101
numpy/typing/tests/data/pass/comparisons.py,sha256=phjskmrmZ0bHTGLyNvo3mDuHCGR6I1OOj6S8ks71wRQ,3293
numpy/typing/tests/data/pass/dtype.py,sha256=hzkco2RrsTNo9CdhkkZdEnsytLBlSgmaRgmlbsQd_hE,1126
numpy/typing/tests/data/pass/einsumfunc.py,sha256=CXdLvQsU2iDqQc7d2TRRCSwguQzJ0SJDFn23SDeOOuY,1406
numpy/typing/tests/data/pass/flatiter.py,sha256=2xtMPvDgfhgjZIqiN3B3Wvy6Q9oBeo9uh4UkCAQNmwg,190
numpy/typing/tests/data/pass/fromnumeric.py,sha256=eM6RUBjVInsShXlblqq07-I0QwoST8n6g8WWuPSgYtA,4002
numpy/typing/tests/data/pass/index_tricks.py,sha256=Vn5iEuWlNdbr03zMEwAHvjBgI25-uCqRAJfUvRVWSp0,1556
numpy/typing/tests/data/pass/lib_utils.py,sha256=AAopNI74rCGO9OiuVTEjdM6JZvCFZ5aue1bQxxJODEM,462
numpy/typing/tests/data/pass/lib_version.py,sha256=TlLZK8sekCMm__WWo22FZfZc40zpczENf6y_TNjBpCw,317
numpy/typing/tests/data/pass/literal.py,sha256=7X2GZIjTiE-WUrBCKNi1FesDI_ENCc9RY_Lf3X2UVUE,1378
numpy/typing/tests/data/pass/mod.py,sha256=Intb9Ni_LlHhEka8kk81-601JCOl85jz_4_BQDA6iYI,1727
numpy/typing/tests/data/pass/modules.py,sha256=UZnvViLGUfRQVCR8-OJ7MWphQBlArWPBcoUgaz-86IM,618
numpy/typing/tests/data/pass/multiarray.py,sha256=i6VU-VN96Q16mRGzVoY3oTE2W1z16GOGTOVFxWGRacM,1407
numpy/typing/tests/data/pass/ndarray_conversion.py,sha256=-1iJDSvdD86k3yJCrWf1nouQrRHStf4cheiZ5OHFE78,1720
numpy/typing/tests/data/pass/ndarray_misc.py,sha256=IV7UAqhR27c3UhsQ6H956EW5ysPJubZA2n2iDai72KY,2900
numpy/typing/tests/data/pass/ndarray_shape_manipulation.py,sha256=yaBK3hW5fe2VpvARkn_NMeF-JX-OajI8JiRWOA_Uk7Y,687
numpy/typing/tests/data/pass/numeric.py,sha256=8AiyBs48ADbckFLK2JYyuEpnGi7G59cpQh-BBtaCUxc,1580
numpy/typing/tests/data/pass/numerictypes.py,sha256=67q4kLs4ETZukLV-RZVDUXl7QuvJtLjh_zeDKCklcKU,792
numpy/typing/tests/data/pass/random.py,sha256=sPsOvmCpLVr4_WVbEb11GS0mFdh0PQAknLTv0vxuP40,63380
numpy/typing/tests/data/pass/scalars.py,sha256=PeVMO9i7vD27bw5zK_V0CKF0up4Ed8OECOpmT3oQhRY,3727
numpy/typing/tests/data/pass/simple.py,sha256=btjJPbgU-Z-_ixzfavCvdlxtDZrpJZXYdPGS-v8R_1w,2841
numpy/typing/tests/data/pass/simple_py3.py,sha256=OBpoDmf5u4bRblugokiOZzufESsEmoU03MqipERrjLg,102
numpy/typing/tests/data/pass/ufunc_config.py,sha256=H-nZa9XGzdUdqt9BWDdUub4mFx75r_pnagjb_H_eTp8,1268
numpy/typing/tests/data/pass/ufunclike.py,sha256=vhQJlqPAZegEyC882iIU0uCqoA7ijTOZP1Vij-MrgEI,1085
numpy/typing/tests/data/pass/ufuncs.py,sha256=xGuFo9gOvT04Pj7iNOhRIAIj_NHSTykIFyGV2Oj_0yA,479
numpy/typing/tests/data/pass/warnings_and_errors.py,sha256=84SQc0-Le9MIy9rC9pQ4JchXsaaRNY1sqKwxxEaMNuE,156
numpy/typing/tests/data/reveal/arithmetic.pyi,sha256=TSpoLun23mEZVvt4--ftprpS1JaxyhiOyYqLyB9P_uU,20281
numpy/typing/tests/data/reveal/array_constructors.pyi,sha256=JwsMb24Gv4vLZ_DIHFBQb2KVEfnCKlT2JZxi9atS6EI,10821
numpy/typing/tests/data/reveal/arraypad.pyi,sha256=jNYVO_bhcfiS4qD2yjdh9zHHfYwTXlpbS0S4monXg3A,804
numpy/typing/tests/data/reveal/arrayprint.pyi,sha256=-4CoLI3sTRn0-XRMpRuhEaaOZdGejzvyqU1CXp0GaKI,906
numpy/typing/tests/data/reveal/arraysetops.pyi,sha256=jcp6vcFce_PVI-6s9gKmvV5EKU7grqEbQSSOLf79-fM,4223
numpy/typing/tests/data/reveal/arrayterator.pyi,sha256=k6ZI2RkUSOKULJVGciGWcifVB3VkaYKKW2kKj3lWCxY,1144
numpy/typing/tests/data/reveal/bitwise_ops.pyi,sha256=C3Q7yxFnySOPxA1VSAmxi7SEJi51ZpzMys_oTBeLthU,4054
numpy/typing/tests/data/reveal/char.pyi,sha256=zXQoYyUQXZG7b3VC9-DNkpjVIWnhpzQehao1GuyuIrc,7443
numpy/typing/tests/data/reveal/chararray.pyi,sha256=028eTc550lglLovE18DL2yx29yX60gUx6HnD9mD8Xkw,6233
numpy/typing/tests/data/reveal/comparisons.pyi,sha256=0PXqHMy_Q7KXwWD_211cuOY7wrjzY2jJEA8hkzsRHE0,7738
numpy/typing/tests/data/reveal/constants.pyi,sha256=_4sG_sWr15v192z4fbkfAgfO8YbdzQ-Nq5fUjfHtmdU,2010
numpy/typing/tests/data/reveal/ctypeslib.pyi,sha256=srVzKX1mch-xf0lWJhvajhqiA8YzAhYDuC4nWFAIjNY,4822
numpy/typing/tests/data/reveal/datasource.pyi,sha256=U6TMsfJtoocNxj_682JLgNEh-pNPTK85E8yWycnt32U,700
numpy/typing/tests/data/reveal/dtype.pyi,sha256=AIY5oY1wV1Xijn19HnYzNtifYxdGiQoXIIN9rRSuWhk,2957
numpy/typing/tests/data/reveal/einsumfunc.pyi,sha256=Hiz6uEzKhrdLZ5mLQouwwtzHSlqKq37UowcC_wxCgQY,2089
numpy/typing/tests/data/reveal/emath.pyi,sha256=cwFfmjIs6QoK7OrsaNUxWbshIRJCramjX4BM37n0LsM,2483
numpy/typing/tests/data/reveal/false_positives.pyi,sha256=BvCw4kn5DV8oDobFzgkpSJEUow1Hdh-FsAQZz0m29-A,500
numpy/typing/tests/data/reveal/fft.pyi,sha256=f5y4zbqvnby1QfKPxp-sVz4CEULTLwbxcBys_nToPJ0,1792
numpy/typing/tests/data/reveal/flatiter.pyi,sha256=lEqnvh5cCnjvsEilXjNEt6e3DEhseDrXlmZSxfb9w48,913
numpy/typing/tests/data/reveal/fromnumeric.pyi,sha256=K-1XmRsAfpsPCFfPpy2hdtTNyTo4G3RZUt2ERH88YuE,12406
numpy/typing/tests/data/reveal/getlimits.pyi,sha256=7f6assbCeq6iXRtSRqN35JNPx3udRYlTQiv4VnRKEho,1648
numpy/typing/tests/data/reveal/histograms.pyi,sha256=7F-UTBLfFGCJAAFDDp2raQJxbl1Hixwfb3h3sUq1sto,1330
numpy/typing/tests/data/reveal/index_tricks.pyi,sha256=Hre8emI2pT6J2eT2hvflZeZjBSP3Yg7C_2SLugy9PXw,3251
numpy/typing/tests/data/reveal/lib_function_base.pyi,sha256=Q6ewi_UxcilM2R4JRSLNpp9x0hjNnfYU5oH8q62pBXA,8503
numpy/typing/tests/data/reveal/lib_polynomial.pyi,sha256=lH7uci1UuK9acO6xN91euuURTTF1_0YV2umT9rwRNB4,6136
numpy/typing/tests/data/reveal/lib_utils.pyi,sha256=_Xdgg2USAL8HKDSLb_d49u58axdPUjZL7KE6QoqPxuI,1090
numpy/typing/tests/data/reveal/lib_version.pyi,sha256=GEI8Jy3t3L3C3nmh5NARPK_Duxpn53RmBs59MXkLmOc,697
numpy/typing/tests/data/reveal/linalg.pyi,sha256=qgtzr1GRIy0p9_Sy-zxpXT3nID00Jvg2_EQ4TmDskvk,4983
numpy/typing/tests/data/reveal/matrix.pyi,sha256=zagRWH9su-lDjs0Z1fl0fvl5Rn4BeY10PJ1hwj2bsJY,2998
numpy/typing/tests/data/reveal/memmap.pyi,sha256=6yhQzVryBCRhJkwsZsqcQNZZYfN-HHZ0F97EOa9SjrM,867
numpy/typing/tests/data/reveal/mod.pyi,sha256=GR6yVQK536PQG1RCdt-RoG7PBdYYNRElWoIU8TdnNJY,5814
numpy/typing/tests/data/reveal/modules.pyi,sha256=Q4486mAOwGhMcsV8qSm1eNNsHC-4egMYODqLMpXrEa8,2014
numpy/typing/tests/data/reveal/multiarray.pyi,sha256=6mJYCWA6XTrgvd3JNpZM86mSOoiMYbF1jWVd-7uybr4,5476
numpy/typing/tests/data/reveal/nbit_base_example.pyi,sha256=XsPLKvd6Xg0EfoeT9AP8ZLc24Ml14TOpEA3hdq4nFJY,684
numpy/typing/tests/data/reveal/ndarray_conversion.pyi,sha256=wvmJ9FmcbAdXShhRRLNjSPeUymetQFVqBqjGUVn7eUg,1850
numpy/typing/tests/data/reveal/ndarray_misc.pyi,sha256=UA1GE0NCvouLXHyF_RAtMoW2IakLWOayrWt4u47k1rc,7352
numpy/typing/tests/data/reveal/ndarray_shape_manipulation.pyi,sha256=_hTO6zBAPMK-y1N9UU1CHweJhCUPcNqjXux_X8u0xig,1277
numpy/typing/tests/data/reveal/nditer.pyi,sha256=MNWZpoW7tuZmBKml94OIFB3vNAWDPgfSvsWrtlYmCcU,2076
numpy/typing/tests/data/reveal/nested_sequence.pyi,sha256=8eO-nOkd1p2u3KS3TvLAU5V53TBeFNIQzqJJt_x4UyM,766
numpy/typing/tests/data/reveal/npyio.pyi,sha256=F4ciLbNfBAEnuaEpciBHkOAFb7aPy1pw15KNSAW52OI,4311
numpy/typing/tests/data/reveal/numeric.pyi,sha256=HOdDfs-KFkyYX6m1J40yeatdrD14phLkekXQCoouAgw,6301
numpy/typing/tests/data/reveal/numerictypes.pyi,sha256=nuHevkq1fOUYHBKyMadFTGGQwxtjjuVkjlFN9Y-KuAA,2571
numpy/typing/tests/data/reveal/random.pyi,sha256=AsAYVotjcWhEFx3vn-Hi95KdoFeCuCLC55wGH32jchQ,105646
numpy/typing/tests/data/reveal/rec.pyi,sha256=tUqe-PgpkifUeuMV9bESn8t3SiJ2CZI63fX4YDzp6dQ,4025
numpy/typing/tests/data/reveal/scalars.pyi,sha256=BbuPV73Oks-EM2fjm2PswCNOWCyKivveFuYW4Q-btjI,4952
numpy/typing/tests/data/reveal/shape_base.pyi,sha256=_G6pnVKMjl6wmGU92RTdAqm-QEek_Ba3qB68WrYXrV0,2492
numpy/typing/tests/data/reveal/stride_tricks.pyi,sha256=eQo62-EVJxdm-IlPntykP8dccNSp0HsJwi22rJkTb8Y,1578
numpy/typing/tests/data/reveal/testing.pyi,sha256=aYyDKU3xgpGzX1H1TXmQar8-msF-khuEIUCNhQRiVR8,8814
numpy/typing/tests/data/reveal/twodim_base.pyi,sha256=nf9Id9mNxsgw7h4kmpMG2Ofk1_D3sid3XlzbhXyfWvI,3231
numpy/typing/tests/data/reveal/type_check.pyi,sha256=0oisNihLFRAnN4XuY7-NFREeiUA5DOk3QRPvbI47mi4,3131
numpy/typing/tests/data/reveal/ufunc_config.pyi,sha256=eAxs_pQVwELU1t5Xsg54-38ojWyl1-3x9WaFnYCEIJI,1368
numpy/typing/tests/data/reveal/ufunclike.pyi,sha256=EWrPF84UbRLQXC7cMxWxoIuqNiQNCq0MeQiu9sPOjD4,1366
numpy/typing/tests/data/reveal/ufuncs.pyi,sha256=Bbc6Z2AWFbk-pb1WQYtGJw1uKx6_eVxCulJfmLk2B-k,2874
numpy/typing/tests/data/reveal/warnings_and_errors.pyi,sha256=YL5y1Xi0bysN4D7_FK7vAJXW1qgifrmD-1YuRNpbyVM,554
numpy/typing/tests/test_isfile.py,sha256=u9fSF4O1jVwkMYE1HcQvyGw7nECzNSa6r6p2iDw3HpM,896
numpy/typing/tests/test_runtime.py,sha256=p-Ydvt0Rt6mPHmAKYOOAGxxXQnjoARJSVZmViKMAX0A,3384
numpy/typing/tests/test_typing.py,sha256=zejEYjqkAJ4m2Brd_v8Ecu5FOJ8uWhoy9RuG0uMFRV4,9043
numpy/version.py,sha256=mpTT1wnrnIn2DzePYE_wkEx7Fv8iNtOL7a5JxGYXMxE,224
