About the Copyright Holders
===========================

*   Copyright (c) 2008-2011 AQR Capital Management, LLC

    AQR Capital Management began pandas development in 2008. Development was
    led by <PERSON>. AQR released the source under this license in 2009.
*   Copyright (c) 2011-2012, Lambda Foundry, Inc.

    Wes is now an employee of Lambda Foundry, and remains the pandas project
    lead.
*   Copyright (c) 2011-2012, PyData Development Team

    The PyData Development Team is the collection of developers of the PyData
    project. This includes all of the PyData sub-projects, including pandas. The
    core team that coordinates development on GitHub can be found here:
    https://github.com/pydata.

Full credits for pandas contributors can be found in the documentation.

Our Copyright Policy
====================

PyData uses a shared copyright model. Each contributor maintains copyright
over their contributions to PyData. However, it is important to note that
these contributions are typically only changes to the repositories. Thus,
the PyData source code, in its entirety, is not the copyright of any single
person or institution. Instead, it is the collective copyright of the
entire PyData Development Team. If individual contributors want to maintain
a record of what changes/contributions they have specific copyright on,
they should indicate their copyright in the commit message of the change
when they commit the change to one of the PyData repositories.

With this in mind, the following banner should be used in any source code
file to indicate the copyright and license terms:

```
#-----------------------------------------------------------------------------
# Copyright (c) 2012, PyData Development Team
# All rights reserved.
#
# Distributed under the terms of the BSD Simplified License.
#
# The full license is in the LICENSE file, distributed with this software.
#-----------------------------------------------------------------------------
```

Other licenses can be found in the LICENSES directory.

License
=======

pandas is distributed under a 3-clause ("Simplified" or "New") BSD
license. Parts of NumPy, SciPy, numpydoc, bottleneck, which all have
BSD-compatible licenses, are included. Their licenses follow the pandas
license.
