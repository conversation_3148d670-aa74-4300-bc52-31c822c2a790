
# This file was generated by 'versioneer.py' (0.28) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2023-06-28T14:27:24-0700",
 "dirty": false,
 "error": null,
 "full-revisionid": "0f437949513225922d851e9581723d82120684a6",
 "version": "2.0.3"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
