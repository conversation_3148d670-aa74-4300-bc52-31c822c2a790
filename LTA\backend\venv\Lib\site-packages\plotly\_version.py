
# This file was generated by 'versioneer.py' (0.21) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2023-06-08T10:48:38-0400",
 "dirty": false,
 "error": null,
 "full-revisionid": "e42897d4be454be23004b3d1f8704386b57773dc",
 "version": "5.15.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
