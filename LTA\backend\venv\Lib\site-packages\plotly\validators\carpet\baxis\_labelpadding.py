import _plotly_utils.basevalidators


class LabelpaddingValidator(_plotly_utils.basevalidators.IntegerValidator):
    def __init__(
        self, plotly_name="labelpadding", parent_name="carpet.baxis", **kwargs
    ):
        super(LabelpaddingValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "calc"),
            **kwargs,
        )
