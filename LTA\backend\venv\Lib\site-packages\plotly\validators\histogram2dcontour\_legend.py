import _plotly_utils.basevalidators


class LegendValidator(_plotly_utils.basevalidators.SubplotidValidator):
    def __init__(
        self, plotly_name="legend", parent_name="histogram2dcontour", **kwargs
    ):
        super(LegendValida<PERSON>, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            dflt=kwargs.pop("dflt", "legend"),
            edit_type=kwargs.pop("edit_type", "style"),
            **kwargs,
        )
