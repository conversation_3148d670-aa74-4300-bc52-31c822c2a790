import _plotly_utils.basevalidators


class VertexnormalsepsilonValidator(_plotly_utils.basevalidators.NumberValidator):
    def __init__(
        self,
        plotly_name="vertexnormalsepsilon",
        parent_name="isosurface.lighting",
        **kwargs,
    ):
        super(VertexnormalsepsilonValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "calc"),
            max=kwargs.pop("max", 1),
            min=kwargs.pop("min", 0),
            **kwargs,
        )
