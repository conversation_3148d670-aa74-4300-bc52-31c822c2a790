import _plotly_utils.basevalidators


class HiddenlabelsValidator(_plotly_utils.basevalidators.DataArrayValidator):
    def __init__(self, plotly_name="hiddenlabels", parent_name="layout", **kwargs):
        super(HiddenlabelsValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "calc"),
            **kwargs,
        )
