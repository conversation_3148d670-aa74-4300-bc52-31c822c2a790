import sys
from typing import TYPE_CHECKING

if sys.version_info < (3, 7) or TYPE_CHECKING:
    from ._opacity import OpacityValidator
    from ._line import LineValidator
    from ._layer import LayerValidator
    from ._label import LabelValidator
    from ._fillrule import <PERSON><PERSON><PERSON><PERSON>Valida<PERSON>
    from ._fillcolor import <PERSON>llcolorValida<PERSON>
    from ._drawdirection import DrawdirectionValidator
else:
    from _plotly_utils.importers import relative_import

    __all__, __getattr__, __dir__ = relative_import(
        __name__,
        [],
        [
            "._opacity.OpacityValidator",
            "._line.LineValidator",
            "._layer.LayerValidator",
            "._label.LabelValidator",
            "._fillrule.FillruleValidator",
            "._fillcolor.FillcolorValidator",
            "._drawdirection.DrawdirectionValidator",
        ],
    )
