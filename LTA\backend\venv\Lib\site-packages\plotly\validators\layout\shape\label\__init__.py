import sys
from typing import TYPE_CHECKING

if sys.version_info < (3, 7) or TYPE_CHECKING:
    from ._yanchor import Yan<PERSON>Validator
    from ._xanchor import <PERSON>an<PERSON>Validator
    from ._texttemplate import TexttemplateValidator
    from ._textposition import Text<PERSON>V<PERSON>da<PERSON>
    from ._textangle import Textang<PERSON>V<PERSON>da<PERSON>
    from ._text import TextValidator
    from ._padding import PaddingValidator
    from ._font import FontValidator
else:
    from _plotly_utils.importers import relative_import

    __all__, __getattr__, __dir__ = relative_import(
        __name__,
        [],
        [
            "._yanchor.YanchorValidator",
            "._xanchor.XanchorValidator",
            "._texttemplate.TexttemplateValidator",
            "._textposition.TextpositionValidator",
            "._textangle.TextangleValidator",
            "._text.TextValidator",
            "._padding.PaddingValidator",
            "._font.FontValidator",
        ],
    )
