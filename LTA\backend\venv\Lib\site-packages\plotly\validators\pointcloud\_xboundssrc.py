import _plotly_utils.basevalidators


class XboundssrcValidator(_plotly_utils.basevalidators.SrcValidator):
    def __init__(self, plotly_name="xboundssrc", parent_name="pointcloud", **kwargs):
        super(XboundssrcValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "none"),
            **kwargs,
        )
