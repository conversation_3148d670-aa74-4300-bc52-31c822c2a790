import sys
from typing import TYPE_CHECKING

if sys.version_info < (3, 7) or TYPE_CHECKING:
    from ._width import WidthV<PERSON>da<PERSON>
    from ._smoothing import Smoothing<PERSON><PERSON>da<PERSON>
    from ._simplify import SimplifyV<PERSON>da<PERSON>
    from ._shape import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    from ._dash import <PERSON>V<PERSON>da<PERSON>
    from ._color import <PERSON>Validator
    from ._backoffsrc import BackoffsrcValidator
    from ._backoff import BackoffValidator
else:
    from _plotly_utils.importers import relative_import

    __all__, __getattr__, __dir__ = relative_import(
        __name__,
        [],
        [
            "._width.WidthValidator",
            "._smoothing.SmoothingValidator",
            "._simplify.SimplifyValidator",
            "._shape.ShapeValidator",
            "._dash.DashValidator",
            "._color.ColorValidator",
            "._backoffsrc.BackoffsrcValidator",
            "._backoff.BackoffValidator",
        ],
    )
