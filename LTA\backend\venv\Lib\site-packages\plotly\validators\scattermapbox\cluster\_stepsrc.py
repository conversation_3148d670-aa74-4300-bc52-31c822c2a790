import _plotly_utils.basevalidators


class StepsrcValidator(_plotly_utils.basevalidators.SrcValidator):
    def __init__(
        self, plotly_name="stepsrc", parent_name="scattermapbox.cluster", **kwargs
    ):
        super(StepsrcValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "none"),
            **kwargs,
        )
