import _plotly_utils.basevalidators


class NamelengthsrcValidator(_plotly_utils.basevalidators.SrcValidator):
    def __init__(
        self,
        plotly_name="namelengthsrc",
        parent_name="scattermapbox.hoverlabel",
        **kwargs,
    ):
        super(NamelengthsrcValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "none"),
            **kwargs,
        )
