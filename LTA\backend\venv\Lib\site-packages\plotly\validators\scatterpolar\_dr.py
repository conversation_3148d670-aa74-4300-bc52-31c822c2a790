import _plotly_utils.basevalidators


class DrValidator(_plotly_utils.basevalidators.NumberValidator):
    def __init__(self, plotly_name="dr", parent_name="scatterpolar", **kwargs):
        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "calc"),
            **kwargs,
        )
