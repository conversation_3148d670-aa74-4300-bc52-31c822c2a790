import _plotly_utils.basevalidators


class RValidator(_plotly_utils.basevalidators.DataArrayValidator):
    def __init__(self, plotly_name="r", parent_name="scatterpolar", **kwargs):
        super(R<PERSON>alida<PERSON>, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "calc+clearAxisTypes"),
            **kwargs,
        )
