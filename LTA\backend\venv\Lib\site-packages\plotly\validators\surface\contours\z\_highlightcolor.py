import _plotly_utils.basevalidators


class HighlightcolorValidator(_plotly_utils.basevalidators.ColorValidator):
    def __init__(
        self, plotly_name="highlightcolor", parent_name="surface.contours.z", **kwargs
    ):
        super(HighlightcolorValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "calc"),
            **kwargs,
        )
