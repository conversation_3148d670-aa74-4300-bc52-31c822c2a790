import _plotly_utils.basevalidators


class MarkerValidator(_plotly_utils.basevalidators.CompoundValidator):
    def __init__(self, plotly_name="marker", parent_name="treemap", **kwargs):
        super(<PERSON><PERSON><PERSON>alida<PERSON>, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            data_class_str=kwargs.pop("data_class_str", "Marker"),
            data_docs=kwargs.pop(
                "data_docs",
                """
            autocolorscale
                Determines whether the colorscale is a default
                palette (`autocolorscale: true`) or the palette
                determined by `marker.colorscale`. Has an
                effect only if colors is set to a numerical
                array. In case `colorscale` is unspecified or
                `autocolorscale` is true, the default palette
                will be chosen according to whether numbers in
                the `color` array are all positive, all
                negative or mixed.
            cauto
                Determines whether or not the color domain is
                computed with respect to the input data (here
                colors) or the bounds set in `marker.cmin` and
                `marker.cmax` Has an effect only if colors is
                set to a numerical array. Defaults to `false`
                when `marker.cmin` and `marker.cmax` are set by
                the user.
            cmax
                Sets the upper bound of the color domain. Has
                an effect only if colors is set to a numerical
                array. Value should have the same units as
                colors and if set, `marker.cmin` must be set as
                well.
            cmid
                Sets the mid-point of the color domain by
                scaling `marker.cmin` and/or `marker.cmax` to
                be equidistant to this point. Has an effect
                only if colors is set to a numerical array.
                Value should have the same units as colors. Has
                no effect when `marker.cauto` is `false`.
            cmin
                Sets the lower bound of the color domain. Has
                an effect only if colors is set to a numerical
                array. Value should have the same units as
                colors and if set, `marker.cmax` must be set as
                well.
            coloraxis
                Sets a reference to a shared color axis.
                References to these shared color axes are
                "coloraxis", "coloraxis2", "coloraxis3", etc.
                Settings for these shared color axes are set in
                the layout, under `layout.coloraxis`,
                `layout.coloraxis2`, etc. Note that multiple
                color scales can be linked to the same color
                axis.
            colorbar
                :class:`plotly.graph_objects.treemap.marker.Col
                orBar` instance or dict with compatible
                properties
            colors
                Sets the color of each sector of this trace. If
                not specified, the default trace color set is
                used to pick the sector colors.
            colorscale
                Sets the colorscale. Has an effect only if
                colors is set to a numerical array. The
                colorscale must be an array containing arrays
                mapping a normalized value to an rgb, rgba,
                hex, hsl, hsv, or named color string. At
                minimum, a mapping for the lowest (0) and
                highest (1) values are required. For example,
                `[[0, 'rgb(0,0,255)'], [1, 'rgb(255,0,0)']]`.
                To control the bounds of the colorscale in
                color space, use `marker.cmin` and
                `marker.cmax`. Alternatively, `colorscale` may
                be a palette name string of the following list:
                Blackbody,Bluered,Blues,Cividis,Earth,Electric,
                Greens,Greys,Hot,Jet,Picnic,Portland,Rainbow,Rd
                Bu,Reds,Viridis,YlGnBu,YlOrRd.
            colorssrc
                Sets the source reference on Chart Studio Cloud
                for `colors`.
            cornerradius
                Sets the maximum rounding of corners (in px).
            depthfade
                Determines if the sector colors are faded
                towards the background from the leaves up to
                the headers. This option is unavailable when a
                `colorscale` is present, defaults to false when
                `marker.colors` is set, but otherwise defaults
                to true. When set to "reversed", the fading
                direction is inverted, that is the top elements
                within hierarchy are drawn with fully saturated
                colors while the leaves are faded towards the
                background color.
            line
                :class:`plotly.graph_objects.treemap.marker.Lin
                e` instance or dict with compatible properties
            pad
                :class:`plotly.graph_objects.treemap.marker.Pad
                ` instance or dict with compatible properties
            pattern
                Sets the pattern within the marker.
            reversescale
                Reverses the color mapping if true. Has an
                effect only if colors is set to a numerical
                array. If true, `marker.cmin` will correspond
                to the last color in the array and
                `marker.cmax` will correspond to the first
                color.
            showscale
                Determines whether or not a colorbar is
                displayed for this trace. Has an effect only if
                colors is set to a numerical array.
""",
            ),
            **kwargs,
        )
