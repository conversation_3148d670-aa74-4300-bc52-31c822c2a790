c     This file was generated by _generate_pyx.py.
c     Do not edit this file directly.

      subroutine chla_transtypewrp(
     +    ret,
     +    trans
     +    )
        external chla_transtype
        character chla_transtype
        character ret
        integer trans
        ret = chla_transtype(
     +    trans
     +    )
      end

      subroutine cladivwrp(
     +    ret,
     +    x,
     +    y
     +    )
        external wcladiv
        complex wcladiv
        complex ret
        complex x
        complex y
        ret = wcladiv(
     +    x,
     +    y
     +    )
      end

      subroutine clangbwrp(
     +    ret,
     +    norm,
     +    n,
     +    kl,
     +    ku,
     +    ab,
     +    ldab,
     +    work
     +    )
        external clangb
        real clangb
        real ret
        character norm
        integer n
        integer kl
        integer ku
        complex ab
        integer ldab
        real work
        ret = clangb(
     +    norm,
     +    n,
     +    kl,
     +    ku,
     +    ab,
     +    ldab,
     +    work
     +    )
      end

      subroutine clangewrp(
     +    ret,
     +    norm,
     +    m,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
        external clange
        real clange
        real ret
        character norm
        integer m
        integer n
        complex a
        integer lda
        real work
        ret = clange(
     +    norm,
     +    m,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
      end

      subroutine clangtwrp(
     +    ret,
     +    norm,
     +    n,
     +    dl,
     +    d,
     +    du
     +    )
        external clangt
        real clangt
        real ret
        character norm
        integer n
        complex dl
        complex d
        complex du
        ret = clangt(
     +    norm,
     +    n,
     +    dl,
     +    d,
     +    du
     +    )
      end

      subroutine clanhbwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
        external clanhb
        real clanhb
        real ret
        character norm
        character uplo
        integer n
        integer k
        complex ab
        integer ldab
        real work
        ret = clanhb(
     +    norm,
     +    uplo,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
      end

      subroutine clanhewrp(
     +    ret,
     +    norm,
     +    uplo,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
        external clanhe
        real clanhe
        real ret
        character norm
        character uplo
        integer n
        complex a
        integer lda
        real work
        ret = clanhe(
     +    norm,
     +    uplo,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
      end

      subroutine clanhfwrp(
     +    ret,
     +    norm,
     +    transr,
     +    uplo,
     +    n,
     +    a,
     +    work
     +    )
        external clanhf
        real clanhf
        real ret
        character norm
        character transr
        character uplo
        integer n
        complex a(*)
        real work
        ret = clanhf(
     +    norm,
     +    transr,
     +    uplo,
     +    n,
     +    a,
     +    work
     +    )
      end

      subroutine clanhpwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    n,
     +    ap,
     +    work
     +    )
        external clanhp
        real clanhp
        real ret
        character norm
        character uplo
        integer n
        complex ap
        real work
        ret = clanhp(
     +    norm,
     +    uplo,
     +    n,
     +    ap,
     +    work
     +    )
      end

      subroutine clanhswrp(
     +    ret,
     +    norm,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
        external clanhs
        real clanhs
        real ret
        character norm
        integer n
        complex a
        integer lda
        real work
        ret = clanhs(
     +    norm,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
      end

      subroutine clanhtwrp(
     +    ret,
     +    norm,
     +    n,
     +    d,
     +    e
     +    )
        external clanht
        real clanht
        real ret
        character norm
        integer n
        real d
        complex e
        ret = clanht(
     +    norm,
     +    n,
     +    d,
     +    e
     +    )
      end

      subroutine clansbwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
        external clansb
        real clansb
        real ret
        character norm
        character uplo
        integer n
        integer k
        complex ab
        integer ldab
        real work
        ret = clansb(
     +    norm,
     +    uplo,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
      end

      subroutine clanspwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    n,
     +    ap,
     +    work
     +    )
        external clansp
        real clansp
        real ret
        character norm
        character uplo
        integer n
        complex ap
        real work
        ret = clansp(
     +    norm,
     +    uplo,
     +    n,
     +    ap,
     +    work
     +    )
      end

      subroutine clansywrp(
     +    ret,
     +    norm,
     +    uplo,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
        external clansy
        real clansy
        real ret
        character norm
        character uplo
        integer n
        complex a
        integer lda
        real work
        ret = clansy(
     +    norm,
     +    uplo,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
      end

      subroutine clantbwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    diag,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
        external clantb
        real clantb
        real ret
        character norm
        character uplo
        character diag
        integer n
        integer k
        complex ab
        integer ldab
        real work
        ret = clantb(
     +    norm,
     +    uplo,
     +    diag,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
      end

      subroutine clantpwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    diag,
     +    n,
     +    ap,
     +    work
     +    )
        external clantp
        real clantp
        real ret
        character norm
        character uplo
        character diag
        integer n
        complex ap
        real work
        ret = clantp(
     +    norm,
     +    uplo,
     +    diag,
     +    n,
     +    ap,
     +    work
     +    )
      end

      subroutine clantrwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    diag,
     +    m,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
        external clantr
        real clantr
        real ret
        character norm
        character uplo
        character diag
        integer m
        integer n
        complex a
        integer lda
        real work
        ret = clantr(
     +    norm,
     +    uplo,
     +    diag,
     +    m,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
      end

      subroutine disnanwrp(
     +    ret,
     +    din
     +    )
        external disnan
        logical disnan
        logical ret
        double precision din
        ret = disnan(
     +    din
     +    )
      end

      subroutine dlamchwrp(
     +    ret,
     +    cmach
     +    )
        external dlamch
        double precision dlamch
        double precision ret
        character cmach
        ret = dlamch(
     +    cmach
     +    )
      end

      subroutine dlanegwrp(
     +    ret,
     +    n,
     +    d,
     +    lld,
     +    sigma,
     +    pivmin,
     +    r
     +    )
        external dlaneg
        integer dlaneg
        integer ret
        integer n
        double precision d
        double precision lld
        double precision sigma
        double precision pivmin
        integer r
        ret = dlaneg(
     +    n,
     +    d,
     +    lld,
     +    sigma,
     +    pivmin,
     +    r
     +    )
      end

      subroutine dlangbwrp(
     +    ret,
     +    norm,
     +    n,
     +    kl,
     +    ku,
     +    ab,
     +    ldab,
     +    work
     +    )
        external dlangb
        double precision dlangb
        double precision ret
        character norm
        integer n
        integer kl
        integer ku
        double precision ab
        integer ldab
        double precision work
        ret = dlangb(
     +    norm,
     +    n,
     +    kl,
     +    ku,
     +    ab,
     +    ldab,
     +    work
     +    )
      end

      subroutine dlangewrp(
     +    ret,
     +    norm,
     +    m,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
        external dlange
        double precision dlange
        double precision ret
        character norm
        integer m
        integer n
        double precision a
        integer lda
        double precision work
        ret = dlange(
     +    norm,
     +    m,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
      end

      subroutine dlangtwrp(
     +    ret,
     +    norm,
     +    n,
     +    dl,
     +    d,
     +    du
     +    )
        external dlangt
        double precision dlangt
        double precision ret
        character norm
        integer n
        double precision dl
        double precision d
        double precision du
        ret = dlangt(
     +    norm,
     +    n,
     +    dl,
     +    d,
     +    du
     +    )
      end

      subroutine dlanhswrp(
     +    ret,
     +    norm,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
        external dlanhs
        double precision dlanhs
        double precision ret
        character norm
        integer n
        double precision a
        integer lda
        double precision work
        ret = dlanhs(
     +    norm,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
      end

      subroutine dlansbwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
        external dlansb
        double precision dlansb
        double precision ret
        character norm
        character uplo
        integer n
        integer k
        double precision ab
        integer ldab
        double precision work
        ret = dlansb(
     +    norm,
     +    uplo,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
      end

      subroutine dlansfwrp(
     +    ret,
     +    norm,
     +    transr,
     +    uplo,
     +    n,
     +    a,
     +    work
     +    )
        external dlansf
        double precision dlansf
        double precision ret
        character norm
        character transr
        character uplo
        integer n
        double precision a(*)
        double precision work
        ret = dlansf(
     +    norm,
     +    transr,
     +    uplo,
     +    n,
     +    a,
     +    work
     +    )
      end

      subroutine dlanspwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    n,
     +    ap,
     +    work
     +    )
        external dlansp
        double precision dlansp
        double precision ret
        character norm
        character uplo
        integer n
        double precision ap
        double precision work
        ret = dlansp(
     +    norm,
     +    uplo,
     +    n,
     +    ap,
     +    work
     +    )
      end

      subroutine dlanstwrp(
     +    ret,
     +    norm,
     +    n,
     +    d,
     +    e
     +    )
        external dlanst
        double precision dlanst
        double precision ret
        character norm
        integer n
        double precision d
        double precision e
        ret = dlanst(
     +    norm,
     +    n,
     +    d,
     +    e
     +    )
      end

      subroutine dlansywrp(
     +    ret,
     +    norm,
     +    uplo,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
        external dlansy
        double precision dlansy
        double precision ret
        character norm
        character uplo
        integer n
        double precision a
        integer lda
        double precision work
        ret = dlansy(
     +    norm,
     +    uplo,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
      end

      subroutine dlantbwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    diag,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
        external dlantb
        double precision dlantb
        double precision ret
        character norm
        character uplo
        character diag
        integer n
        integer k
        double precision ab
        integer ldab
        double precision work
        ret = dlantb(
     +    norm,
     +    uplo,
     +    diag,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
      end

      subroutine dlantpwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    diag,
     +    n,
     +    ap,
     +    work
     +    )
        external dlantp
        double precision dlantp
        double precision ret
        character norm
        character uplo
        character diag
        integer n
        double precision ap
        double precision work
        ret = dlantp(
     +    norm,
     +    uplo,
     +    diag,
     +    n,
     +    ap,
     +    work
     +    )
      end

      subroutine dlantrwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    diag,
     +    m,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
        external dlantr
        double precision dlantr
        double precision ret
        character norm
        character uplo
        character diag
        integer m
        integer n
        double precision a
        integer lda
        double precision work
        ret = dlantr(
     +    norm,
     +    uplo,
     +    diag,
     +    m,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
      end

      subroutine dlapy2wrp(
     +    ret,
     +    x,
     +    y
     +    )
        external dlapy2
        double precision dlapy2
        double precision ret
        double precision x
        double precision y
        ret = dlapy2(
     +    x,
     +    y
     +    )
      end

      subroutine dlapy3wrp(
     +    ret,
     +    x,
     +    y,
     +    z
     +    )
        external dlapy3
        double precision dlapy3
        double precision ret
        double precision x
        double precision y
        double precision z
        ret = dlapy3(
     +    x,
     +    y,
     +    z
     +    )
      end

      subroutine dzsum1wrp(
     +    ret,
     +    n,
     +    cx,
     +    incx
     +    )
        external dzsum1
        double precision dzsum1
        double precision ret
        integer n
        complex*16 cx(n)
        integer incx
        ret = dzsum1(
     +    n,
     +    cx,
     +    incx
     +    )
      end

      subroutine icmax1wrp(
     +    ret,
     +    n,
     +    cx,
     +    incx
     +    )
        external icmax1
        integer icmax1
        integer ret
        integer n
        complex cx(n)
        integer incx
        ret = icmax1(
     +    n,
     +    cx,
     +    incx
     +    )
      end

      subroutine ieeeckwrp(
     +    ret,
     +    ispec,
     +    zero,
     +    one
     +    )
        external ieeeck
        integer ieeeck
        integer ret
        integer ispec
        real zero
        real one
        ret = ieeeck(
     +    ispec,
     +    zero,
     +    one
     +    )
      end

      subroutine ilaclcwrp(
     +    ret,
     +    m,
     +    n,
     +    a,
     +    lda
     +    )
        external ilaclc
        integer ilaclc
        integer ret
        integer m
        integer n
        complex a
        integer lda
        ret = ilaclc(
     +    m,
     +    n,
     +    a,
     +    lda
     +    )
      end

      subroutine ilaclrwrp(
     +    ret,
     +    m,
     +    n,
     +    a,
     +    lda
     +    )
        external ilaclr
        integer ilaclr
        integer ret
        integer m
        integer n
        complex a
        integer lda
        ret = ilaclr(
     +    m,
     +    n,
     +    a,
     +    lda
     +    )
      end

      subroutine iladiagwrp(
     +    ret,
     +    diag
     +    )
        external iladiag
        integer iladiag
        integer ret
        character diag
        ret = iladiag(
     +    diag
     +    )
      end

      subroutine iladlcwrp(
     +    ret,
     +    m,
     +    n,
     +    a,
     +    lda
     +    )
        external iladlc
        integer iladlc
        integer ret
        integer m
        integer n
        double precision a
        integer lda
        ret = iladlc(
     +    m,
     +    n,
     +    a,
     +    lda
     +    )
      end

      subroutine iladlrwrp(
     +    ret,
     +    m,
     +    n,
     +    a,
     +    lda
     +    )
        external iladlr
        integer iladlr
        integer ret
        integer m
        integer n
        double precision a
        integer lda
        ret = iladlr(
     +    m,
     +    n,
     +    a,
     +    lda
     +    )
      end

      subroutine ilaprecwrp(
     +    ret,
     +    prec
     +    )
        external ilaprec
        integer ilaprec
        integer ret
        character prec
        ret = ilaprec(
     +    prec
     +    )
      end

      subroutine ilaslcwrp(
     +    ret,
     +    m,
     +    n,
     +    a,
     +    lda
     +    )
        external ilaslc
        integer ilaslc
        integer ret
        integer m
        integer n
        real a
        integer lda
        ret = ilaslc(
     +    m,
     +    n,
     +    a,
     +    lda
     +    )
      end

      subroutine ilaslrwrp(
     +    ret,
     +    m,
     +    n,
     +    a,
     +    lda
     +    )
        external ilaslr
        integer ilaslr
        integer ret
        integer m
        integer n
        real a
        integer lda
        ret = ilaslr(
     +    m,
     +    n,
     +    a,
     +    lda
     +    )
      end

      subroutine ilatranswrp(
     +    ret,
     +    trans
     +    )
        external ilatrans
        integer ilatrans
        integer ret
        character trans
        ret = ilatrans(
     +    trans
     +    )
      end

      subroutine ilauplowrp(
     +    ret,
     +    uplo
     +    )
        external ilauplo
        integer ilauplo
        integer ret
        character uplo
        ret = ilauplo(
     +    uplo
     +    )
      end

      subroutine ilazlcwrp(
     +    ret,
     +    m,
     +    n,
     +    a,
     +    lda
     +    )
        external ilazlc
        integer ilazlc
        integer ret
        integer m
        integer n
        complex*16 a
        integer lda
        ret = ilazlc(
     +    m,
     +    n,
     +    a,
     +    lda
     +    )
      end

      subroutine ilazlrwrp(
     +    ret,
     +    m,
     +    n,
     +    a,
     +    lda
     +    )
        external ilazlr
        integer ilazlr
        integer ret
        integer m
        integer n
        complex*16 a
        integer lda
        ret = ilazlr(
     +    m,
     +    n,
     +    a,
     +    lda
     +    )
      end

      subroutine izmax1wrp(
     +    ret,
     +    n,
     +    cx,
     +    incx
     +    )
        external izmax1
        integer izmax1
        integer ret
        integer n
        complex*16 cx(n)
        integer incx
        ret = izmax1(
     +    n,
     +    cx,
     +    incx
     +    )
      end

      subroutine scsum1wrp(
     +    ret,
     +    n,
     +    cx,
     +    incx
     +    )
        external scsum1
        real scsum1
        real ret
        integer n
        complex cx(n)
        integer incx
        ret = scsum1(
     +    n,
     +    cx,
     +    incx
     +    )
      end

      subroutine slamchwrp(
     +    ret,
     +    cmach
     +    )
        external slamch
        real slamch
        real ret
        character cmach
        ret = slamch(
     +    cmach
     +    )
      end

      subroutine slangbwrp(
     +    ret,
     +    norm,
     +    n,
     +    kl,
     +    ku,
     +    ab,
     +    ldab,
     +    work
     +    )
        external slangb
        real slangb
        real ret
        character norm
        integer n
        integer kl
        integer ku
        real ab
        integer ldab
        real work
        ret = slangb(
     +    norm,
     +    n,
     +    kl,
     +    ku,
     +    ab,
     +    ldab,
     +    work
     +    )
      end

      subroutine slangewrp(
     +    ret,
     +    norm,
     +    m,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
        external slange
        real slange
        real ret
        character norm
        integer m
        integer n
        real a
        integer lda
        real work
        ret = slange(
     +    norm,
     +    m,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
      end

      subroutine slangtwrp(
     +    ret,
     +    norm,
     +    n,
     +    dl,
     +    d,
     +    du
     +    )
        external slangt
        real slangt
        real ret
        character norm
        integer n
        real dl
        real d
        real du
        ret = slangt(
     +    norm,
     +    n,
     +    dl,
     +    d,
     +    du
     +    )
      end

      subroutine slanhswrp(
     +    ret,
     +    norm,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
        external slanhs
        real slanhs
        real ret
        character norm
        integer n
        real a
        integer lda
        real work
        ret = slanhs(
     +    norm,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
      end

      subroutine slansbwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
        external slansb
        real slansb
        real ret
        character norm
        character uplo
        integer n
        integer k
        real ab
        integer ldab
        real work
        ret = slansb(
     +    norm,
     +    uplo,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
      end

      subroutine slansfwrp(
     +    ret,
     +    norm,
     +    transr,
     +    uplo,
     +    n,
     +    a,
     +    work
     +    )
        external slansf
        real slansf
        real ret
        character norm
        character transr
        character uplo
        integer n
        real a(*)
        real work
        ret = slansf(
     +    norm,
     +    transr,
     +    uplo,
     +    n,
     +    a,
     +    work
     +    )
      end

      subroutine slanspwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    n,
     +    ap,
     +    work
     +    )
        external slansp
        real slansp
        real ret
        character norm
        character uplo
        integer n
        real ap
        real work
        ret = slansp(
     +    norm,
     +    uplo,
     +    n,
     +    ap,
     +    work
     +    )
      end

      subroutine slanstwrp(
     +    ret,
     +    norm,
     +    n,
     +    d,
     +    e
     +    )
        external slanst
        real slanst
        real ret
        character norm
        integer n
        real d
        real e
        ret = slanst(
     +    norm,
     +    n,
     +    d,
     +    e
     +    )
      end

      subroutine slansywrp(
     +    ret,
     +    norm,
     +    uplo,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
        external slansy
        real slansy
        real ret
        character norm
        character uplo
        integer n
        real a
        integer lda
        real work
        ret = slansy(
     +    norm,
     +    uplo,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
      end

      subroutine slantbwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    diag,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
        external slantb
        real slantb
        real ret
        character norm
        character uplo
        character diag
        integer n
        integer k
        real ab
        integer ldab
        real work
        ret = slantb(
     +    norm,
     +    uplo,
     +    diag,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
      end

      subroutine slantpwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    diag,
     +    n,
     +    ap,
     +    work
     +    )
        external slantp
        real slantp
        real ret
        character norm
        character uplo
        character diag
        integer n
        real ap
        real work
        ret = slantp(
     +    norm,
     +    uplo,
     +    diag,
     +    n,
     +    ap,
     +    work
     +    )
      end

      subroutine slantrwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    diag,
     +    m,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
        external slantr
        real slantr
        real ret
        character norm
        character uplo
        character diag
        integer m
        integer n
        real a
        integer lda
        real work
        ret = slantr(
     +    norm,
     +    uplo,
     +    diag,
     +    m,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
      end

      subroutine slapy2wrp(
     +    ret,
     +    x,
     +    y
     +    )
        external slapy2
        real slapy2
        real ret
        real x
        real y
        ret = slapy2(
     +    x,
     +    y
     +    )
      end

      subroutine slapy3wrp(
     +    ret,
     +    x,
     +    y,
     +    z
     +    )
        external slapy3
        real slapy3
        real ret
        real x
        real y
        real z
        ret = slapy3(
     +    x,
     +    y,
     +    z
     +    )
      end

      subroutine zladivwrp(
     +    ret,
     +    x,
     +    y
     +    )
        external wzladiv
        complex*16 wzladiv
        complex*16 ret
        complex*16 x
        complex*16 y
        ret = wzladiv(
     +    x,
     +    y
     +    )
      end

      subroutine zlangbwrp(
     +    ret,
     +    norm,
     +    n,
     +    kl,
     +    ku,
     +    ab,
     +    ldab,
     +    work
     +    )
        external zlangb
        double precision zlangb
        double precision ret
        character norm
        integer n
        integer kl
        integer ku
        complex*16 ab
        integer ldab
        double precision work
        ret = zlangb(
     +    norm,
     +    n,
     +    kl,
     +    ku,
     +    ab,
     +    ldab,
     +    work
     +    )
      end

      subroutine zlangewrp(
     +    ret,
     +    norm,
     +    m,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
        external zlange
        double precision zlange
        double precision ret
        character norm
        integer m
        integer n
        complex*16 a
        integer lda
        double precision work
        ret = zlange(
     +    norm,
     +    m,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
      end

      subroutine zlangtwrp(
     +    ret,
     +    norm,
     +    n,
     +    dl,
     +    d,
     +    du
     +    )
        external zlangt
        double precision zlangt
        double precision ret
        character norm
        integer n
        complex*16 dl
        complex*16 d
        complex*16 du
        ret = zlangt(
     +    norm,
     +    n,
     +    dl,
     +    d,
     +    du
     +    )
      end

      subroutine zlanhbwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
        external zlanhb
        double precision zlanhb
        double precision ret
        character norm
        character uplo
        integer n
        integer k
        complex*16 ab
        integer ldab
        double precision work
        ret = zlanhb(
     +    norm,
     +    uplo,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
      end

      subroutine zlanhewrp(
     +    ret,
     +    norm,
     +    uplo,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
        external zlanhe
        double precision zlanhe
        double precision ret
        character norm
        character uplo
        integer n
        complex*16 a
        integer lda
        double precision work
        ret = zlanhe(
     +    norm,
     +    uplo,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
      end

      subroutine zlanhfwrp(
     +    ret,
     +    norm,
     +    transr,
     +    uplo,
     +    n,
     +    a,
     +    work
     +    )
        external zlanhf
        double precision zlanhf
        double precision ret
        character norm
        character transr
        character uplo
        integer n
        complex*16 a(*)
        double precision work
        ret = zlanhf(
     +    norm,
     +    transr,
     +    uplo,
     +    n,
     +    a,
     +    work
     +    )
      end

      subroutine zlanhpwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    n,
     +    ap,
     +    work
     +    )
        external zlanhp
        double precision zlanhp
        double precision ret
        character norm
        character uplo
        integer n
        complex*16 ap
        double precision work
        ret = zlanhp(
     +    norm,
     +    uplo,
     +    n,
     +    ap,
     +    work
     +    )
      end

      subroutine zlanhswrp(
     +    ret,
     +    norm,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
        external zlanhs
        double precision zlanhs
        double precision ret
        character norm
        integer n
        complex*16 a
        integer lda
        double precision work
        ret = zlanhs(
     +    norm,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
      end

      subroutine zlanhtwrp(
     +    ret,
     +    norm,
     +    n,
     +    d,
     +    e
     +    )
        external zlanht
        double precision zlanht
        double precision ret
        character norm
        integer n
        double precision d
        complex*16 e
        ret = zlanht(
     +    norm,
     +    n,
     +    d,
     +    e
     +    )
      end

      subroutine zlansbwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
        external zlansb
        double precision zlansb
        double precision ret
        character norm
        character uplo
        integer n
        integer k
        complex*16 ab
        integer ldab
        double precision work
        ret = zlansb(
     +    norm,
     +    uplo,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
      end

      subroutine zlanspwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    n,
     +    ap,
     +    work
     +    )
        external zlansp
        double precision zlansp
        double precision ret
        character norm
        character uplo
        integer n
        complex*16 ap
        double precision work
        ret = zlansp(
     +    norm,
     +    uplo,
     +    n,
     +    ap,
     +    work
     +    )
      end

      subroutine zlansywrp(
     +    ret,
     +    norm,
     +    uplo,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
        external zlansy
        double precision zlansy
        double precision ret
        character norm
        character uplo
        integer n
        complex*16 a
        integer lda
        double precision work
        ret = zlansy(
     +    norm,
     +    uplo,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
      end

      subroutine zlantbwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    diag,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
        external zlantb
        double precision zlantb
        double precision ret
        character norm
        character uplo
        character diag
        integer n
        integer k
        complex*16 ab
        integer ldab
        double precision work
        ret = zlantb(
     +    norm,
     +    uplo,
     +    diag,
     +    n,
     +    k,
     +    ab,
     +    ldab,
     +    work
     +    )
      end

      subroutine zlantpwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    diag,
     +    n,
     +    ap,
     +    work
     +    )
        external zlantp
        double precision zlantp
        double precision ret
        character norm
        character uplo
        character diag
        integer n
        complex*16 ap
        double precision work
        ret = zlantp(
     +    norm,
     +    uplo,
     +    diag,
     +    n,
     +    ap,
     +    work
     +    )
      end

      subroutine zlantrwrp(
     +    ret,
     +    norm,
     +    uplo,
     +    diag,
     +    m,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
        external zlantr
        double precision zlantr
        double precision ret
        character norm
        character uplo
        character diag
        integer m
        integer n
        complex*16 a
        integer lda
        double precision work
        ret = zlantr(
     +    norm,
     +    uplo,
     +    diag,
     +    m,
     +    n,
     +    a,
     +    lda,
     +    work
     +    )
      end
