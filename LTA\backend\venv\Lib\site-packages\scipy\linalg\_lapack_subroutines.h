/* This file was generated by _generate_pyx.py. */
/* Do not edit this file directly. */

#ifndef SCIPY_LINALG_LAPACK_FORTRAN_WRAPPERS_H
#define SCIPY_LINALG_LAPACK_FORTRAN_WRAPPERS_H
#include "fortran_defs.h"
#include "numpy/arrayobject.h"

typedef int (*_cselect1)(npy_complex64*);
typedef int (*_cselect2)(npy_complex64*, npy_complex64*);
typedef int (*_dselect2)(double*, double*);
typedef int (*_dselect3)(double*, double*, double*);
typedef int (*_sselect2)(float*, float*);
typedef int (*_sselect3)(float*, float*, float*);
typedef int (*_zselect1)(npy_complex128*);
typedef int (*_zselect2)(npy_complex128*, npy_complex128*);

#ifdef __cplusplus
extern "C" {
#endif

void F_FUNC(chla_transtypewrp, CHLA_TRANSTYPEWRP)(char *ret, int *trans);
void F_FUNC(cladivwrp, CLADIVWRP)(npy_complex64 *ret, npy_complex64 *x, npy_complex64 *y);
void F_FUNC(clangbwrp, CLANGBWRP)(float *ret, char *norm, int *n, int *kl, int *ku, npy_complex64 *ab, int *ldab, float *work);
void F_FUNC(clangewrp, CLANGEWRP)(float *ret, char *norm, int *m, int *n, npy_complex64 *a, int *lda, float *work);
void F_FUNC(clangtwrp, CLANGTWRP)(float *ret, char *norm, int *n, npy_complex64 *dl, npy_complex64 *d, npy_complex64 *du);
void F_FUNC(clanhbwrp, CLANHBWRP)(float *ret, char *norm, char *uplo, int *n, int *k, npy_complex64 *ab, int *ldab, float *work);
void F_FUNC(clanhewrp, CLANHEWRP)(float *ret, char *norm, char *uplo, int *n, npy_complex64 *a, int *lda, float *work);
void F_FUNC(clanhfwrp, CLANHFWRP)(float *ret, char *norm, char *transr, char *uplo, int *n, npy_complex64 *a, float *work);
void F_FUNC(clanhpwrp, CLANHPWRP)(float *ret, char *norm, char *uplo, int *n, npy_complex64 *ap, float *work);
void F_FUNC(clanhswrp, CLANHSWRP)(float *ret, char *norm, int *n, npy_complex64 *a, int *lda, float *work);
void F_FUNC(clanhtwrp, CLANHTWRP)(float *ret, char *norm, int *n, float *d, npy_complex64 *e);
void F_FUNC(clansbwrp, CLANSBWRP)(float *ret, char *norm, char *uplo, int *n, int *k, npy_complex64 *ab, int *ldab, float *work);
void F_FUNC(clanspwrp, CLANSPWRP)(float *ret, char *norm, char *uplo, int *n, npy_complex64 *ap, float *work);
void F_FUNC(clansywrp, CLANSYWRP)(float *ret, char *norm, char *uplo, int *n, npy_complex64 *a, int *lda, float *work);
void F_FUNC(clantbwrp, CLANTBWRP)(float *ret, char *norm, char *uplo, char *diag, int *n, int *k, npy_complex64 *ab, int *ldab, float *work);
void F_FUNC(clantpwrp, CLANTPWRP)(float *ret, char *norm, char *uplo, char *diag, int *n, npy_complex64 *ap, float *work);
void F_FUNC(clantrwrp, CLANTRWRP)(float *ret, char *norm, char *uplo, char *diag, int *m, int *n, npy_complex64 *a, int *lda, float *work);
void F_FUNC(disnanwrp, DISNANWRP)(int *ret, double *din);
void F_FUNC(dlamchwrp, DLAMCHWRP)(double *ret, char *cmach);
void F_FUNC(dlanegwrp, DLANEGWRP)(int *ret, int *n, double *d, double *lld, double *sigma, double *pivmin, int *r);
void F_FUNC(dlangbwrp, DLANGBWRP)(double *ret, char *norm, int *n, int *kl, int *ku, double *ab, int *ldab, double *work);
void F_FUNC(dlangewrp, DLANGEWRP)(double *ret, char *norm, int *m, int *n, double *a, int *lda, double *work);
void F_FUNC(dlangtwrp, DLANGTWRP)(double *ret, char *norm, int *n, double *dl, double *d, double *du);
void F_FUNC(dlanhswrp, DLANHSWRP)(double *ret, char *norm, int *n, double *a, int *lda, double *work);
void F_FUNC(dlansbwrp, DLANSBWRP)(double *ret, char *norm, char *uplo, int *n, int *k, double *ab, int *ldab, double *work);
void F_FUNC(dlansfwrp, DLANSFWRP)(double *ret, char *norm, char *transr, char *uplo, int *n, double *a, double *work);
void F_FUNC(dlanspwrp, DLANSPWRP)(double *ret, char *norm, char *uplo, int *n, double *ap, double *work);
void F_FUNC(dlanstwrp, DLANSTWRP)(double *ret, char *norm, int *n, double *d, double *e);
void F_FUNC(dlansywrp, DLANSYWRP)(double *ret, char *norm, char *uplo, int *n, double *a, int *lda, double *work);
void F_FUNC(dlantbwrp, DLANTBWRP)(double *ret, char *norm, char *uplo, char *diag, int *n, int *k, double *ab, int *ldab, double *work);
void F_FUNC(dlantpwrp, DLANTPWRP)(double *ret, char *norm, char *uplo, char *diag, int *n, double *ap, double *work);
void F_FUNC(dlantrwrp, DLANTRWRP)(double *ret, char *norm, char *uplo, char *diag, int *m, int *n, double *a, int *lda, double *work);
void F_FUNC(dlapy2wrp, DLAPY2WRP)(double *ret, double *x, double *y);
void F_FUNC(dlapy3wrp, DLAPY3WRP)(double *ret, double *x, double *y, double *z);
void F_FUNC(dzsum1wrp, DZSUM1WRP)(double *ret, int *n, npy_complex128 *cx, int *incx);
void F_FUNC(icmax1wrp, ICMAX1WRP)(int *ret, int *n, npy_complex64 *cx, int *incx);
void F_FUNC(ieeeckwrp, IEEECKWRP)(int *ret, int *ispec, float *zero, float *one);
void F_FUNC(ilaclcwrp, ILACLCWRP)(int *ret, int *m, int *n, npy_complex64 *a, int *lda);
void F_FUNC(ilaclrwrp, ILACLRWRP)(int *ret, int *m, int *n, npy_complex64 *a, int *lda);
void F_FUNC(iladiagwrp, ILADIAGWRP)(int *ret, char *diag);
void F_FUNC(iladlcwrp, ILADLCWRP)(int *ret, int *m, int *n, double *a, int *lda);
void F_FUNC(iladlrwrp, ILADLRWRP)(int *ret, int *m, int *n, double *a, int *lda);
void F_FUNC(ilaprecwrp, ILAPRECWRP)(int *ret, char *prec);
void F_FUNC(ilaslcwrp, ILASLCWRP)(int *ret, int *m, int *n, float *a, int *lda);
void F_FUNC(ilaslrwrp, ILASLRWRP)(int *ret, int *m, int *n, float *a, int *lda);
void F_FUNC(ilatranswrp, ILATRANSWRP)(int *ret, char *trans);
void F_FUNC(ilauplowrp, ILAUPLOWRP)(int *ret, char *uplo);
void F_FUNC(ilazlcwrp, ILAZLCWRP)(int *ret, int *m, int *n, npy_complex128 *a, int *lda);
void F_FUNC(ilazlrwrp, ILAZLRWRP)(int *ret, int *m, int *n, npy_complex128 *a, int *lda);
void F_FUNC(izmax1wrp, IZMAX1WRP)(int *ret, int *n, npy_complex128 *cx, int *incx);
void F_FUNC(scsum1wrp, SCSUM1WRP)(float *ret, int *n, npy_complex64 *cx, int *incx);
void F_FUNC(slamchwrp, SLAMCHWRP)(float *ret, char *cmach);
void F_FUNC(slangbwrp, SLANGBWRP)(float *ret, char *norm, int *n, int *kl, int *ku, float *ab, int *ldab, float *work);
void F_FUNC(slangewrp, SLANGEWRP)(float *ret, char *norm, int *m, int *n, float *a, int *lda, float *work);
void F_FUNC(slangtwrp, SLANGTWRP)(float *ret, char *norm, int *n, float *dl, float *d, float *du);
void F_FUNC(slanhswrp, SLANHSWRP)(float *ret, char *norm, int *n, float *a, int *lda, float *work);
void F_FUNC(slansbwrp, SLANSBWRP)(float *ret, char *norm, char *uplo, int *n, int *k, float *ab, int *ldab, float *work);
void F_FUNC(slansfwrp, SLANSFWRP)(float *ret, char *norm, char *transr, char *uplo, int *n, float *a, float *work);
void F_FUNC(slanspwrp, SLANSPWRP)(float *ret, char *norm, char *uplo, int *n, float *ap, float *work);
void F_FUNC(slanstwrp, SLANSTWRP)(float *ret, char *norm, int *n, float *d, float *e);
void F_FUNC(slansywrp, SLANSYWRP)(float *ret, char *norm, char *uplo, int *n, float *a, int *lda, float *work);
void F_FUNC(slantbwrp, SLANTBWRP)(float *ret, char *norm, char *uplo, char *diag, int *n, int *k, float *ab, int *ldab, float *work);
void F_FUNC(slantpwrp, SLANTPWRP)(float *ret, char *norm, char *uplo, char *diag, int *n, float *ap, float *work);
void F_FUNC(slantrwrp, SLANTRWRP)(float *ret, char *norm, char *uplo, char *diag, int *m, int *n, float *a, int *lda, float *work);
void F_FUNC(slapy2wrp, SLAPY2WRP)(float *ret, float *x, float *y);
void F_FUNC(slapy3wrp, SLAPY3WRP)(float *ret, float *x, float *y, float *z);
void F_FUNC(zladivwrp, ZLADIVWRP)(npy_complex128 *ret, npy_complex128 *x, npy_complex128 *y);
void F_FUNC(zlangbwrp, ZLANGBWRP)(double *ret, char *norm, int *n, int *kl, int *ku, npy_complex128 *ab, int *ldab, double *work);
void F_FUNC(zlangewrp, ZLANGEWRP)(double *ret, char *norm, int *m, int *n, npy_complex128 *a, int *lda, double *work);
void F_FUNC(zlangtwrp, ZLANGTWRP)(double *ret, char *norm, int *n, npy_complex128 *dl, npy_complex128 *d, npy_complex128 *du);
void F_FUNC(zlanhbwrp, ZLANHBWRP)(double *ret, char *norm, char *uplo, int *n, int *k, npy_complex128 *ab, int *ldab, double *work);
void F_FUNC(zlanhewrp, ZLANHEWRP)(double *ret, char *norm, char *uplo, int *n, npy_complex128 *a, int *lda, double *work);
void F_FUNC(zlanhfwrp, ZLANHFWRP)(double *ret, char *norm, char *transr, char *uplo, int *n, npy_complex128 *a, double *work);
void F_FUNC(zlanhpwrp, ZLANHPWRP)(double *ret, char *norm, char *uplo, int *n, npy_complex128 *ap, double *work);
void F_FUNC(zlanhswrp, ZLANHSWRP)(double *ret, char *norm, int *n, npy_complex128 *a, int *lda, double *work);
void F_FUNC(zlanhtwrp, ZLANHTWRP)(double *ret, char *norm, int *n, double *d, npy_complex128 *e);
void F_FUNC(zlansbwrp, ZLANSBWRP)(double *ret, char *norm, char *uplo, int *n, int *k, npy_complex128 *ab, int *ldab, double *work);
void F_FUNC(zlanspwrp, ZLANSPWRP)(double *ret, char *norm, char *uplo, int *n, npy_complex128 *ap, double *work);
void F_FUNC(zlansywrp, ZLANSYWRP)(double *ret, char *norm, char *uplo, int *n, npy_complex128 *a, int *lda, double *work);
void F_FUNC(zlantbwrp, ZLANTBWRP)(double *ret, char *norm, char *uplo, char *diag, int *n, int *k, npy_complex128 *ab, int *ldab, double *work);
void F_FUNC(zlantpwrp, ZLANTPWRP)(double *ret, char *norm, char *uplo, char *diag, int *n, npy_complex128 *ap, double *work);
void F_FUNC(zlantrwrp, ZLANTRWRP)(double *ret, char *norm, char *uplo, char *diag, int *m, int *n, npy_complex128 *a, int *lda, double *work);

void F_FUNC(cbbcsd,CBBCSD)(char *jobu1, char *jobu2, char *jobv1t, char *jobv2t, char *trans, int *m, int *p, int *q, float *theta, float *phi, npy_complex64 *u1, int *ldu1, npy_complex64 *u2, int *ldu2, npy_complex64 *v1t, int *ldv1t, npy_complex64 *v2t, int *ldv2t, float *b11d, float *b11e, float *b12d, float *b12e, float *b21d, float *b21e, float *b22d, float *b22e, float *rwork, int *lrwork, int *info);
void F_FUNC(cbdsqr,CBDSQR)(char *uplo, int *n, int *ncvt, int *nru, int *ncc, float *d, float *e, npy_complex64 *vt, int *ldvt, npy_complex64 *u, int *ldu, npy_complex64 *c, int *ldc, float *rwork, int *info);
void F_FUNC(cgbbrd,CGBBRD)(char *vect, int *m, int *n, int *ncc, int *kl, int *ku, npy_complex64 *ab, int *ldab, float *d, float *e, npy_complex64 *q, int *ldq, npy_complex64 *pt, int *ldpt, npy_complex64 *c, int *ldc, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cgbcon,CGBCON)(char *norm, int *n, int *kl, int *ku, npy_complex64 *ab, int *ldab, int *ipiv, float *anorm, float *rcond, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cgbequ,CGBEQU)(int *m, int *n, int *kl, int *ku, npy_complex64 *ab, int *ldab, float *r, float *c, float *rowcnd, float *colcnd, float *amax, int *info);
void F_FUNC(cgbequb,CGBEQUB)(int *m, int *n, int *kl, int *ku, npy_complex64 *ab, int *ldab, float *r, float *c, float *rowcnd, float *colcnd, float *amax, int *info);
void F_FUNC(cgbrfs,CGBRFS)(char *trans, int *n, int *kl, int *ku, int *nrhs, npy_complex64 *ab, int *ldab, npy_complex64 *afb, int *ldafb, int *ipiv, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cgbsv,CGBSV)(int *n, int *kl, int *ku, int *nrhs, npy_complex64 *ab, int *ldab, int *ipiv, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(cgbsvx,CGBSVX)(char *fact, char *trans, int *n, int *kl, int *ku, int *nrhs, npy_complex64 *ab, int *ldab, npy_complex64 *afb, int *ldafb, int *ipiv, char *equed, float *r, float *c, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *rcond, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cgbtf2,CGBTF2)(int *m, int *n, int *kl, int *ku, npy_complex64 *ab, int *ldab, int *ipiv, int *info);
void F_FUNC(cgbtrf,CGBTRF)(int *m, int *n, int *kl, int *ku, npy_complex64 *ab, int *ldab, int *ipiv, int *info);
void F_FUNC(cgbtrs,CGBTRS)(char *trans, int *n, int *kl, int *ku, int *nrhs, npy_complex64 *ab, int *ldab, int *ipiv, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(cgebak,CGEBAK)(char *job, char *side, int *n, int *ilo, int *ihi, float *scale, int *m, npy_complex64 *v, int *ldv, int *info);
void F_FUNC(cgebal,CGEBAL)(char *job, int *n, npy_complex64 *a, int *lda, int *ilo, int *ihi, float *scale, int *info);
void F_FUNC(cgebd2,CGEBD2)(int *m, int *n, npy_complex64 *a, int *lda, float *d, float *e, npy_complex64 *tauq, npy_complex64 *taup, npy_complex64 *work, int *info);
void F_FUNC(cgebrd,CGEBRD)(int *m, int *n, npy_complex64 *a, int *lda, float *d, float *e, npy_complex64 *tauq, npy_complex64 *taup, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cgecon,CGECON)(char *norm, int *n, npy_complex64 *a, int *lda, float *anorm, float *rcond, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cgeequ,CGEEQU)(int *m, int *n, npy_complex64 *a, int *lda, float *r, float *c, float *rowcnd, float *colcnd, float *amax, int *info);
void F_FUNC(cgeequb,CGEEQUB)(int *m, int *n, npy_complex64 *a, int *lda, float *r, float *c, float *rowcnd, float *colcnd, float *amax, int *info);
void F_FUNC(cgees,CGEES)(char *jobvs, char *sort, _cselect1 *select, int *n, npy_complex64 *a, int *lda, int *sdim, npy_complex64 *w, npy_complex64 *vs, int *ldvs, npy_complex64 *work, int *lwork, float *rwork, int *bwork, int *info);
void F_FUNC(cgeesx,CGEESX)(char *jobvs, char *sort, _cselect1 *select, char *sense, int *n, npy_complex64 *a, int *lda, int *sdim, npy_complex64 *w, npy_complex64 *vs, int *ldvs, float *rconde, float *rcondv, npy_complex64 *work, int *lwork, float *rwork, int *bwork, int *info);
void F_FUNC(cgeev,CGEEV)(char *jobvl, char *jobvr, int *n, npy_complex64 *a, int *lda, npy_complex64 *w, npy_complex64 *vl, int *ldvl, npy_complex64 *vr, int *ldvr, npy_complex64 *work, int *lwork, float *rwork, int *info);
void F_FUNC(cgeevx,CGEEVX)(char *balanc, char *jobvl, char *jobvr, char *sense, int *n, npy_complex64 *a, int *lda, npy_complex64 *w, npy_complex64 *vl, int *ldvl, npy_complex64 *vr, int *ldvr, int *ilo, int *ihi, float *scale, float *abnrm, float *rconde, float *rcondv, npy_complex64 *work, int *lwork, float *rwork, int *info);
void F_FUNC(cgehd2,CGEHD2)(int *n, int *ilo, int *ihi, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *info);
void F_FUNC(cgehrd,CGEHRD)(int *n, int *ilo, int *ihi, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cgelq2,CGELQ2)(int *m, int *n, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *info);
void F_FUNC(cgelqf,CGELQF)(int *m, int *n, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cgels,CGELS)(char *trans, int *m, int *n, int *nrhs, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cgelsd,CGELSD)(int *m, int *n, int *nrhs, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, float *s, float *rcond, int *rank, npy_complex64 *work, int *lwork, float *rwork, int *iwork, int *info);
void F_FUNC(cgelss,CGELSS)(int *m, int *n, int *nrhs, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, float *s, float *rcond, int *rank, npy_complex64 *work, int *lwork, float *rwork, int *info);
void F_FUNC(cgelsy,CGELSY)(int *m, int *n, int *nrhs, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, int *jpvt, float *rcond, int *rank, npy_complex64 *work, int *lwork, float *rwork, int *info);
void F_FUNC(cgemqrt,CGEMQRT)(char *side, char *trans, int *m, int *n, int *k, int *nb, npy_complex64 *v, int *ldv, npy_complex64 *t, int *ldt, npy_complex64 *c, int *ldc, npy_complex64 *work, int *info);
void F_FUNC(cgeql2,CGEQL2)(int *m, int *n, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *info);
void F_FUNC(cgeqlf,CGEQLF)(int *m, int *n, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cgeqp3,CGEQP3)(int *m, int *n, npy_complex64 *a, int *lda, int *jpvt, npy_complex64 *tau, npy_complex64 *work, int *lwork, float *rwork, int *info);
void F_FUNC(cgeqr2,CGEQR2)(int *m, int *n, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *info);
void F_FUNC(cgeqr2p,CGEQR2P)(int *m, int *n, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *info);
void F_FUNC(cgeqrf,CGEQRF)(int *m, int *n, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cgeqrfp,CGEQRFP)(int *m, int *n, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cgeqrt,CGEQRT)(int *m, int *n, int *nb, npy_complex64 *a, int *lda, npy_complex64 *t, int *ldt, npy_complex64 *work, int *info);
void F_FUNC(cgeqrt2,CGEQRT2)(int *m, int *n, npy_complex64 *a, int *lda, npy_complex64 *t, int *ldt, int *info);
void F_FUNC(cgeqrt3,CGEQRT3)(int *m, int *n, npy_complex64 *a, int *lda, npy_complex64 *t, int *ldt, int *info);
void F_FUNC(cgerfs,CGERFS)(char *trans, int *n, int *nrhs, npy_complex64 *a, int *lda, npy_complex64 *af, int *ldaf, int *ipiv, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cgerq2,CGERQ2)(int *m, int *n, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *info);
void F_FUNC(cgerqf,CGERQF)(int *m, int *n, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cgesc2,CGESC2)(int *n, npy_complex64 *a, int *lda, npy_complex64 *rhs, int *ipiv, int *jpiv, float *scale);
void F_FUNC(cgesdd,CGESDD)(char *jobz, int *m, int *n, npy_complex64 *a, int *lda, float *s, npy_complex64 *u, int *ldu, npy_complex64 *vt, int *ldvt, npy_complex64 *work, int *lwork, float *rwork, int *iwork, int *info);
void F_FUNC(cgesv,CGESV)(int *n, int *nrhs, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(cgesvd,CGESVD)(char *jobu, char *jobvt, int *m, int *n, npy_complex64 *a, int *lda, float *s, npy_complex64 *u, int *ldu, npy_complex64 *vt, int *ldvt, npy_complex64 *work, int *lwork, float *rwork, int *info);
void F_FUNC(cgesvx,CGESVX)(char *fact, char *trans, int *n, int *nrhs, npy_complex64 *a, int *lda, npy_complex64 *af, int *ldaf, int *ipiv, char *equed, float *r, float *c, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *rcond, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cgetc2,CGETC2)(int *n, npy_complex64 *a, int *lda, int *ipiv, int *jpiv, int *info);
void F_FUNC(cgetf2,CGETF2)(int *m, int *n, npy_complex64 *a, int *lda, int *ipiv, int *info);
void F_FUNC(cgetrf,CGETRF)(int *m, int *n, npy_complex64 *a, int *lda, int *ipiv, int *info);
void F_FUNC(cgetri,CGETRI)(int *n, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cgetrs,CGETRS)(char *trans, int *n, int *nrhs, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(cggbak,CGGBAK)(char *job, char *side, int *n, int *ilo, int *ihi, float *lscale, float *rscale, int *m, npy_complex64 *v, int *ldv, int *info);
void F_FUNC(cggbal,CGGBAL)(char *job, int *n, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, int *ilo, int *ihi, float *lscale, float *rscale, float *work, int *info);
void F_FUNC(cgges,CGGES)(char *jobvsl, char *jobvsr, char *sort, _cselect2 *selctg, int *n, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, int *sdim, npy_complex64 *alpha, npy_complex64 *beta, npy_complex64 *vsl, int *ldvsl, npy_complex64 *vsr, int *ldvsr, npy_complex64 *work, int *lwork, float *rwork, int *bwork, int *info);
void F_FUNC(cggesx,CGGESX)(char *jobvsl, char *jobvsr, char *sort, _cselect2 *selctg, char *sense, int *n, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, int *sdim, npy_complex64 *alpha, npy_complex64 *beta, npy_complex64 *vsl, int *ldvsl, npy_complex64 *vsr, int *ldvsr, float *rconde, float *rcondv, npy_complex64 *work, int *lwork, float *rwork, int *iwork, int *liwork, int *bwork, int *info);
void F_FUNC(cggev,CGGEV)(char *jobvl, char *jobvr, int *n, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *alpha, npy_complex64 *beta, npy_complex64 *vl, int *ldvl, npy_complex64 *vr, int *ldvr, npy_complex64 *work, int *lwork, float *rwork, int *info);
void F_FUNC(cggevx,CGGEVX)(char *balanc, char *jobvl, char *jobvr, char *sense, int *n, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *alpha, npy_complex64 *beta, npy_complex64 *vl, int *ldvl, npy_complex64 *vr, int *ldvr, int *ilo, int *ihi, float *lscale, float *rscale, float *abnrm, float *bbnrm, float *rconde, float *rcondv, npy_complex64 *work, int *lwork, float *rwork, int *iwork, int *bwork, int *info);
void F_FUNC(cggglm,CGGGLM)(int *n, int *m, int *p, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *d, npy_complex64 *x, npy_complex64 *y, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cgghrd,CGGHRD)(char *compq, char *compz, int *n, int *ilo, int *ihi, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *q, int *ldq, npy_complex64 *z, int *ldz, int *info);
void F_FUNC(cgglse,CGGLSE)(int *m, int *n, int *p, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *c, npy_complex64 *d, npy_complex64 *x, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cggqrf,CGGQRF)(int *n, int *m, int *p, npy_complex64 *a, int *lda, npy_complex64 *taua, npy_complex64 *b, int *ldb, npy_complex64 *taub, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cggrqf,CGGRQF)(int *m, int *p, int *n, npy_complex64 *a, int *lda, npy_complex64 *taua, npy_complex64 *b, int *ldb, npy_complex64 *taub, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cgtcon,CGTCON)(char *norm, int *n, npy_complex64 *dl, npy_complex64 *d, npy_complex64 *du, npy_complex64 *du2, int *ipiv, float *anorm, float *rcond, npy_complex64 *work, int *info);
void F_FUNC(cgtrfs,CGTRFS)(char *trans, int *n, int *nrhs, npy_complex64 *dl, npy_complex64 *d, npy_complex64 *du, npy_complex64 *dlf, npy_complex64 *df, npy_complex64 *duf, npy_complex64 *du2, int *ipiv, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cgtsv,CGTSV)(int *n, int *nrhs, npy_complex64 *dl, npy_complex64 *d, npy_complex64 *du, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(cgtsvx,CGTSVX)(char *fact, char *trans, int *n, int *nrhs, npy_complex64 *dl, npy_complex64 *d, npy_complex64 *du, npy_complex64 *dlf, npy_complex64 *df, npy_complex64 *duf, npy_complex64 *du2, int *ipiv, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *rcond, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cgttrf,CGTTRF)(int *n, npy_complex64 *dl, npy_complex64 *d, npy_complex64 *du, npy_complex64 *du2, int *ipiv, int *info);
void F_FUNC(cgttrs,CGTTRS)(char *trans, int *n, int *nrhs, npy_complex64 *dl, npy_complex64 *d, npy_complex64 *du, npy_complex64 *du2, int *ipiv, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(cgtts2,CGTTS2)(int *itrans, int *n, int *nrhs, npy_complex64 *dl, npy_complex64 *d, npy_complex64 *du, npy_complex64 *du2, int *ipiv, npy_complex64 *b, int *ldb);
void F_FUNC(chbev,CHBEV)(char *jobz, char *uplo, int *n, int *kd, npy_complex64 *ab, int *ldab, float *w, npy_complex64 *z, int *ldz, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(chbevd,CHBEVD)(char *jobz, char *uplo, int *n, int *kd, npy_complex64 *ab, int *ldab, float *w, npy_complex64 *z, int *ldz, npy_complex64 *work, int *lwork, float *rwork, int *lrwork, int *iwork, int *liwork, int *info);
void F_FUNC(chbevx,CHBEVX)(char *jobz, char *range, char *uplo, int *n, int *kd, npy_complex64 *ab, int *ldab, npy_complex64 *q, int *ldq, float *vl, float *vu, int *il, int *iu, float *abstol, int *m, float *w, npy_complex64 *z, int *ldz, npy_complex64 *work, float *rwork, int *iwork, int *ifail, int *info);
void F_FUNC(chbgst,CHBGST)(char *vect, char *uplo, int *n, int *ka, int *kb, npy_complex64 *ab, int *ldab, npy_complex64 *bb, int *ldbb, npy_complex64 *x, int *ldx, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(chbgv,CHBGV)(char *jobz, char *uplo, int *n, int *ka, int *kb, npy_complex64 *ab, int *ldab, npy_complex64 *bb, int *ldbb, float *w, npy_complex64 *z, int *ldz, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(chbgvd,CHBGVD)(char *jobz, char *uplo, int *n, int *ka, int *kb, npy_complex64 *ab, int *ldab, npy_complex64 *bb, int *ldbb, float *w, npy_complex64 *z, int *ldz, npy_complex64 *work, int *lwork, float *rwork, int *lrwork, int *iwork, int *liwork, int *info);
void F_FUNC(chbgvx,CHBGVX)(char *jobz, char *range, char *uplo, int *n, int *ka, int *kb, npy_complex64 *ab, int *ldab, npy_complex64 *bb, int *ldbb, npy_complex64 *q, int *ldq, float *vl, float *vu, int *il, int *iu, float *abstol, int *m, float *w, npy_complex64 *z, int *ldz, npy_complex64 *work, float *rwork, int *iwork, int *ifail, int *info);
void F_FUNC(chbtrd,CHBTRD)(char *vect, char *uplo, int *n, int *kd, npy_complex64 *ab, int *ldab, float *d, float *e, npy_complex64 *q, int *ldq, npy_complex64 *work, int *info);
void F_FUNC(checon,CHECON)(char *uplo, int *n, npy_complex64 *a, int *lda, int *ipiv, float *anorm, float *rcond, npy_complex64 *work, int *info);
void F_FUNC(cheequb,CHEEQUB)(char *uplo, int *n, npy_complex64 *a, int *lda, float *s, float *scond, float *amax, npy_complex64 *work, int *info);
void F_FUNC(cheev,CHEEV)(char *jobz, char *uplo, int *n, npy_complex64 *a, int *lda, float *w, npy_complex64 *work, int *lwork, float *rwork, int *info);
void F_FUNC(cheevd,CHEEVD)(char *jobz, char *uplo, int *n, npy_complex64 *a, int *lda, float *w, npy_complex64 *work, int *lwork, float *rwork, int *lrwork, int *iwork, int *liwork, int *info);
void F_FUNC(cheevr,CHEEVR)(char *jobz, char *range, char *uplo, int *n, npy_complex64 *a, int *lda, float *vl, float *vu, int *il, int *iu, float *abstol, int *m, float *w, npy_complex64 *z, int *ldz, int *isuppz, npy_complex64 *work, int *lwork, float *rwork, int *lrwork, int *iwork, int *liwork, int *info);
void F_FUNC(cheevx,CHEEVX)(char *jobz, char *range, char *uplo, int *n, npy_complex64 *a, int *lda, float *vl, float *vu, int *il, int *iu, float *abstol, int *m, float *w, npy_complex64 *z, int *ldz, npy_complex64 *work, int *lwork, float *rwork, int *iwork, int *ifail, int *info);
void F_FUNC(chegs2,CHEGS2)(int *itype, char *uplo, int *n, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(chegst,CHEGST)(int *itype, char *uplo, int *n, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(chegv,CHEGV)(int *itype, char *jobz, char *uplo, int *n, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, float *w, npy_complex64 *work, int *lwork, float *rwork, int *info);
void F_FUNC(chegvd,CHEGVD)(int *itype, char *jobz, char *uplo, int *n, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, float *w, npy_complex64 *work, int *lwork, float *rwork, int *lrwork, int *iwork, int *liwork, int *info);
void F_FUNC(chegvx,CHEGVX)(int *itype, char *jobz, char *range, char *uplo, int *n, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, float *vl, float *vu, int *il, int *iu, float *abstol, int *m, float *w, npy_complex64 *z, int *ldz, npy_complex64 *work, int *lwork, float *rwork, int *iwork, int *ifail, int *info);
void F_FUNC(cherfs,CHERFS)(char *uplo, int *n, int *nrhs, npy_complex64 *a, int *lda, npy_complex64 *af, int *ldaf, int *ipiv, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(chesv,CHESV)(char *uplo, int *n, int *nrhs, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *b, int *ldb, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(chesvx,CHESVX)(char *fact, char *uplo, int *n, int *nrhs, npy_complex64 *a, int *lda, npy_complex64 *af, int *ldaf, int *ipiv, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *rcond, float *ferr, float *berr, npy_complex64 *work, int *lwork, float *rwork, int *info);
void F_FUNC(cheswapr,CHESWAPR)(char *uplo, int *n, npy_complex64 *a, int *lda, int *i1, int *i2);
void F_FUNC(chetd2,CHETD2)(char *uplo, int *n, npy_complex64 *a, int *lda, float *d, float *e, npy_complex64 *tau, int *info);
void F_FUNC(chetf2,CHETF2)(char *uplo, int *n, npy_complex64 *a, int *lda, int *ipiv, int *info);
void F_FUNC(chetrd,CHETRD)(char *uplo, int *n, npy_complex64 *a, int *lda, float *d, float *e, npy_complex64 *tau, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(chetrf,CHETRF)(char *uplo, int *n, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(chetri,CHETRI)(char *uplo, int *n, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *work, int *info);
void F_FUNC(chetri2,CHETRI2)(char *uplo, int *n, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(chetri2x,CHETRI2X)(char *uplo, int *n, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *work, int *nb, int *info);
void F_FUNC(chetrs,CHETRS)(char *uplo, int *n, int *nrhs, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(chetrs2,CHETRS2)(char *uplo, int *n, int *nrhs, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *b, int *ldb, npy_complex64 *work, int *info);
void F_FUNC(chfrk,CHFRK)(char *transr, char *uplo, char *trans, int *n, int *k, float *alpha, npy_complex64 *a, int *lda, float *beta, npy_complex64 *c);
void F_FUNC(chgeqz,CHGEQZ)(char *job, char *compq, char *compz, int *n, int *ilo, int *ihi, npy_complex64 *h, int *ldh, npy_complex64 *t, int *ldt, npy_complex64 *alpha, npy_complex64 *beta, npy_complex64 *q, int *ldq, npy_complex64 *z, int *ldz, npy_complex64 *work, int *lwork, float *rwork, int *info);
void F_FUNC(chpcon,CHPCON)(char *uplo, int *n, npy_complex64 *ap, int *ipiv, float *anorm, float *rcond, npy_complex64 *work, int *info);
void F_FUNC(chpev,CHPEV)(char *jobz, char *uplo, int *n, npy_complex64 *ap, float *w, npy_complex64 *z, int *ldz, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(chpevd,CHPEVD)(char *jobz, char *uplo, int *n, npy_complex64 *ap, float *w, npy_complex64 *z, int *ldz, npy_complex64 *work, int *lwork, float *rwork, int *lrwork, int *iwork, int *liwork, int *info);
void F_FUNC(chpevx,CHPEVX)(char *jobz, char *range, char *uplo, int *n, npy_complex64 *ap, float *vl, float *vu, int *il, int *iu, float *abstol, int *m, float *w, npy_complex64 *z, int *ldz, npy_complex64 *work, float *rwork, int *iwork, int *ifail, int *info);
void F_FUNC(chpgst,CHPGST)(int *itype, char *uplo, int *n, npy_complex64 *ap, npy_complex64 *bp, int *info);
void F_FUNC(chpgv,CHPGV)(int *itype, char *jobz, char *uplo, int *n, npy_complex64 *ap, npy_complex64 *bp, float *w, npy_complex64 *z, int *ldz, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(chpgvd,CHPGVD)(int *itype, char *jobz, char *uplo, int *n, npy_complex64 *ap, npy_complex64 *bp, float *w, npy_complex64 *z, int *ldz, npy_complex64 *work, int *lwork, float *rwork, int *lrwork, int *iwork, int *liwork, int *info);
void F_FUNC(chpgvx,CHPGVX)(int *itype, char *jobz, char *range, char *uplo, int *n, npy_complex64 *ap, npy_complex64 *bp, float *vl, float *vu, int *il, int *iu, float *abstol, int *m, float *w, npy_complex64 *z, int *ldz, npy_complex64 *work, float *rwork, int *iwork, int *ifail, int *info);
void F_FUNC(chprfs,CHPRFS)(char *uplo, int *n, int *nrhs, npy_complex64 *ap, npy_complex64 *afp, int *ipiv, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(chpsv,CHPSV)(char *uplo, int *n, int *nrhs, npy_complex64 *ap, int *ipiv, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(chpsvx,CHPSVX)(char *fact, char *uplo, int *n, int *nrhs, npy_complex64 *ap, npy_complex64 *afp, int *ipiv, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *rcond, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(chptrd,CHPTRD)(char *uplo, int *n, npy_complex64 *ap, float *d, float *e, npy_complex64 *tau, int *info);
void F_FUNC(chptrf,CHPTRF)(char *uplo, int *n, npy_complex64 *ap, int *ipiv, int *info);
void F_FUNC(chptri,CHPTRI)(char *uplo, int *n, npy_complex64 *ap, int *ipiv, npy_complex64 *work, int *info);
void F_FUNC(chptrs,CHPTRS)(char *uplo, int *n, int *nrhs, npy_complex64 *ap, int *ipiv, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(chsein,CHSEIN)(char *side, char *eigsrc, char *initv, int *select, int *n, npy_complex64 *h, int *ldh, npy_complex64 *w, npy_complex64 *vl, int *ldvl, npy_complex64 *vr, int *ldvr, int *mm, int *m, npy_complex64 *work, float *rwork, int *ifaill, int *ifailr, int *info);
void F_FUNC(chseqr,CHSEQR)(char *job, char *compz, int *n, int *ilo, int *ihi, npy_complex64 *h, int *ldh, npy_complex64 *w, npy_complex64 *z, int *ldz, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(clabrd,CLABRD)(int *m, int *n, int *nb, npy_complex64 *a, int *lda, float *d, float *e, npy_complex64 *tauq, npy_complex64 *taup, npy_complex64 *x, int *ldx, npy_complex64 *y, int *ldy);
void F_FUNC(clacgv,CLACGV)(int *n, npy_complex64 *x, int *incx);
void F_FUNC(clacn2,CLACN2)(int *n, npy_complex64 *v, npy_complex64 *x, float *est, int *kase, int *isave);
void F_FUNC(clacon,CLACON)(int *n, npy_complex64 *v, npy_complex64 *x, float *est, int *kase);
void F_FUNC(clacp2,CLACP2)(char *uplo, int *m, int *n, float *a, int *lda, npy_complex64 *b, int *ldb);
void F_FUNC(clacpy,CLACPY)(char *uplo, int *m, int *n, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb);
void F_FUNC(clacrm,CLACRM)(int *m, int *n, npy_complex64 *a, int *lda, float *b, int *ldb, npy_complex64 *c, int *ldc, float *rwork);
void F_FUNC(clacrt,CLACRT)(int *n, npy_complex64 *cx, int *incx, npy_complex64 *cy, int *incy, npy_complex64 *c, npy_complex64 *s);
void F_FUNC(claed0,CLAED0)(int *qsiz, int *n, float *d, float *e, npy_complex64 *q, int *ldq, npy_complex64 *qstore, int *ldqs, float *rwork, int *iwork, int *info);
void F_FUNC(claed7,CLAED7)(int *n, int *cutpnt, int *qsiz, int *tlvls, int *curlvl, int *curpbm, float *d, npy_complex64 *q, int *ldq, float *rho, int *indxq, float *qstore, int *qptr, int *prmptr, int *perm, int *givptr, int *givcol, float *givnum, npy_complex64 *work, float *rwork, int *iwork, int *info);
void F_FUNC(claed8,CLAED8)(int *k, int *n, int *qsiz, npy_complex64 *q, int *ldq, float *d, float *rho, int *cutpnt, float *z, float *dlamda, npy_complex64 *q2, int *ldq2, float *w, int *indxp, int *indx, int *indxq, int *perm, int *givptr, int *givcol, float *givnum, int *info);
void F_FUNC(claein,CLAEIN)(int *rightv, int *noinit, int *n, npy_complex64 *h, int *ldh, npy_complex64 *w, npy_complex64 *v, npy_complex64 *b, int *ldb, float *rwork, float *eps3, float *smlnum, int *info);
void F_FUNC(claesy,CLAESY)(npy_complex64 *a, npy_complex64 *b, npy_complex64 *c, npy_complex64 *rt1, npy_complex64 *rt2, npy_complex64 *evscal, npy_complex64 *cs1, npy_complex64 *sn1);
void F_FUNC(claev2,CLAEV2)(npy_complex64 *a, npy_complex64 *b, npy_complex64 *c, float *rt1, float *rt2, float *cs1, npy_complex64 *sn1);
void F_FUNC(clag2z,CLAG2Z)(int *m, int *n, npy_complex64 *sa, int *ldsa, npy_complex128 *a, int *lda, int *info);
void F_FUNC(clags2,CLAGS2)(int *upper, float *a1, npy_complex64 *a2, float *a3, float *b1, npy_complex64 *b2, float *b3, float *csu, npy_complex64 *snu, float *csv, npy_complex64 *snv, float *csq, npy_complex64 *snq);
void F_FUNC(clagtm,CLAGTM)(char *trans, int *n, int *nrhs, float *alpha, npy_complex64 *dl, npy_complex64 *d, npy_complex64 *du, npy_complex64 *x, int *ldx, float *beta, npy_complex64 *b, int *ldb);
void F_FUNC(clahef,CLAHEF)(char *uplo, int *n, int *nb, int *kb, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *w, int *ldw, int *info);
void F_FUNC(clahqr,CLAHQR)(int *wantt, int *wantz, int *n, int *ilo, int *ihi, npy_complex64 *h, int *ldh, npy_complex64 *w, int *iloz, int *ihiz, npy_complex64 *z, int *ldz, int *info);
void F_FUNC(clahr2,CLAHR2)(int *n, int *k, int *nb, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *t, int *ldt, npy_complex64 *y, int *ldy);
void F_FUNC(claic1,CLAIC1)(int *job, int *j, npy_complex64 *x, float *sest, npy_complex64 *w, npy_complex64 *gamma, float *sestpr, npy_complex64 *s, npy_complex64 *c);
void F_FUNC(clals0,CLALS0)(int *icompq, int *nl, int *nr, int *sqre, int *nrhs, npy_complex64 *b, int *ldb, npy_complex64 *bx, int *ldbx, int *perm, int *givptr, int *givcol, int *ldgcol, float *givnum, int *ldgnum, float *poles, float *difl, float *difr, float *z, int *k, float *c, float *s, float *rwork, int *info);
void F_FUNC(clalsa,CLALSA)(int *icompq, int *smlsiz, int *n, int *nrhs, npy_complex64 *b, int *ldb, npy_complex64 *bx, int *ldbx, float *u, int *ldu, float *vt, int *k, float *difl, float *difr, float *z, float *poles, int *givptr, int *givcol, int *ldgcol, int *perm, float *givnum, float *c, float *s, float *rwork, int *iwork, int *info);
void F_FUNC(clalsd,CLALSD)(char *uplo, int *smlsiz, int *n, int *nrhs, float *d, float *e, npy_complex64 *b, int *ldb, float *rcond, int *rank, npy_complex64 *work, float *rwork, int *iwork, int *info);
void F_FUNC(clapll,CLAPLL)(int *n, npy_complex64 *x, int *incx, npy_complex64 *y, int *incy, float *ssmin);
void F_FUNC(clapmr,CLAPMR)(int *forwrd, int *m, int *n, npy_complex64 *x, int *ldx, int *k);
void F_FUNC(clapmt,CLAPMT)(int *forwrd, int *m, int *n, npy_complex64 *x, int *ldx, int *k);
void F_FUNC(claqgb,CLAQGB)(int *m, int *n, int *kl, int *ku, npy_complex64 *ab, int *ldab, float *r, float *c, float *rowcnd, float *colcnd, float *amax, char *equed);
void F_FUNC(claqge,CLAQGE)(int *m, int *n, npy_complex64 *a, int *lda, float *r, float *c, float *rowcnd, float *colcnd, float *amax, char *equed);
void F_FUNC(claqhb,CLAQHB)(char *uplo, int *n, int *kd, npy_complex64 *ab, int *ldab, float *s, float *scond, float *amax, char *equed);
void F_FUNC(claqhe,CLAQHE)(char *uplo, int *n, npy_complex64 *a, int *lda, float *s, float *scond, float *amax, char *equed);
void F_FUNC(claqhp,CLAQHP)(char *uplo, int *n, npy_complex64 *ap, float *s, float *scond, float *amax, char *equed);
void F_FUNC(claqp2,CLAQP2)(int *m, int *n, int *offset, npy_complex64 *a, int *lda, int *jpvt, npy_complex64 *tau, float *vn1, float *vn2, npy_complex64 *work);
void F_FUNC(claqps,CLAQPS)(int *m, int *n, int *offset, int *nb, int *kb, npy_complex64 *a, int *lda, int *jpvt, npy_complex64 *tau, float *vn1, float *vn2, npy_complex64 *auxv, npy_complex64 *f, int *ldf);
void F_FUNC(claqr0,CLAQR0)(int *wantt, int *wantz, int *n, int *ilo, int *ihi, npy_complex64 *h, int *ldh, npy_complex64 *w, int *iloz, int *ihiz, npy_complex64 *z, int *ldz, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(claqr1,CLAQR1)(int *n, npy_complex64 *h, int *ldh, npy_complex64 *s1, npy_complex64 *s2, npy_complex64 *v);
void F_FUNC(claqr2,CLAQR2)(int *wantt, int *wantz, int *n, int *ktop, int *kbot, int *nw, npy_complex64 *h, int *ldh, int *iloz, int *ihiz, npy_complex64 *z, int *ldz, int *ns, int *nd, npy_complex64 *sh, npy_complex64 *v, int *ldv, int *nh, npy_complex64 *t, int *ldt, int *nv, npy_complex64 *wv, int *ldwv, npy_complex64 *work, int *lwork);
void F_FUNC(claqr3,CLAQR3)(int *wantt, int *wantz, int *n, int *ktop, int *kbot, int *nw, npy_complex64 *h, int *ldh, int *iloz, int *ihiz, npy_complex64 *z, int *ldz, int *ns, int *nd, npy_complex64 *sh, npy_complex64 *v, int *ldv, int *nh, npy_complex64 *t, int *ldt, int *nv, npy_complex64 *wv, int *ldwv, npy_complex64 *work, int *lwork);
void F_FUNC(claqr4,CLAQR4)(int *wantt, int *wantz, int *n, int *ilo, int *ihi, npy_complex64 *h, int *ldh, npy_complex64 *w, int *iloz, int *ihiz, npy_complex64 *z, int *ldz, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(claqr5,CLAQR5)(int *wantt, int *wantz, int *kacc22, int *n, int *ktop, int *kbot, int *nshfts, npy_complex64 *s, npy_complex64 *h, int *ldh, int *iloz, int *ihiz, npy_complex64 *z, int *ldz, npy_complex64 *v, int *ldv, npy_complex64 *u, int *ldu, int *nv, npy_complex64 *wv, int *ldwv, int *nh, npy_complex64 *wh, int *ldwh);
void F_FUNC(claqsb,CLAQSB)(char *uplo, int *n, int *kd, npy_complex64 *ab, int *ldab, float *s, float *scond, float *amax, char *equed);
void F_FUNC(claqsp,CLAQSP)(char *uplo, int *n, npy_complex64 *ap, float *s, float *scond, float *amax, char *equed);
void F_FUNC(claqsy,CLAQSY)(char *uplo, int *n, npy_complex64 *a, int *lda, float *s, float *scond, float *amax, char *equed);
void F_FUNC(clar1v,CLAR1V)(int *n, int *b1, int *bn, float *lambda, float *d, float *l, float *ld, float *lld, float *pivmin, float *gaptol, npy_complex64 *z, int *wantnc, int *negcnt, float *ztz, float *mingma, int *r, int *isuppz, float *nrminv, float *resid, float *rqcorr, float *work);
void F_FUNC(clar2v,CLAR2V)(int *n, npy_complex64 *x, npy_complex64 *y, npy_complex64 *z, int *incx, float *c, npy_complex64 *s, int *incc);
void F_FUNC(clarcm,CLARCM)(int *m, int *n, float *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *c, int *ldc, float *rwork);
void F_FUNC(clarf,CLARF)(char *side, int *m, int *n, npy_complex64 *v, int *incv, npy_complex64 *tau, npy_complex64 *c, int *ldc, npy_complex64 *work);
void F_FUNC(clarfb,CLARFB)(char *side, char *trans, char *direct, char *storev, int *m, int *n, int *k, npy_complex64 *v, int *ldv, npy_complex64 *t, int *ldt, npy_complex64 *c, int *ldc, npy_complex64 *work, int *ldwork);
void F_FUNC(clarfg,CLARFG)(int *n, npy_complex64 *alpha, npy_complex64 *x, int *incx, npy_complex64 *tau);
void F_FUNC(clarfgp,CLARFGP)(int *n, npy_complex64 *alpha, npy_complex64 *x, int *incx, npy_complex64 *tau);
void F_FUNC(clarft,CLARFT)(char *direct, char *storev, int *n, int *k, npy_complex64 *v, int *ldv, npy_complex64 *tau, npy_complex64 *t, int *ldt);
void F_FUNC(clarfx,CLARFX)(char *side, int *m, int *n, npy_complex64 *v, npy_complex64 *tau, npy_complex64 *c, int *ldc, npy_complex64 *work);
void F_FUNC(clargv,CLARGV)(int *n, npy_complex64 *x, int *incx, npy_complex64 *y, int *incy, float *c, int *incc);
void F_FUNC(clarnv,CLARNV)(int *idist, int *iseed, int *n, npy_complex64 *x);
void F_FUNC(clarrv,CLARRV)(int *n, float *vl, float *vu, float *d, float *l, float *pivmin, int *isplit, int *m, int *dol, int *dou, float *minrgp, float *rtol1, float *rtol2, float *w, float *werr, float *wgap, int *iblock, int *indexw, float *gers, npy_complex64 *z, int *ldz, int *isuppz, float *work, int *iwork, int *info);
void F_FUNC(clartg,CLARTG)(npy_complex64 *f, npy_complex64 *g, float *cs, npy_complex64 *sn, npy_complex64 *r);
void F_FUNC(clartv,CLARTV)(int *n, npy_complex64 *x, int *incx, npy_complex64 *y, int *incy, float *c, npy_complex64 *s, int *incc);
void F_FUNC(clarz,CLARZ)(char *side, int *m, int *n, int *l, npy_complex64 *v, int *incv, npy_complex64 *tau, npy_complex64 *c, int *ldc, npy_complex64 *work);
void F_FUNC(clarzb,CLARZB)(char *side, char *trans, char *direct, char *storev, int *m, int *n, int *k, int *l, npy_complex64 *v, int *ldv, npy_complex64 *t, int *ldt, npy_complex64 *c, int *ldc, npy_complex64 *work, int *ldwork);
void F_FUNC(clarzt,CLARZT)(char *direct, char *storev, int *n, int *k, npy_complex64 *v, int *ldv, npy_complex64 *tau, npy_complex64 *t, int *ldt);
void F_FUNC(clascl,CLASCL)(char *type_bn, int *kl, int *ku, float *cfrom, float *cto, int *m, int *n, npy_complex64 *a, int *lda, int *info);
void F_FUNC(claset,CLASET)(char *uplo, int *m, int *n, npy_complex64 *alpha, npy_complex64 *beta, npy_complex64 *a, int *lda);
void F_FUNC(clasr,CLASR)(char *side, char *pivot, char *direct, int *m, int *n, float *c, float *s, npy_complex64 *a, int *lda);
void F_FUNC(classq,CLASSQ)(int *n, npy_complex64 *x, int *incx, float *scale, float *sumsq);
void F_FUNC(claswp,CLASWP)(int *n, npy_complex64 *a, int *lda, int *k1, int *k2, int *ipiv, int *incx);
void F_FUNC(clasyf,CLASYF)(char *uplo, int *n, int *nb, int *kb, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *w, int *ldw, int *info);
void F_FUNC(clatbs,CLATBS)(char *uplo, char *trans, char *diag, char *normin, int *n, int *kd, npy_complex64 *ab, int *ldab, npy_complex64 *x, float *scale, float *cnorm, int *info);
void F_FUNC(clatdf,CLATDF)(int *ijob, int *n, npy_complex64 *z, int *ldz, npy_complex64 *rhs, float *rdsum, float *rdscal, int *ipiv, int *jpiv);
void F_FUNC(clatps,CLATPS)(char *uplo, char *trans, char *diag, char *normin, int *n, npy_complex64 *ap, npy_complex64 *x, float *scale, float *cnorm, int *info);
void F_FUNC(clatrd,CLATRD)(char *uplo, int *n, int *nb, npy_complex64 *a, int *lda, float *e, npy_complex64 *tau, npy_complex64 *w, int *ldw);
void F_FUNC(clatrs,CLATRS)(char *uplo, char *trans, char *diag, char *normin, int *n, npy_complex64 *a, int *lda, npy_complex64 *x, float *scale, float *cnorm, int *info);
void F_FUNC(clatrz,CLATRZ)(int *m, int *n, int *l, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work);
void F_FUNC(clauu2,CLAUU2)(char *uplo, int *n, npy_complex64 *a, int *lda, int *info);
void F_FUNC(clauum,CLAUUM)(char *uplo, int *n, npy_complex64 *a, int *lda, int *info);
void F_FUNC(cpbcon,CPBCON)(char *uplo, int *n, int *kd, npy_complex64 *ab, int *ldab, float *anorm, float *rcond, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cpbequ,CPBEQU)(char *uplo, int *n, int *kd, npy_complex64 *ab, int *ldab, float *s, float *scond, float *amax, int *info);
void F_FUNC(cpbrfs,CPBRFS)(char *uplo, int *n, int *kd, int *nrhs, npy_complex64 *ab, int *ldab, npy_complex64 *afb, int *ldafb, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cpbstf,CPBSTF)(char *uplo, int *n, int *kd, npy_complex64 *ab, int *ldab, int *info);
void F_FUNC(cpbsv,CPBSV)(char *uplo, int *n, int *kd, int *nrhs, npy_complex64 *ab, int *ldab, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(cpbsvx,CPBSVX)(char *fact, char *uplo, int *n, int *kd, int *nrhs, npy_complex64 *ab, int *ldab, npy_complex64 *afb, int *ldafb, char *equed, float *s, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *rcond, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cpbtf2,CPBTF2)(char *uplo, int *n, int *kd, npy_complex64 *ab, int *ldab, int *info);
void F_FUNC(cpbtrf,CPBTRF)(char *uplo, int *n, int *kd, npy_complex64 *ab, int *ldab, int *info);
void F_FUNC(cpbtrs,CPBTRS)(char *uplo, int *n, int *kd, int *nrhs, npy_complex64 *ab, int *ldab, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(cpftrf,CPFTRF)(char *transr, char *uplo, int *n, npy_complex64 *a, int *info);
void F_FUNC(cpftri,CPFTRI)(char *transr, char *uplo, int *n, npy_complex64 *a, int *info);
void F_FUNC(cpftrs,CPFTRS)(char *transr, char *uplo, int *n, int *nrhs, npy_complex64 *a, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(cpocon,CPOCON)(char *uplo, int *n, npy_complex64 *a, int *lda, float *anorm, float *rcond, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cpoequ,CPOEQU)(int *n, npy_complex64 *a, int *lda, float *s, float *scond, float *amax, int *info);
void F_FUNC(cpoequb,CPOEQUB)(int *n, npy_complex64 *a, int *lda, float *s, float *scond, float *amax, int *info);
void F_FUNC(cporfs,CPORFS)(char *uplo, int *n, int *nrhs, npy_complex64 *a, int *lda, npy_complex64 *af, int *ldaf, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cposv,CPOSV)(char *uplo, int *n, int *nrhs, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(cposvx,CPOSVX)(char *fact, char *uplo, int *n, int *nrhs, npy_complex64 *a, int *lda, npy_complex64 *af, int *ldaf, char *equed, float *s, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *rcond, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cpotf2,CPOTF2)(char *uplo, int *n, npy_complex64 *a, int *lda, int *info);
void F_FUNC(cpotrf,CPOTRF)(char *uplo, int *n, npy_complex64 *a, int *lda, int *info);
void F_FUNC(cpotri,CPOTRI)(char *uplo, int *n, npy_complex64 *a, int *lda, int *info);
void F_FUNC(cpotrs,CPOTRS)(char *uplo, int *n, int *nrhs, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(cppcon,CPPCON)(char *uplo, int *n, npy_complex64 *ap, float *anorm, float *rcond, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cppequ,CPPEQU)(char *uplo, int *n, npy_complex64 *ap, float *s, float *scond, float *amax, int *info);
void F_FUNC(cpprfs,CPPRFS)(char *uplo, int *n, int *nrhs, npy_complex64 *ap, npy_complex64 *afp, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cppsv,CPPSV)(char *uplo, int *n, int *nrhs, npy_complex64 *ap, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(cppsvx,CPPSVX)(char *fact, char *uplo, int *n, int *nrhs, npy_complex64 *ap, npy_complex64 *afp, char *equed, float *s, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *rcond, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cpptrf,CPPTRF)(char *uplo, int *n, npy_complex64 *ap, int *info);
void F_FUNC(cpptri,CPPTRI)(char *uplo, int *n, npy_complex64 *ap, int *info);
void F_FUNC(cpptrs,CPPTRS)(char *uplo, int *n, int *nrhs, npy_complex64 *ap, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(cpstf2,CPSTF2)(char *uplo, int *n, npy_complex64 *a, int *lda, int *piv, int *rank, float *tol, float *work, int *info);
void F_FUNC(cpstrf,CPSTRF)(char *uplo, int *n, npy_complex64 *a, int *lda, int *piv, int *rank, float *tol, float *work, int *info);
void F_FUNC(cptcon,CPTCON)(int *n, float *d, npy_complex64 *e, float *anorm, float *rcond, float *rwork, int *info);
void F_FUNC(cpteqr,CPTEQR)(char *compz, int *n, float *d, float *e, npy_complex64 *z, int *ldz, float *work, int *info);
void F_FUNC(cptrfs,CPTRFS)(char *uplo, int *n, int *nrhs, float *d, npy_complex64 *e, float *df, npy_complex64 *ef, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cptsv,CPTSV)(int *n, int *nrhs, float *d, npy_complex64 *e, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(cptsvx,CPTSVX)(char *fact, int *n, int *nrhs, float *d, npy_complex64 *e, float *df, npy_complex64 *ef, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *rcond, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cpttrf,CPTTRF)(int *n, float *d, npy_complex64 *e, int *info);
void F_FUNC(cpttrs,CPTTRS)(char *uplo, int *n, int *nrhs, float *d, npy_complex64 *e, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(cptts2,CPTTS2)(int *iuplo, int *n, int *nrhs, float *d, npy_complex64 *e, npy_complex64 *b, int *ldb);
void F_FUNC(crot,CROT)(int *n, npy_complex64 *cx, int *incx, npy_complex64 *cy, int *incy, float *c, npy_complex64 *s);
void F_FUNC(cspcon,CSPCON)(char *uplo, int *n, npy_complex64 *ap, int *ipiv, float *anorm, float *rcond, npy_complex64 *work, int *info);
void F_FUNC(cspmv,CSPMV)(char *uplo, int *n, npy_complex64 *alpha, npy_complex64 *ap, npy_complex64 *x, int *incx, npy_complex64 *beta, npy_complex64 *y, int *incy);
void F_FUNC(cspr,CSPR)(char *uplo, int *n, npy_complex64 *alpha, npy_complex64 *x, int *incx, npy_complex64 *ap);
void F_FUNC(csprfs,CSPRFS)(char *uplo, int *n, int *nrhs, npy_complex64 *ap, npy_complex64 *afp, int *ipiv, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(cspsv,CSPSV)(char *uplo, int *n, int *nrhs, npy_complex64 *ap, int *ipiv, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(cspsvx,CSPSVX)(char *fact, char *uplo, int *n, int *nrhs, npy_complex64 *ap, npy_complex64 *afp, int *ipiv, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *rcond, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(csptrf,CSPTRF)(char *uplo, int *n, npy_complex64 *ap, int *ipiv, int *info);
void F_FUNC(csptri,CSPTRI)(char *uplo, int *n, npy_complex64 *ap, int *ipiv, npy_complex64 *work, int *info);
void F_FUNC(csptrs,CSPTRS)(char *uplo, int *n, int *nrhs, npy_complex64 *ap, int *ipiv, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(csrscl,CSRSCL)(int *n, float *sa, npy_complex64 *sx, int *incx);
void F_FUNC(cstedc,CSTEDC)(char *compz, int *n, float *d, float *e, npy_complex64 *z, int *ldz, npy_complex64 *work, int *lwork, float *rwork, int *lrwork, int *iwork, int *liwork, int *info);
void F_FUNC(cstegr,CSTEGR)(char *jobz, char *range, int *n, float *d, float *e, float *vl, float *vu, int *il, int *iu, float *abstol, int *m, float *w, npy_complex64 *z, int *ldz, int *isuppz, float *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(cstein,CSTEIN)(int *n, float *d, float *e, int *m, float *w, int *iblock, int *isplit, npy_complex64 *z, int *ldz, float *work, int *iwork, int *ifail, int *info);
void F_FUNC(cstemr,CSTEMR)(char *jobz, char *range, int *n, float *d, float *e, float *vl, float *vu, int *il, int *iu, int *m, float *w, npy_complex64 *z, int *ldz, int *nzc, int *isuppz, int *tryrac, float *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(csteqr,CSTEQR)(char *compz, int *n, float *d, float *e, npy_complex64 *z, int *ldz, float *work, int *info);
void F_FUNC(csycon,CSYCON)(char *uplo, int *n, npy_complex64 *a, int *lda, int *ipiv, float *anorm, float *rcond, npy_complex64 *work, int *info);
void F_FUNC(csyconv,CSYCONV)(char *uplo, char *way, int *n, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *work, int *info);
void F_FUNC(csyequb,CSYEQUB)(char *uplo, int *n, npy_complex64 *a, int *lda, float *s, float *scond, float *amax, npy_complex64 *work, int *info);
void F_FUNC(csymv,CSYMV)(char *uplo, int *n, npy_complex64 *alpha, npy_complex64 *a, int *lda, npy_complex64 *x, int *incx, npy_complex64 *beta, npy_complex64 *y, int *incy);
void F_FUNC(csyr,CSYR)(char *uplo, int *n, npy_complex64 *alpha, npy_complex64 *x, int *incx, npy_complex64 *a, int *lda);
void F_FUNC(csyrfs,CSYRFS)(char *uplo, int *n, int *nrhs, npy_complex64 *a, int *lda, npy_complex64 *af, int *ldaf, int *ipiv, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(csysv,CSYSV)(char *uplo, int *n, int *nrhs, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *b, int *ldb, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(csysvx,CSYSVX)(char *fact, char *uplo, int *n, int *nrhs, npy_complex64 *a, int *lda, npy_complex64 *af, int *ldaf, int *ipiv, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *rcond, float *ferr, float *berr, npy_complex64 *work, int *lwork, float *rwork, int *info);
void F_FUNC(csyswapr,CSYSWAPR)(char *uplo, int *n, npy_complex64 *a, int *lda, int *i1, int *i2);
void F_FUNC(csytf2,CSYTF2)(char *uplo, int *n, npy_complex64 *a, int *lda, int *ipiv, int *info);
void F_FUNC(csytrf,CSYTRF)(char *uplo, int *n, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(csytri,CSYTRI)(char *uplo, int *n, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *work, int *info);
void F_FUNC(csytri2,CSYTRI2)(char *uplo, int *n, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(csytri2x,CSYTRI2X)(char *uplo, int *n, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *work, int *nb, int *info);
void F_FUNC(csytrs,CSYTRS)(char *uplo, int *n, int *nrhs, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(csytrs2,CSYTRS2)(char *uplo, int *n, int *nrhs, npy_complex64 *a, int *lda, int *ipiv, npy_complex64 *b, int *ldb, npy_complex64 *work, int *info);
void F_FUNC(ctbcon,CTBCON)(char *norm, char *uplo, char *diag, int *n, int *kd, npy_complex64 *ab, int *ldab, float *rcond, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(ctbrfs,CTBRFS)(char *uplo, char *trans, char *diag, int *n, int *kd, int *nrhs, npy_complex64 *ab, int *ldab, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(ctbtrs,CTBTRS)(char *uplo, char *trans, char *diag, int *n, int *kd, int *nrhs, npy_complex64 *ab, int *ldab, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(ctfsm,CTFSM)(char *transr, char *side, char *uplo, char *trans, char *diag, int *m, int *n, npy_complex64 *alpha, npy_complex64 *a, npy_complex64 *b, int *ldb);
void F_FUNC(ctftri,CTFTRI)(char *transr, char *uplo, char *diag, int *n, npy_complex64 *a, int *info);
void F_FUNC(ctfttp,CTFTTP)(char *transr, char *uplo, int *n, npy_complex64 *arf, npy_complex64 *ap, int *info);
void F_FUNC(ctfttr,CTFTTR)(char *transr, char *uplo, int *n, npy_complex64 *arf, npy_complex64 *a, int *lda, int *info);
void F_FUNC(ctgevc,CTGEVC)(char *side, char *howmny, int *select, int *n, npy_complex64 *s, int *lds, npy_complex64 *p, int *ldp, npy_complex64 *vl, int *ldvl, npy_complex64 *vr, int *ldvr, int *mm, int *m, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(ctgex2,CTGEX2)(int *wantq, int *wantz, int *n, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *q, int *ldq, npy_complex64 *z, int *ldz, int *j1, int *info);
void F_FUNC(ctgexc,CTGEXC)(int *wantq, int *wantz, int *n, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *q, int *ldq, npy_complex64 *z, int *ldz, int *ifst, int *ilst, int *info);
void F_FUNC(ctgsen,CTGSEN)(int *ijob, int *wantq, int *wantz, int *select, int *n, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *alpha, npy_complex64 *beta, npy_complex64 *q, int *ldq, npy_complex64 *z, int *ldz, int *m, float *pl, float *pr, float *dif, npy_complex64 *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(ctgsja,CTGSJA)(char *jobu, char *jobv, char *jobq, int *m, int *p, int *n, int *k, int *l, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, float *tola, float *tolb, float *alpha, float *beta, npy_complex64 *u, int *ldu, npy_complex64 *v, int *ldv, npy_complex64 *q, int *ldq, npy_complex64 *work, int *ncycle, int *info);
void F_FUNC(ctgsna,CTGSNA)(char *job, char *howmny, int *select, int *n, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *vl, int *ldvl, npy_complex64 *vr, int *ldvr, float *s, float *dif, int *mm, int *m, npy_complex64 *work, int *lwork, int *iwork, int *info);
void F_FUNC(ctgsy2,CTGSY2)(char *trans, int *ijob, int *m, int *n, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *c, int *ldc, npy_complex64 *d, int *ldd, npy_complex64 *e, int *lde, npy_complex64 *f, int *ldf, float *scale, float *rdsum, float *rdscal, int *info);
void F_FUNC(ctgsyl,CTGSYL)(char *trans, int *ijob, int *m, int *n, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *c, int *ldc, npy_complex64 *d, int *ldd, npy_complex64 *e, int *lde, npy_complex64 *f, int *ldf, float *scale, float *dif, npy_complex64 *work, int *lwork, int *iwork, int *info);
void F_FUNC(ctpcon,CTPCON)(char *norm, char *uplo, char *diag, int *n, npy_complex64 *ap, float *rcond, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(ctpmqrt,CTPMQRT)(char *side, char *trans, int *m, int *n, int *k, int *l, int *nb, npy_complex64 *v, int *ldv, npy_complex64 *t, int *ldt, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *work, int *info);
void F_FUNC(ctpqrt,CTPQRT)(int *m, int *n, int *l, int *nb, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *t, int *ldt, npy_complex64 *work, int *info);
void F_FUNC(ctpqrt2,CTPQRT2)(int *m, int *n, int *l, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *t, int *ldt, int *info);
void F_FUNC(ctprfb,CTPRFB)(char *side, char *trans, char *direct, char *storev, int *m, int *n, int *k, int *l, npy_complex64 *v, int *ldv, npy_complex64 *t, int *ldt, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *work, int *ldwork);
void F_FUNC(ctprfs,CTPRFS)(char *uplo, char *trans, char *diag, int *n, int *nrhs, npy_complex64 *ap, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(ctptri,CTPTRI)(char *uplo, char *diag, int *n, npy_complex64 *ap, int *info);
void F_FUNC(ctptrs,CTPTRS)(char *uplo, char *trans, char *diag, int *n, int *nrhs, npy_complex64 *ap, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(ctpttf,CTPTTF)(char *transr, char *uplo, int *n, npy_complex64 *ap, npy_complex64 *arf, int *info);
void F_FUNC(ctpttr,CTPTTR)(char *uplo, int *n, npy_complex64 *ap, npy_complex64 *a, int *lda, int *info);
void F_FUNC(ctrcon,CTRCON)(char *norm, char *uplo, char *diag, int *n, npy_complex64 *a, int *lda, float *rcond, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(ctrevc,CTREVC)(char *side, char *howmny, int *select, int *n, npy_complex64 *t, int *ldt, npy_complex64 *vl, int *ldvl, npy_complex64 *vr, int *ldvr, int *mm, int *m, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(ctrexc,CTREXC)(char *compq, int *n, npy_complex64 *t, int *ldt, npy_complex64 *q, int *ldq, int *ifst, int *ilst, int *info);
void F_FUNC(ctrrfs,CTRRFS)(char *uplo, char *trans, char *diag, int *n, int *nrhs, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *x, int *ldx, float *ferr, float *berr, npy_complex64 *work, float *rwork, int *info);
void F_FUNC(ctrsen,CTRSEN)(char *job, char *compq, int *select, int *n, npy_complex64 *t, int *ldt, npy_complex64 *q, int *ldq, npy_complex64 *w, int *m, float *s, float *sep, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(ctrsna,CTRSNA)(char *job, char *howmny, int *select, int *n, npy_complex64 *t, int *ldt, npy_complex64 *vl, int *ldvl, npy_complex64 *vr, int *ldvr, float *s, float *sep, int *mm, int *m, npy_complex64 *work, int *ldwork, float *rwork, int *info);
void F_FUNC(ctrsyl,CTRSYL)(char *trana, char *tranb, int *isgn, int *m, int *n, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *c, int *ldc, float *scale, int *info);
void F_FUNC(ctrti2,CTRTI2)(char *uplo, char *diag, int *n, npy_complex64 *a, int *lda, int *info);
void F_FUNC(ctrtri,CTRTRI)(char *uplo, char *diag, int *n, npy_complex64 *a, int *lda, int *info);
void F_FUNC(ctrtrs,CTRTRS)(char *uplo, char *trans, char *diag, int *n, int *nrhs, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, int *info);
void F_FUNC(ctrttf,CTRTTF)(char *transr, char *uplo, int *n, npy_complex64 *a, int *lda, npy_complex64 *arf, int *info);
void F_FUNC(ctrttp,CTRTTP)(char *uplo, int *n, npy_complex64 *a, int *lda, npy_complex64 *ap, int *info);
void F_FUNC(ctzrzf,CTZRZF)(int *m, int *n, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cunbdb,CUNBDB)(char *trans, char *signs, int *m, int *p, int *q, npy_complex64 *x11, int *ldx11, npy_complex64 *x12, int *ldx12, npy_complex64 *x21, int *ldx21, npy_complex64 *x22, int *ldx22, float *theta, float *phi, npy_complex64 *taup1, npy_complex64 *taup2, npy_complex64 *tauq1, npy_complex64 *tauq2, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cuncsd,CUNCSD)(char *jobu1, char *jobu2, char *jobv1t, char *jobv2t, char *trans, char *signs, int *m, int *p, int *q, npy_complex64 *x11, int *ldx11, npy_complex64 *x12, int *ldx12, npy_complex64 *x21, int *ldx21, npy_complex64 *x22, int *ldx22, float *theta, npy_complex64 *u1, int *ldu1, npy_complex64 *u2, int *ldu2, npy_complex64 *v1t, int *ldv1t, npy_complex64 *v2t, int *ldv2t, npy_complex64 *work, int *lwork, float *rwork, int *lrwork, int *iwork, int *info);
void F_FUNC(cung2l,CUNG2L)(int *m, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *info);
void F_FUNC(cung2r,CUNG2R)(int *m, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *info);
void F_FUNC(cungbr,CUNGBR)(char *vect, int *m, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cunghr,CUNGHR)(int *n, int *ilo, int *ihi, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cungl2,CUNGL2)(int *m, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *info);
void F_FUNC(cunglq,CUNGLQ)(int *m, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cungql,CUNGQL)(int *m, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cungqr,CUNGQR)(int *m, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cungr2,CUNGR2)(int *m, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *info);
void F_FUNC(cungrq,CUNGRQ)(int *m, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cungtr,CUNGTR)(char *uplo, int *n, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cunm2l,CUNM2L)(char *side, char *trans, int *m, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *c, int *ldc, npy_complex64 *work, int *info);
void F_FUNC(cunm2r,CUNM2R)(char *side, char *trans, int *m, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *c, int *ldc, npy_complex64 *work, int *info);
void F_FUNC(cunmbr,CUNMBR)(char *vect, char *side, char *trans, int *m, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *c, int *ldc, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cunmhr,CUNMHR)(char *side, char *trans, int *m, int *n, int *ilo, int *ihi, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *c, int *ldc, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cunml2,CUNML2)(char *side, char *trans, int *m, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *c, int *ldc, npy_complex64 *work, int *info);
void F_FUNC(cunmlq,CUNMLQ)(char *side, char *trans, int *m, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *c, int *ldc, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cunmql,CUNMQL)(char *side, char *trans, int *m, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *c, int *ldc, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cunmqr,CUNMQR)(char *side, char *trans, int *m, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *c, int *ldc, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cunmr2,CUNMR2)(char *side, char *trans, int *m, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *c, int *ldc, npy_complex64 *work, int *info);
void F_FUNC(cunmr3,CUNMR3)(char *side, char *trans, int *m, int *n, int *k, int *l, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *c, int *ldc, npy_complex64 *work, int *info);
void F_FUNC(cunmrq,CUNMRQ)(char *side, char *trans, int *m, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *c, int *ldc, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cunmrz,CUNMRZ)(char *side, char *trans, int *m, int *n, int *k, int *l, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *c, int *ldc, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cunmtr,CUNMTR)(char *side, char *uplo, char *trans, int *m, int *n, npy_complex64 *a, int *lda, npy_complex64 *tau, npy_complex64 *c, int *ldc, npy_complex64 *work, int *lwork, int *info);
void F_FUNC(cupgtr,CUPGTR)(char *uplo, int *n, npy_complex64 *ap, npy_complex64 *tau, npy_complex64 *q, int *ldq, npy_complex64 *work, int *info);
void F_FUNC(cupmtr,CUPMTR)(char *side, char *uplo, char *trans, int *m, int *n, npy_complex64 *ap, npy_complex64 *tau, npy_complex64 *c, int *ldc, npy_complex64 *work, int *info);
void F_FUNC(dbbcsd,DBBCSD)(char *jobu1, char *jobu2, char *jobv1t, char *jobv2t, char *trans, int *m, int *p, int *q, double *theta, double *phi, double *u1, int *ldu1, double *u2, int *ldu2, double *v1t, int *ldv1t, double *v2t, int *ldv2t, double *b11d, double *b11e, double *b12d, double *b12e, double *b21d, double *b21e, double *b22d, double *b22e, double *work, int *lwork, int *info);
void F_FUNC(dbdsdc,DBDSDC)(char *uplo, char *compq, int *n, double *d, double *e, double *u, int *ldu, double *vt, int *ldvt, double *q, int *iq, double *work, int *iwork, int *info);
void F_FUNC(dbdsqr,DBDSQR)(char *uplo, int *n, int *ncvt, int *nru, int *ncc, double *d, double *e, double *vt, int *ldvt, double *u, int *ldu, double *c, int *ldc, double *work, int *info);
void F_FUNC(ddisna,DDISNA)(char *job, int *m, int *n, double *d, double *sep, int *info);
void F_FUNC(dgbbrd,DGBBRD)(char *vect, int *m, int *n, int *ncc, int *kl, int *ku, double *ab, int *ldab, double *d, double *e, double *q, int *ldq, double *pt, int *ldpt, double *c, int *ldc, double *work, int *info);
void F_FUNC(dgbcon,DGBCON)(char *norm, int *n, int *kl, int *ku, double *ab, int *ldab, int *ipiv, double *anorm, double *rcond, double *work, int *iwork, int *info);
void F_FUNC(dgbequ,DGBEQU)(int *m, int *n, int *kl, int *ku, double *ab, int *ldab, double *r, double *c, double *rowcnd, double *colcnd, double *amax, int *info);
void F_FUNC(dgbequb,DGBEQUB)(int *m, int *n, int *kl, int *ku, double *ab, int *ldab, double *r, double *c, double *rowcnd, double *colcnd, double *amax, int *info);
void F_FUNC(dgbrfs,DGBRFS)(char *trans, int *n, int *kl, int *ku, int *nrhs, double *ab, int *ldab, double *afb, int *ldafb, int *ipiv, double *b, int *ldb, double *x, int *ldx, double *ferr, double *berr, double *work, int *iwork, int *info);
void F_FUNC(dgbsv,DGBSV)(int *n, int *kl, int *ku, int *nrhs, double *ab, int *ldab, int *ipiv, double *b, int *ldb, int *info);
void F_FUNC(dgbsvx,DGBSVX)(char *fact, char *trans, int *n, int *kl, int *ku, int *nrhs, double *ab, int *ldab, double *afb, int *ldafb, int *ipiv, char *equed, double *r, double *c, double *b, int *ldb, double *x, int *ldx, double *rcond, double *ferr, double *berr, double *work, int *iwork, int *info);
void F_FUNC(dgbtf2,DGBTF2)(int *m, int *n, int *kl, int *ku, double *ab, int *ldab, int *ipiv, int *info);
void F_FUNC(dgbtrf,DGBTRF)(int *m, int *n, int *kl, int *ku, double *ab, int *ldab, int *ipiv, int *info);
void F_FUNC(dgbtrs,DGBTRS)(char *trans, int *n, int *kl, int *ku, int *nrhs, double *ab, int *ldab, int *ipiv, double *b, int *ldb, int *info);
void F_FUNC(dgebak,DGEBAK)(char *job, char *side, int *n, int *ilo, int *ihi, double *scale, int *m, double *v, int *ldv, int *info);
void F_FUNC(dgebal,DGEBAL)(char *job, int *n, double *a, int *lda, int *ilo, int *ihi, double *scale, int *info);
void F_FUNC(dgebd2,DGEBD2)(int *m, int *n, double *a, int *lda, double *d, double *e, double *tauq, double *taup, double *work, int *info);
void F_FUNC(dgebrd,DGEBRD)(int *m, int *n, double *a, int *lda, double *d, double *e, double *tauq, double *taup, double *work, int *lwork, int *info);
void F_FUNC(dgecon,DGECON)(char *norm, int *n, double *a, int *lda, double *anorm, double *rcond, double *work, int *iwork, int *info);
void F_FUNC(dgeequ,DGEEQU)(int *m, int *n, double *a, int *lda, double *r, double *c, double *rowcnd, double *colcnd, double *amax, int *info);
void F_FUNC(dgeequb,DGEEQUB)(int *m, int *n, double *a, int *lda, double *r, double *c, double *rowcnd, double *colcnd, double *amax, int *info);
void F_FUNC(dgees,DGEES)(char *jobvs, char *sort, _dselect2 *select, int *n, double *a, int *lda, int *sdim, double *wr, double *wi, double *vs, int *ldvs, double *work, int *lwork, int *bwork, int *info);
void F_FUNC(dgeesx,DGEESX)(char *jobvs, char *sort, _dselect2 *select, char *sense, int *n, double *a, int *lda, int *sdim, double *wr, double *wi, double *vs, int *ldvs, double *rconde, double *rcondv, double *work, int *lwork, int *iwork, int *liwork, int *bwork, int *info);
void F_FUNC(dgeev,DGEEV)(char *jobvl, char *jobvr, int *n, double *a, int *lda, double *wr, double *wi, double *vl, int *ldvl, double *vr, int *ldvr, double *work, int *lwork, int *info);
void F_FUNC(dgeevx,DGEEVX)(char *balanc, char *jobvl, char *jobvr, char *sense, int *n, double *a, int *lda, double *wr, double *wi, double *vl, int *ldvl, double *vr, int *ldvr, int *ilo, int *ihi, double *scale, double *abnrm, double *rconde, double *rcondv, double *work, int *lwork, int *iwork, int *info);
void F_FUNC(dgehd2,DGEHD2)(int *n, int *ilo, int *ihi, double *a, int *lda, double *tau, double *work, int *info);
void F_FUNC(dgehrd,DGEHRD)(int *n, int *ilo, int *ihi, double *a, int *lda, double *tau, double *work, int *lwork, int *info);
void F_FUNC(dgejsv,DGEJSV)(char *joba, char *jobu, char *jobv, char *jobr, char *jobt, char *jobp, int *m, int *n, double *a, int *lda, double *sva, double *u, int *ldu, double *v, int *ldv, double *work, int *lwork, int *iwork, int *info);
void F_FUNC(dgelq2,DGELQ2)(int *m, int *n, double *a, int *lda, double *tau, double *work, int *info);
void F_FUNC(dgelqf,DGELQF)(int *m, int *n, double *a, int *lda, double *tau, double *work, int *lwork, int *info);
void F_FUNC(dgels,DGELS)(char *trans, int *m, int *n, int *nrhs, double *a, int *lda, double *b, int *ldb, double *work, int *lwork, int *info);
void F_FUNC(dgelsd,DGELSD)(int *m, int *n, int *nrhs, double *a, int *lda, double *b, int *ldb, double *s, double *rcond, int *rank, double *work, int *lwork, int *iwork, int *info);
void F_FUNC(dgelss,DGELSS)(int *m, int *n, int *nrhs, double *a, int *lda, double *b, int *ldb, double *s, double *rcond, int *rank, double *work, int *lwork, int *info);
void F_FUNC(dgelsy,DGELSY)(int *m, int *n, int *nrhs, double *a, int *lda, double *b, int *ldb, int *jpvt, double *rcond, int *rank, double *work, int *lwork, int *info);
void F_FUNC(dgemqrt,DGEMQRT)(char *side, char *trans, int *m, int *n, int *k, int *nb, double *v, int *ldv, double *t, int *ldt, double *c, int *ldc, double *work, int *info);
void F_FUNC(dgeql2,DGEQL2)(int *m, int *n, double *a, int *lda, double *tau, double *work, int *info);
void F_FUNC(dgeqlf,DGEQLF)(int *m, int *n, double *a, int *lda, double *tau, double *work, int *lwork, int *info);
void F_FUNC(dgeqp3,DGEQP3)(int *m, int *n, double *a, int *lda, int *jpvt, double *tau, double *work, int *lwork, int *info);
void F_FUNC(dgeqr2,DGEQR2)(int *m, int *n, double *a, int *lda, double *tau, double *work, int *info);
void F_FUNC(dgeqr2p,DGEQR2P)(int *m, int *n, double *a, int *lda, double *tau, double *work, int *info);
void F_FUNC(dgeqrf,DGEQRF)(int *m, int *n, double *a, int *lda, double *tau, double *work, int *lwork, int *info);
void F_FUNC(dgeqrfp,DGEQRFP)(int *m, int *n, double *a, int *lda, double *tau, double *work, int *lwork, int *info);
void F_FUNC(dgeqrt,DGEQRT)(int *m, int *n, int *nb, double *a, int *lda, double *t, int *ldt, double *work, int *info);
void F_FUNC(dgeqrt2,DGEQRT2)(int *m, int *n, double *a, int *lda, double *t, int *ldt, int *info);
void F_FUNC(dgeqrt3,DGEQRT3)(int *m, int *n, double *a, int *lda, double *t, int *ldt, int *info);
void F_FUNC(dgerfs,DGERFS)(char *trans, int *n, int *nrhs, double *a, int *lda, double *af, int *ldaf, int *ipiv, double *b, int *ldb, double *x, int *ldx, double *ferr, double *berr, double *work, int *iwork, int *info);
void F_FUNC(dgerq2,DGERQ2)(int *m, int *n, double *a, int *lda, double *tau, double *work, int *info);
void F_FUNC(dgerqf,DGERQF)(int *m, int *n, double *a, int *lda, double *tau, double *work, int *lwork, int *info);
void F_FUNC(dgesc2,DGESC2)(int *n, double *a, int *lda, double *rhs, int *ipiv, int *jpiv, double *scale);
void F_FUNC(dgesdd,DGESDD)(char *jobz, int *m, int *n, double *a, int *lda, double *s, double *u, int *ldu, double *vt, int *ldvt, double *work, int *lwork, int *iwork, int *info);
void F_FUNC(dgesv,DGESV)(int *n, int *nrhs, double *a, int *lda, int *ipiv, double *b, int *ldb, int *info);
void F_FUNC(dgesvd,DGESVD)(char *jobu, char *jobvt, int *m, int *n, double *a, int *lda, double *s, double *u, int *ldu, double *vt, int *ldvt, double *work, int *lwork, int *info);
void F_FUNC(dgesvj,DGESVJ)(char *joba, char *jobu, char *jobv, int *m, int *n, double *a, int *lda, double *sva, int *mv, double *v, int *ldv, double *work, int *lwork, int *info);
void F_FUNC(dgesvx,DGESVX)(char *fact, char *trans, int *n, int *nrhs, double *a, int *lda, double *af, int *ldaf, int *ipiv, char *equed, double *r, double *c, double *b, int *ldb, double *x, int *ldx, double *rcond, double *ferr, double *berr, double *work, int *iwork, int *info);
void F_FUNC(dgetc2,DGETC2)(int *n, double *a, int *lda, int *ipiv, int *jpiv, int *info);
void F_FUNC(dgetf2,DGETF2)(int *m, int *n, double *a, int *lda, int *ipiv, int *info);
void F_FUNC(dgetrf,DGETRF)(int *m, int *n, double *a, int *lda, int *ipiv, int *info);
void F_FUNC(dgetri,DGETRI)(int *n, double *a, int *lda, int *ipiv, double *work, int *lwork, int *info);
void F_FUNC(dgetrs,DGETRS)(char *trans, int *n, int *nrhs, double *a, int *lda, int *ipiv, double *b, int *ldb, int *info);
void F_FUNC(dggbak,DGGBAK)(char *job, char *side, int *n, int *ilo, int *ihi, double *lscale, double *rscale, int *m, double *v, int *ldv, int *info);
void F_FUNC(dggbal,DGGBAL)(char *job, int *n, double *a, int *lda, double *b, int *ldb, int *ilo, int *ihi, double *lscale, double *rscale, double *work, int *info);
void F_FUNC(dgges,DGGES)(char *jobvsl, char *jobvsr, char *sort, _dselect3 *selctg, int *n, double *a, int *lda, double *b, int *ldb, int *sdim, double *alphar, double *alphai, double *beta, double *vsl, int *ldvsl, double *vsr, int *ldvsr, double *work, int *lwork, int *bwork, int *info);
void F_FUNC(dggesx,DGGESX)(char *jobvsl, char *jobvsr, char *sort, _dselect3 *selctg, char *sense, int *n, double *a, int *lda, double *b, int *ldb, int *sdim, double *alphar, double *alphai, double *beta, double *vsl, int *ldvsl, double *vsr, int *ldvsr, double *rconde, double *rcondv, double *work, int *lwork, int *iwork, int *liwork, int *bwork, int *info);
void F_FUNC(dggev,DGGEV)(char *jobvl, char *jobvr, int *n, double *a, int *lda, double *b, int *ldb, double *alphar, double *alphai, double *beta, double *vl, int *ldvl, double *vr, int *ldvr, double *work, int *lwork, int *info);
void F_FUNC(dggevx,DGGEVX)(char *balanc, char *jobvl, char *jobvr, char *sense, int *n, double *a, int *lda, double *b, int *ldb, double *alphar, double *alphai, double *beta, double *vl, int *ldvl, double *vr, int *ldvr, int *ilo, int *ihi, double *lscale, double *rscale, double *abnrm, double *bbnrm, double *rconde, double *rcondv, double *work, int *lwork, int *iwork, int *bwork, int *info);
void F_FUNC(dggglm,DGGGLM)(int *n, int *m, int *p, double *a, int *lda, double *b, int *ldb, double *d, double *x, double *y, double *work, int *lwork, int *info);
void F_FUNC(dgghrd,DGGHRD)(char *compq, char *compz, int *n, int *ilo, int *ihi, double *a, int *lda, double *b, int *ldb, double *q, int *ldq, double *z, int *ldz, int *info);
void F_FUNC(dgglse,DGGLSE)(int *m, int *n, int *p, double *a, int *lda, double *b, int *ldb, double *c, double *d, double *x, double *work, int *lwork, int *info);
void F_FUNC(dggqrf,DGGQRF)(int *n, int *m, int *p, double *a, int *lda, double *taua, double *b, int *ldb, double *taub, double *work, int *lwork, int *info);
void F_FUNC(dggrqf,DGGRQF)(int *m, int *p, int *n, double *a, int *lda, double *taua, double *b, int *ldb, double *taub, double *work, int *lwork, int *info);
void F_FUNC(dgsvj0,DGSVJ0)(char *jobv, int *m, int *n, double *a, int *lda, double *d, double *sva, int *mv, double *v, int *ldv, double *eps, double *sfmin, double *tol, int *nsweep, double *work, int *lwork, int *info);
void F_FUNC(dgsvj1,DGSVJ1)(char *jobv, int *m, int *n, int *n1, double *a, int *lda, double *d, double *sva, int *mv, double *v, int *ldv, double *eps, double *sfmin, double *tol, int *nsweep, double *work, int *lwork, int *info);
void F_FUNC(dgtcon,DGTCON)(char *norm, int *n, double *dl, double *d, double *du, double *du2, int *ipiv, double *anorm, double *rcond, double *work, int *iwork, int *info);
void F_FUNC(dgtrfs,DGTRFS)(char *trans, int *n, int *nrhs, double *dl, double *d, double *du, double *dlf, double *df, double *duf, double *du2, int *ipiv, double *b, int *ldb, double *x, int *ldx, double *ferr, double *berr, double *work, int *iwork, int *info);
void F_FUNC(dgtsv,DGTSV)(int *n, int *nrhs, double *dl, double *d, double *du, double *b, int *ldb, int *info);
void F_FUNC(dgtsvx,DGTSVX)(char *fact, char *trans, int *n, int *nrhs, double *dl, double *d, double *du, double *dlf, double *df, double *duf, double *du2, int *ipiv, double *b, int *ldb, double *x, int *ldx, double *rcond, double *ferr, double *berr, double *work, int *iwork, int *info);
void F_FUNC(dgttrf,DGTTRF)(int *n, double *dl, double *d, double *du, double *du2, int *ipiv, int *info);
void F_FUNC(dgttrs,DGTTRS)(char *trans, int *n, int *nrhs, double *dl, double *d, double *du, double *du2, int *ipiv, double *b, int *ldb, int *info);
void F_FUNC(dgtts2,DGTTS2)(int *itrans, int *n, int *nrhs, double *dl, double *d, double *du, double *du2, int *ipiv, double *b, int *ldb);
void F_FUNC(dhgeqz,DHGEQZ)(char *job, char *compq, char *compz, int *n, int *ilo, int *ihi, double *h, int *ldh, double *t, int *ldt, double *alphar, double *alphai, double *beta, double *q, int *ldq, double *z, int *ldz, double *work, int *lwork, int *info);
void F_FUNC(dhsein,DHSEIN)(char *side, char *eigsrc, char *initv, int *select, int *n, double *h, int *ldh, double *wr, double *wi, double *vl, int *ldvl, double *vr, int *ldvr, int *mm, int *m, double *work, int *ifaill, int *ifailr, int *info);
void F_FUNC(dhseqr,DHSEQR)(char *job, char *compz, int *n, int *ilo, int *ihi, double *h, int *ldh, double *wr, double *wi, double *z, int *ldz, double *work, int *lwork, int *info);
void F_FUNC(dlabad,DLABAD)(double *small, double *large);
void F_FUNC(dlabrd,DLABRD)(int *m, int *n, int *nb, double *a, int *lda, double *d, double *e, double *tauq, double *taup, double *x, int *ldx, double *y, int *ldy);
void F_FUNC(dlacn2,DLACN2)(int *n, double *v, double *x, int *isgn, double *est, int *kase, int *isave);
void F_FUNC(dlacon,DLACON)(int *n, double *v, double *x, int *isgn, double *est, int *kase);
void F_FUNC(dlacpy,DLACPY)(char *uplo, int *m, int *n, double *a, int *lda, double *b, int *ldb);
void F_FUNC(dladiv,DLADIV)(double *a, double *b, double *c, double *d, double *p, double *q);
void F_FUNC(dlae2,DLAE2)(double *a, double *b, double *c, double *rt1, double *rt2);
void F_FUNC(dlaebz,DLAEBZ)(int *ijob, int *nitmax, int *n, int *mmax, int *minp, int *nbmin, double *abstol, double *reltol, double *pivmin, double *d, double *e, double *e2, int *nval, double *ab, double *c, int *mout, int *nab, double *work, int *iwork, int *info);
void F_FUNC(dlaed0,DLAED0)(int *icompq, int *qsiz, int *n, double *d, double *e, double *q, int *ldq, double *qstore, int *ldqs, double *work, int *iwork, int *info);
void F_FUNC(dlaed1,DLAED1)(int *n, double *d, double *q, int *ldq, int *indxq, double *rho, int *cutpnt, double *work, int *iwork, int *info);
void F_FUNC(dlaed2,DLAED2)(int *k, int *n, int *n1, double *d, double *q, int *ldq, int *indxq, double *rho, double *z, double *dlamda, double *w, double *q2, int *indx, int *indxc, int *indxp, int *coltyp, int *info);
void F_FUNC(dlaed3,DLAED3)(int *k, int *n, int *n1, double *d, double *q, int *ldq, double *rho, double *dlamda, double *q2, int *indx, int *ctot, double *w, double *s, int *info);
void F_FUNC(dlaed4,DLAED4)(int *n, int *i, double *d, double *z, double *delta, double *rho, double *dlam, int *info);
void F_FUNC(dlaed5,DLAED5)(int *i, double *d, double *z, double *delta, double *rho, double *dlam);
void F_FUNC(dlaed6,DLAED6)(int *kniter, int *orgati, double *rho, double *d, double *z, double *finit, double *tau, int *info);
void F_FUNC(dlaed7,DLAED7)(int *icompq, int *n, int *qsiz, int *tlvls, int *curlvl, int *curpbm, double *d, double *q, int *ldq, int *indxq, double *rho, int *cutpnt, double *qstore, int *qptr, int *prmptr, int *perm, int *givptr, int *givcol, double *givnum, double *work, int *iwork, int *info);
void F_FUNC(dlaed8,DLAED8)(int *icompq, int *k, int *n, int *qsiz, double *d, double *q, int *ldq, int *indxq, double *rho, int *cutpnt, double *z, double *dlamda, double *q2, int *ldq2, double *w, int *perm, int *givptr, int *givcol, double *givnum, int *indxp, int *indx, int *info);
void F_FUNC(dlaed9,DLAED9)(int *k, int *kstart, int *kstop, int *n, double *d, double *q, int *ldq, double *rho, double *dlamda, double *w, double *s, int *lds, int *info);
void F_FUNC(dlaeda,DLAEDA)(int *n, int *tlvls, int *curlvl, int *curpbm, int *prmptr, int *perm, int *givptr, int *givcol, double *givnum, double *q, int *qptr, double *z, double *ztemp, int *info);
void F_FUNC(dlaein,DLAEIN)(int *rightv, int *noinit, int *n, double *h, int *ldh, double *wr, double *wi, double *vr, double *vi, double *b, int *ldb, double *work, double *eps3, double *smlnum, double *bignum, int *info);
void F_FUNC(dlaev2,DLAEV2)(double *a, double *b, double *c, double *rt1, double *rt2, double *cs1, double *sn1);
void F_FUNC(dlaexc,DLAEXC)(int *wantq, int *n, double *t, int *ldt, double *q, int *ldq, int *j1, int *n1, int *n2, double *work, int *info);
void F_FUNC(dlag2,DLAG2)(double *a, int *lda, double *b, int *ldb, double *safmin, double *scale1, double *scale2, double *wr1, double *wr2, double *wi);
void F_FUNC(dlag2s,DLAG2S)(int *m, int *n, double *a, int *lda, float *sa, int *ldsa, int *info);
void F_FUNC(dlags2,DLAGS2)(int *upper, double *a1, double *a2, double *a3, double *b1, double *b2, double *b3, double *csu, double *snu, double *csv, double *snv, double *csq, double *snq);
void F_FUNC(dlagtf,DLAGTF)(int *n, double *a, double *lambda, double *b, double *c, double *tol, double *d, int *in, int *info);
void F_FUNC(dlagtm,DLAGTM)(char *trans, int *n, int *nrhs, double *alpha, double *dl, double *d, double *du, double *x, int *ldx, double *beta, double *b, int *ldb);
void F_FUNC(dlagts,DLAGTS)(int *job, int *n, double *a, double *b, double *c, double *d, int *in, double *y, double *tol, int *info);
void F_FUNC(dlagv2,DLAGV2)(double *a, int *lda, double *b, int *ldb, double *alphar, double *alphai, double *beta, double *csl, double *snl, double *csr, double *snr);
void F_FUNC(dlahqr,DLAHQR)(int *wantt, int *wantz, int *n, int *ilo, int *ihi, double *h, int *ldh, double *wr, double *wi, int *iloz, int *ihiz, double *z, int *ldz, int *info);
void F_FUNC(dlahr2,DLAHR2)(int *n, int *k, int *nb, double *a, int *lda, double *tau, double *t, int *ldt, double *y, int *ldy);
void F_FUNC(dlaic1,DLAIC1)(int *job, int *j, double *x, double *sest, double *w, double *gamma, double *sestpr, double *s, double *c);
void F_FUNC(dlaln2,DLALN2)(int *ltrans, int *na, int *nw, double *smin, double *ca, double *a, int *lda, double *d1, double *d2, double *b, int *ldb, double *wr, double *wi, double *x, int *ldx, double *scale, double *xnorm, int *info);
void F_FUNC(dlals0,DLALS0)(int *icompq, int *nl, int *nr, int *sqre, int *nrhs, double *b, int *ldb, double *bx, int *ldbx, int *perm, int *givptr, int *givcol, int *ldgcol, double *givnum, int *ldgnum, double *poles, double *difl, double *difr, double *z, int *k, double *c, double *s, double *work, int *info);
void F_FUNC(dlalsa,DLALSA)(int *icompq, int *smlsiz, int *n, int *nrhs, double *b, int *ldb, double *bx, int *ldbx, double *u, int *ldu, double *vt, int *k, double *difl, double *difr, double *z, double *poles, int *givptr, int *givcol, int *ldgcol, int *perm, double *givnum, double *c, double *s, double *work, int *iwork, int *info);
void F_FUNC(dlalsd,DLALSD)(char *uplo, int *smlsiz, int *n, int *nrhs, double *d, double *e, double *b, int *ldb, double *rcond, int *rank, double *work, int *iwork, int *info);
void F_FUNC(dlamrg,DLAMRG)(int *n1, int *n2, double *a, int *dtrd1, int *dtrd2, int *index_bn);
void F_FUNC(dlanv2,DLANV2)(double *a, double *b, double *c, double *d, double *rt1r, double *rt1i, double *rt2r, double *rt2i, double *cs, double *sn);
void F_FUNC(dlapll,DLAPLL)(int *n, double *x, int *incx, double *y, int *incy, double *ssmin);
void F_FUNC(dlapmr,DLAPMR)(int *forwrd, int *m, int *n, double *x, int *ldx, int *k);
void F_FUNC(dlapmt,DLAPMT)(int *forwrd, int *m, int *n, double *x, int *ldx, int *k);
void F_FUNC(dlaqgb,DLAQGB)(int *m, int *n, int *kl, int *ku, double *ab, int *ldab, double *r, double *c, double *rowcnd, double *colcnd, double *amax, char *equed);
void F_FUNC(dlaqge,DLAQGE)(int *m, int *n, double *a, int *lda, double *r, double *c, double *rowcnd, double *colcnd, double *amax, char *equed);
void F_FUNC(dlaqp2,DLAQP2)(int *m, int *n, int *offset, double *a, int *lda, int *jpvt, double *tau, double *vn1, double *vn2, double *work);
void F_FUNC(dlaqps,DLAQPS)(int *m, int *n, int *offset, int *nb, int *kb, double *a, int *lda, int *jpvt, double *tau, double *vn1, double *vn2, double *auxv, double *f, int *ldf);
void F_FUNC(dlaqr0,DLAQR0)(int *wantt, int *wantz, int *n, int *ilo, int *ihi, double *h, int *ldh, double *wr, double *wi, int *iloz, int *ihiz, double *z, int *ldz, double *work, int *lwork, int *info);
void F_FUNC(dlaqr1,DLAQR1)(int *n, double *h, int *ldh, double *sr1, double *si1, double *sr2, double *si2, double *v);
void F_FUNC(dlaqr2,DLAQR2)(int *wantt, int *wantz, int *n, int *ktop, int *kbot, int *nw, double *h, int *ldh, int *iloz, int *ihiz, double *z, int *ldz, int *ns, int *nd, double *sr, double *si, double *v, int *ldv, int *nh, double *t, int *ldt, int *nv, double *wv, int *ldwv, double *work, int *lwork);
void F_FUNC(dlaqr3,DLAQR3)(int *wantt, int *wantz, int *n, int *ktop, int *kbot, int *nw, double *h, int *ldh, int *iloz, int *ihiz, double *z, int *ldz, int *ns, int *nd, double *sr, double *si, double *v, int *ldv, int *nh, double *t, int *ldt, int *nv, double *wv, int *ldwv, double *work, int *lwork);
void F_FUNC(dlaqr4,DLAQR4)(int *wantt, int *wantz, int *n, int *ilo, int *ihi, double *h, int *ldh, double *wr, double *wi, int *iloz, int *ihiz, double *z, int *ldz, double *work, int *lwork, int *info);
void F_FUNC(dlaqr5,DLAQR5)(int *wantt, int *wantz, int *kacc22, int *n, int *ktop, int *kbot, int *nshfts, double *sr, double *si, double *h, int *ldh, int *iloz, int *ihiz, double *z, int *ldz, double *v, int *ldv, double *u, int *ldu, int *nv, double *wv, int *ldwv, int *nh, double *wh, int *ldwh);
void F_FUNC(dlaqsb,DLAQSB)(char *uplo, int *n, int *kd, double *ab, int *ldab, double *s, double *scond, double *amax, char *equed);
void F_FUNC(dlaqsp,DLAQSP)(char *uplo, int *n, double *ap, double *s, double *scond, double *amax, char *equed);
void F_FUNC(dlaqsy,DLAQSY)(char *uplo, int *n, double *a, int *lda, double *s, double *scond, double *amax, char *equed);
void F_FUNC(dlaqtr,DLAQTR)(int *ltran, int *lreal, int *n, double *t, int *ldt, double *b, double *w, double *scale, double *x, double *work, int *info);
void F_FUNC(dlar1v,DLAR1V)(int *n, int *b1, int *bn, double *lambda, double *d, double *l, double *ld, double *lld, double *pivmin, double *gaptol, double *z, int *wantnc, int *negcnt, double *ztz, double *mingma, int *r, int *isuppz, double *nrminv, double *resid, double *rqcorr, double *work);
void F_FUNC(dlar2v,DLAR2V)(int *n, double *x, double *y, double *z, int *incx, double *c, double *s, int *incc);
void F_FUNC(dlarf,DLARF)(char *side, int *m, int *n, double *v, int *incv, double *tau, double *c, int *ldc, double *work);
void F_FUNC(dlarfb,DLARFB)(char *side, char *trans, char *direct, char *storev, int *m, int *n, int *k, double *v, int *ldv, double *t, int *ldt, double *c, int *ldc, double *work, int *ldwork);
void F_FUNC(dlarfg,DLARFG)(int *n, double *alpha, double *x, int *incx, double *tau);
void F_FUNC(dlarfgp,DLARFGP)(int *n, double *alpha, double *x, int *incx, double *tau);
void F_FUNC(dlarft,DLARFT)(char *direct, char *storev, int *n, int *k, double *v, int *ldv, double *tau, double *t, int *ldt);
void F_FUNC(dlarfx,DLARFX)(char *side, int *m, int *n, double *v, double *tau, double *c, int *ldc, double *work);
void F_FUNC(dlargv,DLARGV)(int *n, double *x, int *incx, double *y, int *incy, double *c, int *incc);
void F_FUNC(dlarnv,DLARNV)(int *idist, int *iseed, int *n, double *x);
void F_FUNC(dlarra,DLARRA)(int *n, double *d, double *e, double *e2, double *spltol, double *tnrm, int *nsplit, int *isplit, int *info);
void F_FUNC(dlarrb,DLARRB)(int *n, double *d, double *lld, int *ifirst, int *ilast, double *rtol1, double *rtol2, int *offset, double *w, double *wgap, double *werr, double *work, int *iwork, double *pivmin, double *spdiam, int *twist, int *info);
void F_FUNC(dlarrc,DLARRC)(char *jobt, int *n, double *vl, double *vu, double *d, double *e, double *pivmin, int *eigcnt, int *lcnt, int *rcnt, int *info);
void F_FUNC(dlarrd,DLARRD)(char *range, char *order, int *n, double *vl, double *vu, int *il, int *iu, double *gers, double *reltol, double *d, double *e, double *e2, double *pivmin, int *nsplit, int *isplit, int *m, double *w, double *werr, double *wl, double *wu, int *iblock, int *indexw, double *work, int *iwork, int *info);
void F_FUNC(dlarre,DLARRE)(char *range, int *n, double *vl, double *vu, int *il, int *iu, double *d, double *e, double *e2, double *rtol1, double *rtol2, double *spltol, int *nsplit, int *isplit, int *m, double *w, double *werr, double *wgap, int *iblock, int *indexw, double *gers, double *pivmin, double *work, int *iwork, int *info);
void F_FUNC(dlarrf,DLARRF)(int *n, double *d, double *l, double *ld, int *clstrt, int *clend, double *w, double *wgap, double *werr, double *spdiam, double *clgapl, double *clgapr, double *pivmin, double *sigma, double *dplus, double *lplus, double *work, int *info);
void F_FUNC(dlarrj,DLARRJ)(int *n, double *d, double *e2, int *ifirst, int *ilast, double *rtol, int *offset, double *w, double *werr, double *work, int *iwork, double *pivmin, double *spdiam, int *info);
void F_FUNC(dlarrk,DLARRK)(int *n, int *iw, double *gl, double *gu, double *d, double *e2, double *pivmin, double *reltol, double *w, double *werr, int *info);
void F_FUNC(dlarrr,DLARRR)(int *n, double *d, double *e, int *info);
void F_FUNC(dlarrv,DLARRV)(int *n, double *vl, double *vu, double *d, double *l, double *pivmin, int *isplit, int *m, int *dol, int *dou, double *minrgp, double *rtol1, double *rtol2, double *w, double *werr, double *wgap, int *iblock, int *indexw, double *gers, double *z, int *ldz, int *isuppz, double *work, int *iwork, int *info);
void F_FUNC(dlartg,DLARTG)(double *f, double *g, double *cs, double *sn, double *r);
void F_FUNC(dlartgp,DLARTGP)(double *f, double *g, double *cs, double *sn, double *r);
void F_FUNC(dlartgs,DLARTGS)(double *x, double *y, double *sigma, double *cs, double *sn);
void F_FUNC(dlartv,DLARTV)(int *n, double *x, int *incx, double *y, int *incy, double *c, double *s, int *incc);
void F_FUNC(dlaruv,DLARUV)(int *iseed, int *n, double *x);
void F_FUNC(dlarz,DLARZ)(char *side, int *m, int *n, int *l, double *v, int *incv, double *tau, double *c, int *ldc, double *work);
void F_FUNC(dlarzb,DLARZB)(char *side, char *trans, char *direct, char *storev, int *m, int *n, int *k, int *l, double *v, int *ldv, double *t, int *ldt, double *c, int *ldc, double *work, int *ldwork);
void F_FUNC(dlarzt,DLARZT)(char *direct, char *storev, int *n, int *k, double *v, int *ldv, double *tau, double *t, int *ldt);
void F_FUNC(dlas2,DLAS2)(double *f, double *g, double *h, double *ssmin, double *ssmax);
void F_FUNC(dlascl,DLASCL)(char *type_bn, int *kl, int *ku, double *cfrom, double *cto, int *m, int *n, double *a, int *lda, int *info);
void F_FUNC(dlasd0,DLASD0)(int *n, int *sqre, double *d, double *e, double *u, int *ldu, double *vt, int *ldvt, int *smlsiz, int *iwork, double *work, int *info);
void F_FUNC(dlasd1,DLASD1)(int *nl, int *nr, int *sqre, double *d, double *alpha, double *beta, double *u, int *ldu, double *vt, int *ldvt, int *idxq, int *iwork, double *work, int *info);
void F_FUNC(dlasd2,DLASD2)(int *nl, int *nr, int *sqre, int *k, double *d, double *z, double *alpha, double *beta, double *u, int *ldu, double *vt, int *ldvt, double *dsigma, double *u2, int *ldu2, double *vt2, int *ldvt2, int *idxp, int *idx, int *idxc, int *idxq, int *coltyp, int *info);
void F_FUNC(dlasd3,DLASD3)(int *nl, int *nr, int *sqre, int *k, double *d, double *q, int *ldq, double *dsigma, double *u, int *ldu, double *u2, int *ldu2, double *vt, int *ldvt, double *vt2, int *ldvt2, int *idxc, int *ctot, double *z, int *info);
void F_FUNC(dlasd4,DLASD4)(int *n, int *i, double *d, double *z, double *delta, double *rho, double *sigma, double *work, int *info);
void F_FUNC(dlasd5,DLASD5)(int *i, double *d, double *z, double *delta, double *rho, double *dsigma, double *work);
void F_FUNC(dlasd6,DLASD6)(int *icompq, int *nl, int *nr, int *sqre, double *d, double *vf, double *vl, double *alpha, double *beta, int *idxq, int *perm, int *givptr, int *givcol, int *ldgcol, double *givnum, int *ldgnum, double *poles, double *difl, double *difr, double *z, int *k, double *c, double *s, double *work, int *iwork, int *info);
void F_FUNC(dlasd7,DLASD7)(int *icompq, int *nl, int *nr, int *sqre, int *k, double *d, double *z, double *zw, double *vf, double *vfw, double *vl, double *vlw, double *alpha, double *beta, double *dsigma, int *idx, int *idxp, int *idxq, int *perm, int *givptr, int *givcol, int *ldgcol, double *givnum, int *ldgnum, double *c, double *s, int *info);
void F_FUNC(dlasd8,DLASD8)(int *icompq, int *k, double *d, double *z, double *vf, double *vl, double *difl, double *difr, int *lddifr, double *dsigma, double *work, int *info);
void F_FUNC(dlasda,DLASDA)(int *icompq, int *smlsiz, int *n, int *sqre, double *d, double *e, double *u, int *ldu, double *vt, int *k, double *difl, double *difr, double *z, double *poles, int *givptr, int *givcol, int *ldgcol, int *perm, double *givnum, double *c, double *s, double *work, int *iwork, int *info);
void F_FUNC(dlasdq,DLASDQ)(char *uplo, int *sqre, int *n, int *ncvt, int *nru, int *ncc, double *d, double *e, double *vt, int *ldvt, double *u, int *ldu, double *c, int *ldc, double *work, int *info);
void F_FUNC(dlasdt,DLASDT)(int *n, int *lvl, int *nd, int *inode, int *ndiml, int *ndimr, int *msub);
void F_FUNC(dlaset,DLASET)(char *uplo, int *m, int *n, double *alpha, double *beta, double *a, int *lda);
void F_FUNC(dlasq1,DLASQ1)(int *n, double *d, double *e, double *work, int *info);
void F_FUNC(dlasq2,DLASQ2)(int *n, double *z, int *info);
void F_FUNC(dlasq3,DLASQ3)(int *i0, int *n0, double *z, int *pp, double *dmin, double *sigma, double *desig, double *qmax, int *nfail, int *iter, int *ndiv, int *ieee, int *ttype, double *dmin1, double *dmin2, double *dn, double *dn1, double *dn2, double *g, double *tau);
void F_FUNC(dlasq4,DLASQ4)(int *i0, int *n0, double *z, int *pp, int *n0in, double *dmin, double *dmin1, double *dmin2, double *dn, double *dn1, double *dn2, double *tau, int *ttype, double *g);
void F_FUNC(dlasq6,DLASQ6)(int *i0, int *n0, double *z, int *pp, double *dmin, double *dmin1, double *dmin2, double *dn, double *dnm1, double *dnm2);
void F_FUNC(dlasr,DLASR)(char *side, char *pivot, char *direct, int *m, int *n, double *c, double *s, double *a, int *lda);
void F_FUNC(dlasrt,DLASRT)(char *id, int *n, double *d, int *info);
void F_FUNC(dlassq,DLASSQ)(int *n, double *x, int *incx, double *scale, double *sumsq);
void F_FUNC(dlasv2,DLASV2)(double *f, double *g, double *h, double *ssmin, double *ssmax, double *snr, double *csr, double *snl, double *csl);
void F_FUNC(dlaswp,DLASWP)(int *n, double *a, int *lda, int *k1, int *k2, int *ipiv, int *incx);
void F_FUNC(dlasy2,DLASY2)(int *ltranl, int *ltranr, int *isgn, int *n1, int *n2, double *tl, int *ldtl, double *tr, int *ldtr, double *b, int *ldb, double *scale, double *x, int *ldx, double *xnorm, int *info);
void F_FUNC(dlasyf,DLASYF)(char *uplo, int *n, int *nb, int *kb, double *a, int *lda, int *ipiv, double *w, int *ldw, int *info);
void F_FUNC(dlat2s,DLAT2S)(char *uplo, int *n, double *a, int *lda, float *sa, int *ldsa, int *info);
void F_FUNC(dlatbs,DLATBS)(char *uplo, char *trans, char *diag, char *normin, int *n, int *kd, double *ab, int *ldab, double *x, double *scale, double *cnorm, int *info);
void F_FUNC(dlatdf,DLATDF)(int *ijob, int *n, double *z, int *ldz, double *rhs, double *rdsum, double *rdscal, int *ipiv, int *jpiv);
void F_FUNC(dlatps,DLATPS)(char *uplo, char *trans, char *diag, char *normin, int *n, double *ap, double *x, double *scale, double *cnorm, int *info);
void F_FUNC(dlatrd,DLATRD)(char *uplo, int *n, int *nb, double *a, int *lda, double *e, double *tau, double *w, int *ldw);
void F_FUNC(dlatrs,DLATRS)(char *uplo, char *trans, char *diag, char *normin, int *n, double *a, int *lda, double *x, double *scale, double *cnorm, int *info);
void F_FUNC(dlatrz,DLATRZ)(int *m, int *n, int *l, double *a, int *lda, double *tau, double *work);
void F_FUNC(dlauu2,DLAUU2)(char *uplo, int *n, double *a, int *lda, int *info);
void F_FUNC(dlauum,DLAUUM)(char *uplo, int *n, double *a, int *lda, int *info);
void F_FUNC(dopgtr,DOPGTR)(char *uplo, int *n, double *ap, double *tau, double *q, int *ldq, double *work, int *info);
void F_FUNC(dopmtr,DOPMTR)(char *side, char *uplo, char *trans, int *m, int *n, double *ap, double *tau, double *c, int *ldc, double *work, int *info);
void F_FUNC(dorbdb,DORBDB)(char *trans, char *signs, int *m, int *p, int *q, double *x11, int *ldx11, double *x12, int *ldx12, double *x21, int *ldx21, double *x22, int *ldx22, double *theta, double *phi, double *taup1, double *taup2, double *tauq1, double *tauq2, double *work, int *lwork, int *info);
void F_FUNC(dorcsd,DORCSD)(char *jobu1, char *jobu2, char *jobv1t, char *jobv2t, char *trans, char *signs, int *m, int *p, int *q, double *x11, int *ldx11, double *x12, int *ldx12, double *x21, int *ldx21, double *x22, int *ldx22, double *theta, double *u1, int *ldu1, double *u2, int *ldu2, double *v1t, int *ldv1t, double *v2t, int *ldv2t, double *work, int *lwork, int *iwork, int *info);
void F_FUNC(dorg2l,DORG2L)(int *m, int *n, int *k, double *a, int *lda, double *tau, double *work, int *info);
void F_FUNC(dorg2r,DORG2R)(int *m, int *n, int *k, double *a, int *lda, double *tau, double *work, int *info);
void F_FUNC(dorgbr,DORGBR)(char *vect, int *m, int *n, int *k, double *a, int *lda, double *tau, double *work, int *lwork, int *info);
void F_FUNC(dorghr,DORGHR)(int *n, int *ilo, int *ihi, double *a, int *lda, double *tau, double *work, int *lwork, int *info);
void F_FUNC(dorgl2,DORGL2)(int *m, int *n, int *k, double *a, int *lda, double *tau, double *work, int *info);
void F_FUNC(dorglq,DORGLQ)(int *m, int *n, int *k, double *a, int *lda, double *tau, double *work, int *lwork, int *info);
void F_FUNC(dorgql,DORGQL)(int *m, int *n, int *k, double *a, int *lda, double *tau, double *work, int *lwork, int *info);
void F_FUNC(dorgqr,DORGQR)(int *m, int *n, int *k, double *a, int *lda, double *tau, double *work, int *lwork, int *info);
void F_FUNC(dorgr2,DORGR2)(int *m, int *n, int *k, double *a, int *lda, double *tau, double *work, int *info);
void F_FUNC(dorgrq,DORGRQ)(int *m, int *n, int *k, double *a, int *lda, double *tau, double *work, int *lwork, int *info);
void F_FUNC(dorgtr,DORGTR)(char *uplo, int *n, double *a, int *lda, double *tau, double *work, int *lwork, int *info);
void F_FUNC(dorm2l,DORM2L)(char *side, char *trans, int *m, int *n, int *k, double *a, int *lda, double *tau, double *c, int *ldc, double *work, int *info);
void F_FUNC(dorm2r,DORM2R)(char *side, char *trans, int *m, int *n, int *k, double *a, int *lda, double *tau, double *c, int *ldc, double *work, int *info);
void F_FUNC(dormbr,DORMBR)(char *vect, char *side, char *trans, int *m, int *n, int *k, double *a, int *lda, double *tau, double *c, int *ldc, double *work, int *lwork, int *info);
void F_FUNC(dormhr,DORMHR)(char *side, char *trans, int *m, int *n, int *ilo, int *ihi, double *a, int *lda, double *tau, double *c, int *ldc, double *work, int *lwork, int *info);
void F_FUNC(dorml2,DORML2)(char *side, char *trans, int *m, int *n, int *k, double *a, int *lda, double *tau, double *c, int *ldc, double *work, int *info);
void F_FUNC(dormlq,DORMLQ)(char *side, char *trans, int *m, int *n, int *k, double *a, int *lda, double *tau, double *c, int *ldc, double *work, int *lwork, int *info);
void F_FUNC(dormql,DORMQL)(char *side, char *trans, int *m, int *n, int *k, double *a, int *lda, double *tau, double *c, int *ldc, double *work, int *lwork, int *info);
void F_FUNC(dormqr,DORMQR)(char *side, char *trans, int *m, int *n, int *k, double *a, int *lda, double *tau, double *c, int *ldc, double *work, int *lwork, int *info);
void F_FUNC(dormr2,DORMR2)(char *side, char *trans, int *m, int *n, int *k, double *a, int *lda, double *tau, double *c, int *ldc, double *work, int *info);
void F_FUNC(dormr3,DORMR3)(char *side, char *trans, int *m, int *n, int *k, int *l, double *a, int *lda, double *tau, double *c, int *ldc, double *work, int *info);
void F_FUNC(dormrq,DORMRQ)(char *side, char *trans, int *m, int *n, int *k, double *a, int *lda, double *tau, double *c, int *ldc, double *work, int *lwork, int *info);
void F_FUNC(dormrz,DORMRZ)(char *side, char *trans, int *m, int *n, int *k, int *l, double *a, int *lda, double *tau, double *c, int *ldc, double *work, int *lwork, int *info);
void F_FUNC(dormtr,DORMTR)(char *side, char *uplo, char *trans, int *m, int *n, double *a, int *lda, double *tau, double *c, int *ldc, double *work, int *lwork, int *info);
void F_FUNC(dpbcon,DPBCON)(char *uplo, int *n, int *kd, double *ab, int *ldab, double *anorm, double *rcond, double *work, int *iwork, int *info);
void F_FUNC(dpbequ,DPBEQU)(char *uplo, int *n, int *kd, double *ab, int *ldab, double *s, double *scond, double *amax, int *info);
void F_FUNC(dpbrfs,DPBRFS)(char *uplo, int *n, int *kd, int *nrhs, double *ab, int *ldab, double *afb, int *ldafb, double *b, int *ldb, double *x, int *ldx, double *ferr, double *berr, double *work, int *iwork, int *info);
void F_FUNC(dpbstf,DPBSTF)(char *uplo, int *n, int *kd, double *ab, int *ldab, int *info);
void F_FUNC(dpbsv,DPBSV)(char *uplo, int *n, int *kd, int *nrhs, double *ab, int *ldab, double *b, int *ldb, int *info);
void F_FUNC(dpbsvx,DPBSVX)(char *fact, char *uplo, int *n, int *kd, int *nrhs, double *ab, int *ldab, double *afb, int *ldafb, char *equed, double *s, double *b, int *ldb, double *x, int *ldx, double *rcond, double *ferr, double *berr, double *work, int *iwork, int *info);
void F_FUNC(dpbtf2,DPBTF2)(char *uplo, int *n, int *kd, double *ab, int *ldab, int *info);
void F_FUNC(dpbtrf,DPBTRF)(char *uplo, int *n, int *kd, double *ab, int *ldab, int *info);
void F_FUNC(dpbtrs,DPBTRS)(char *uplo, int *n, int *kd, int *nrhs, double *ab, int *ldab, double *b, int *ldb, int *info);
void F_FUNC(dpftrf,DPFTRF)(char *transr, char *uplo, int *n, double *a, int *info);
void F_FUNC(dpftri,DPFTRI)(char *transr, char *uplo, int *n, double *a, int *info);
void F_FUNC(dpftrs,DPFTRS)(char *transr, char *uplo, int *n, int *nrhs, double *a, double *b, int *ldb, int *info);
void F_FUNC(dpocon,DPOCON)(char *uplo, int *n, double *a, int *lda, double *anorm, double *rcond, double *work, int *iwork, int *info);
void F_FUNC(dpoequ,DPOEQU)(int *n, double *a, int *lda, double *s, double *scond, double *amax, int *info);
void F_FUNC(dpoequb,DPOEQUB)(int *n, double *a, int *lda, double *s, double *scond, double *amax, int *info);
void F_FUNC(dporfs,DPORFS)(char *uplo, int *n, int *nrhs, double *a, int *lda, double *af, int *ldaf, double *b, int *ldb, double *x, int *ldx, double *ferr, double *berr, double *work, int *iwork, int *info);
void F_FUNC(dposv,DPOSV)(char *uplo, int *n, int *nrhs, double *a, int *lda, double *b, int *ldb, int *info);
void F_FUNC(dposvx,DPOSVX)(char *fact, char *uplo, int *n, int *nrhs, double *a, int *lda, double *af, int *ldaf, char *equed, double *s, double *b, int *ldb, double *x, int *ldx, double *rcond, double *ferr, double *berr, double *work, int *iwork, int *info);
void F_FUNC(dpotf2,DPOTF2)(char *uplo, int *n, double *a, int *lda, int *info);
void F_FUNC(dpotrf,DPOTRF)(char *uplo, int *n, double *a, int *lda, int *info);
void F_FUNC(dpotri,DPOTRI)(char *uplo, int *n, double *a, int *lda, int *info);
void F_FUNC(dpotrs,DPOTRS)(char *uplo, int *n, int *nrhs, double *a, int *lda, double *b, int *ldb, int *info);
void F_FUNC(dppcon,DPPCON)(char *uplo, int *n, double *ap, double *anorm, double *rcond, double *work, int *iwork, int *info);
void F_FUNC(dppequ,DPPEQU)(char *uplo, int *n, double *ap, double *s, double *scond, double *amax, int *info);
void F_FUNC(dpprfs,DPPRFS)(char *uplo, int *n, int *nrhs, double *ap, double *afp, double *b, int *ldb, double *x, int *ldx, double *ferr, double *berr, double *work, int *iwork, int *info);
void F_FUNC(dppsv,DPPSV)(char *uplo, int *n, int *nrhs, double *ap, double *b, int *ldb, int *info);
void F_FUNC(dppsvx,DPPSVX)(char *fact, char *uplo, int *n, int *nrhs, double *ap, double *afp, char *equed, double *s, double *b, int *ldb, double *x, int *ldx, double *rcond, double *ferr, double *berr, double *work, int *iwork, int *info);
void F_FUNC(dpptrf,DPPTRF)(char *uplo, int *n, double *ap, int *info);
void F_FUNC(dpptri,DPPTRI)(char *uplo, int *n, double *ap, int *info);
void F_FUNC(dpptrs,DPPTRS)(char *uplo, int *n, int *nrhs, double *ap, double *b, int *ldb, int *info);
void F_FUNC(dpstf2,DPSTF2)(char *uplo, int *n, double *a, int *lda, int *piv, int *rank, double *tol, double *work, int *info);
void F_FUNC(dpstrf,DPSTRF)(char *uplo, int *n, double *a, int *lda, int *piv, int *rank, double *tol, double *work, int *info);
void F_FUNC(dptcon,DPTCON)(int *n, double *d, double *e, double *anorm, double *rcond, double *work, int *info);
void F_FUNC(dpteqr,DPTEQR)(char *compz, int *n, double *d, double *e, double *z, int *ldz, double *work, int *info);
void F_FUNC(dptrfs,DPTRFS)(int *n, int *nrhs, double *d, double *e, double *df, double *ef, double *b, int *ldb, double *x, int *ldx, double *ferr, double *berr, double *work, int *info);
void F_FUNC(dptsv,DPTSV)(int *n, int *nrhs, double *d, double *e, double *b, int *ldb, int *info);
void F_FUNC(dptsvx,DPTSVX)(char *fact, int *n, int *nrhs, double *d, double *e, double *df, double *ef, double *b, int *ldb, double *x, int *ldx, double *rcond, double *ferr, double *berr, double *work, int *info);
void F_FUNC(dpttrf,DPTTRF)(int *n, double *d, double *e, int *info);
void F_FUNC(dpttrs,DPTTRS)(int *n, int *nrhs, double *d, double *e, double *b, int *ldb, int *info);
void F_FUNC(dptts2,DPTTS2)(int *n, int *nrhs, double *d, double *e, double *b, int *ldb);
void F_FUNC(drscl,DRSCL)(int *n, double *sa, double *sx, int *incx);
void F_FUNC(dsbev,DSBEV)(char *jobz, char *uplo, int *n, int *kd, double *ab, int *ldab, double *w, double *z, int *ldz, double *work, int *info);
void F_FUNC(dsbevd,DSBEVD)(char *jobz, char *uplo, int *n, int *kd, double *ab, int *ldab, double *w, double *z, int *ldz, double *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(dsbevx,DSBEVX)(char *jobz, char *range, char *uplo, int *n, int *kd, double *ab, int *ldab, double *q, int *ldq, double *vl, double *vu, int *il, int *iu, double *abstol, int *m, double *w, double *z, int *ldz, double *work, int *iwork, int *ifail, int *info);
void F_FUNC(dsbgst,DSBGST)(char *vect, char *uplo, int *n, int *ka, int *kb, double *ab, int *ldab, double *bb, int *ldbb, double *x, int *ldx, double *work, int *info);
void F_FUNC(dsbgv,DSBGV)(char *jobz, char *uplo, int *n, int *ka, int *kb, double *ab, int *ldab, double *bb, int *ldbb, double *w, double *z, int *ldz, double *work, int *info);
void F_FUNC(dsbgvd,DSBGVD)(char *jobz, char *uplo, int *n, int *ka, int *kb, double *ab, int *ldab, double *bb, int *ldbb, double *w, double *z, int *ldz, double *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(dsbgvx,DSBGVX)(char *jobz, char *range, char *uplo, int *n, int *ka, int *kb, double *ab, int *ldab, double *bb, int *ldbb, double *q, int *ldq, double *vl, double *vu, int *il, int *iu, double *abstol, int *m, double *w, double *z, int *ldz, double *work, int *iwork, int *ifail, int *info);
void F_FUNC(dsbtrd,DSBTRD)(char *vect, char *uplo, int *n, int *kd, double *ab, int *ldab, double *d, double *e, double *q, int *ldq, double *work, int *info);
void F_FUNC(dsfrk,DSFRK)(char *transr, char *uplo, char *trans, int *n, int *k, double *alpha, double *a, int *lda, double *beta, double *c);
void F_FUNC(dsgesv,DSGESV)(int *n, int *nrhs, double *a, int *lda, int *ipiv, double *b, int *ldb, double *x, int *ldx, double *work, float *swork, int *iter, int *info);
void F_FUNC(dspcon,DSPCON)(char *uplo, int *n, double *ap, int *ipiv, double *anorm, double *rcond, double *work, int *iwork, int *info);
void F_FUNC(dspev,DSPEV)(char *jobz, char *uplo, int *n, double *ap, double *w, double *z, int *ldz, double *work, int *info);
void F_FUNC(dspevd,DSPEVD)(char *jobz, char *uplo, int *n, double *ap, double *w, double *z, int *ldz, double *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(dspevx,DSPEVX)(char *jobz, char *range, char *uplo, int *n, double *ap, double *vl, double *vu, int *il, int *iu, double *abstol, int *m, double *w, double *z, int *ldz, double *work, int *iwork, int *ifail, int *info);
void F_FUNC(dspgst,DSPGST)(int *itype, char *uplo, int *n, double *ap, double *bp, int *info);
void F_FUNC(dspgv,DSPGV)(int *itype, char *jobz, char *uplo, int *n, double *ap, double *bp, double *w, double *z, int *ldz, double *work, int *info);
void F_FUNC(dspgvd,DSPGVD)(int *itype, char *jobz, char *uplo, int *n, double *ap, double *bp, double *w, double *z, int *ldz, double *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(dspgvx,DSPGVX)(int *itype, char *jobz, char *range, char *uplo, int *n, double *ap, double *bp, double *vl, double *vu, int *il, int *iu, double *abstol, int *m, double *w, double *z, int *ldz, double *work, int *iwork, int *ifail, int *info);
void F_FUNC(dsposv,DSPOSV)(char *uplo, int *n, int *nrhs, double *a, int *lda, double *b, int *ldb, double *x, int *ldx, double *work, float *swork, int *iter, int *info);
void F_FUNC(dsprfs,DSPRFS)(char *uplo, int *n, int *nrhs, double *ap, double *afp, int *ipiv, double *b, int *ldb, double *x, int *ldx, double *ferr, double *berr, double *work, int *iwork, int *info);
void F_FUNC(dspsv,DSPSV)(char *uplo, int *n, int *nrhs, double *ap, int *ipiv, double *b, int *ldb, int *info);
void F_FUNC(dspsvx,DSPSVX)(char *fact, char *uplo, int *n, int *nrhs, double *ap, double *afp, int *ipiv, double *b, int *ldb, double *x, int *ldx, double *rcond, double *ferr, double *berr, double *work, int *iwork, int *info);
void F_FUNC(dsptrd,DSPTRD)(char *uplo, int *n, double *ap, double *d, double *e, double *tau, int *info);
void F_FUNC(dsptrf,DSPTRF)(char *uplo, int *n, double *ap, int *ipiv, int *info);
void F_FUNC(dsptri,DSPTRI)(char *uplo, int *n, double *ap, int *ipiv, double *work, int *info);
void F_FUNC(dsptrs,DSPTRS)(char *uplo, int *n, int *nrhs, double *ap, int *ipiv, double *b, int *ldb, int *info);
void F_FUNC(dstebz,DSTEBZ)(char *range, char *order, int *n, double *vl, double *vu, int *il, int *iu, double *abstol, double *d, double *e, int *m, int *nsplit, double *w, int *iblock, int *isplit, double *work, int *iwork, int *info);
void F_FUNC(dstedc,DSTEDC)(char *compz, int *n, double *d, double *e, double *z, int *ldz, double *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(dstegr,DSTEGR)(char *jobz, char *range, int *n, double *d, double *e, double *vl, double *vu, int *il, int *iu, double *abstol, int *m, double *w, double *z, int *ldz, int *isuppz, double *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(dstein,DSTEIN)(int *n, double *d, double *e, int *m, double *w, int *iblock, int *isplit, double *z, int *ldz, double *work, int *iwork, int *ifail, int *info);
void F_FUNC(dstemr,DSTEMR)(char *jobz, char *range, int *n, double *d, double *e, double *vl, double *vu, int *il, int *iu, int *m, double *w, double *z, int *ldz, int *nzc, int *isuppz, int *tryrac, double *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(dsteqr,DSTEQR)(char *compz, int *n, double *d, double *e, double *z, int *ldz, double *work, int *info);
void F_FUNC(dsterf,DSTERF)(int *n, double *d, double *e, int *info);
void F_FUNC(dstev,DSTEV)(char *jobz, int *n, double *d, double *e, double *z, int *ldz, double *work, int *info);
void F_FUNC(dstevd,DSTEVD)(char *jobz, int *n, double *d, double *e, double *z, int *ldz, double *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(dstevr,DSTEVR)(char *jobz, char *range, int *n, double *d, double *e, double *vl, double *vu, int *il, int *iu, double *abstol, int *m, double *w, double *z, int *ldz, int *isuppz, double *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(dstevx,DSTEVX)(char *jobz, char *range, int *n, double *d, double *e, double *vl, double *vu, int *il, int *iu, double *abstol, int *m, double *w, double *z, int *ldz, double *work, int *iwork, int *ifail, int *info);
void F_FUNC(dsycon,DSYCON)(char *uplo, int *n, double *a, int *lda, int *ipiv, double *anorm, double *rcond, double *work, int *iwork, int *info);
void F_FUNC(dsyconv,DSYCONV)(char *uplo, char *way, int *n, double *a, int *lda, int *ipiv, double *work, int *info);
void F_FUNC(dsyequb,DSYEQUB)(char *uplo, int *n, double *a, int *lda, double *s, double *scond, double *amax, double *work, int *info);
void F_FUNC(dsyev,DSYEV)(char *jobz, char *uplo, int *n, double *a, int *lda, double *w, double *work, int *lwork, int *info);
void F_FUNC(dsyevd,DSYEVD)(char *jobz, char *uplo, int *n, double *a, int *lda, double *w, double *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(dsyevr,DSYEVR)(char *jobz, char *range, char *uplo, int *n, double *a, int *lda, double *vl, double *vu, int *il, int *iu, double *abstol, int *m, double *w, double *z, int *ldz, int *isuppz, double *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(dsyevx,DSYEVX)(char *jobz, char *range, char *uplo, int *n, double *a, int *lda, double *vl, double *vu, int *il, int *iu, double *abstol, int *m, double *w, double *z, int *ldz, double *work, int *lwork, int *iwork, int *ifail, int *info);
void F_FUNC(dsygs2,DSYGS2)(int *itype, char *uplo, int *n, double *a, int *lda, double *b, int *ldb, int *info);
void F_FUNC(dsygst,DSYGST)(int *itype, char *uplo, int *n, double *a, int *lda, double *b, int *ldb, int *info);
void F_FUNC(dsygv,DSYGV)(int *itype, char *jobz, char *uplo, int *n, double *a, int *lda, double *b, int *ldb, double *w, double *work, int *lwork, int *info);
void F_FUNC(dsygvd,DSYGVD)(int *itype, char *jobz, char *uplo, int *n, double *a, int *lda, double *b, int *ldb, double *w, double *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(dsygvx,DSYGVX)(int *itype, char *jobz, char *range, char *uplo, int *n, double *a, int *lda, double *b, int *ldb, double *vl, double *vu, int *il, int *iu, double *abstol, int *m, double *w, double *z, int *ldz, double *work, int *lwork, int *iwork, int *ifail, int *info);
void F_FUNC(dsyrfs,DSYRFS)(char *uplo, int *n, int *nrhs, double *a, int *lda, double *af, int *ldaf, int *ipiv, double *b, int *ldb, double *x, int *ldx, double *ferr, double *berr, double *work, int *iwork, int *info);
void F_FUNC(dsysv,DSYSV)(char *uplo, int *n, int *nrhs, double *a, int *lda, int *ipiv, double *b, int *ldb, double *work, int *lwork, int *info);
void F_FUNC(dsysvx,DSYSVX)(char *fact, char *uplo, int *n, int *nrhs, double *a, int *lda, double *af, int *ldaf, int *ipiv, double *b, int *ldb, double *x, int *ldx, double *rcond, double *ferr, double *berr, double *work, int *lwork, int *iwork, int *info);
void F_FUNC(dsyswapr,DSYSWAPR)(char *uplo, int *n, double *a, int *lda, int *i1, int *i2);
void F_FUNC(dsytd2,DSYTD2)(char *uplo, int *n, double *a, int *lda, double *d, double *e, double *tau, int *info);
void F_FUNC(dsytf2,DSYTF2)(char *uplo, int *n, double *a, int *lda, int *ipiv, int *info);
void F_FUNC(dsytrd,DSYTRD)(char *uplo, int *n, double *a, int *lda, double *d, double *e, double *tau, double *work, int *lwork, int *info);
void F_FUNC(dsytrf,DSYTRF)(char *uplo, int *n, double *a, int *lda, int *ipiv, double *work, int *lwork, int *info);
void F_FUNC(dsytri,DSYTRI)(char *uplo, int *n, double *a, int *lda, int *ipiv, double *work, int *info);
void F_FUNC(dsytri2,DSYTRI2)(char *uplo, int *n, double *a, int *lda, int *ipiv, double *work, int *lwork, int *info);
void F_FUNC(dsytri2x,DSYTRI2X)(char *uplo, int *n, double *a, int *lda, int *ipiv, double *work, int *nb, int *info);
void F_FUNC(dsytrs,DSYTRS)(char *uplo, int *n, int *nrhs, double *a, int *lda, int *ipiv, double *b, int *ldb, int *info);
void F_FUNC(dsytrs2,DSYTRS2)(char *uplo, int *n, int *nrhs, double *a, int *lda, int *ipiv, double *b, int *ldb, double *work, int *info);
void F_FUNC(dtbcon,DTBCON)(char *norm, char *uplo, char *diag, int *n, int *kd, double *ab, int *ldab, double *rcond, double *work, int *iwork, int *info);
void F_FUNC(dtbrfs,DTBRFS)(char *uplo, char *trans, char *diag, int *n, int *kd, int *nrhs, double *ab, int *ldab, double *b, int *ldb, double *x, int *ldx, double *ferr, double *berr, double *work, int *iwork, int *info);
void F_FUNC(dtbtrs,DTBTRS)(char *uplo, char *trans, char *diag, int *n, int *kd, int *nrhs, double *ab, int *ldab, double *b, int *ldb, int *info);
void F_FUNC(dtfsm,DTFSM)(char *transr, char *side, char *uplo, char *trans, char *diag, int *m, int *n, double *alpha, double *a, double *b, int *ldb);
void F_FUNC(dtftri,DTFTRI)(char *transr, char *uplo, char *diag, int *n, double *a, int *info);
void F_FUNC(dtfttp,DTFTTP)(char *transr, char *uplo, int *n, double *arf, double *ap, int *info);
void F_FUNC(dtfttr,DTFTTR)(char *transr, char *uplo, int *n, double *arf, double *a, int *lda, int *info);
void F_FUNC(dtgevc,DTGEVC)(char *side, char *howmny, int *select, int *n, double *s, int *lds, double *p, int *ldp, double *vl, int *ldvl, double *vr, int *ldvr, int *mm, int *m, double *work, int *info);
void F_FUNC(dtgex2,DTGEX2)(int *wantq, int *wantz, int *n, double *a, int *lda, double *b, int *ldb, double *q, int *ldq, double *z, int *ldz, int *j1, int *n1, int *n2, double *work, int *lwork, int *info);
void F_FUNC(dtgexc,DTGEXC)(int *wantq, int *wantz, int *n, double *a, int *lda, double *b, int *ldb, double *q, int *ldq, double *z, int *ldz, int *ifst, int *ilst, double *work, int *lwork, int *info);
void F_FUNC(dtgsen,DTGSEN)(int *ijob, int *wantq, int *wantz, int *select, int *n, double *a, int *lda, double *b, int *ldb, double *alphar, double *alphai, double *beta, double *q, int *ldq, double *z, int *ldz, int *m, double *pl, double *pr, double *dif, double *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(dtgsja,DTGSJA)(char *jobu, char *jobv, char *jobq, int *m, int *p, int *n, int *k, int *l, double *a, int *lda, double *b, int *ldb, double *tola, double *tolb, double *alpha, double *beta, double *u, int *ldu, double *v, int *ldv, double *q, int *ldq, double *work, int *ncycle, int *info);
void F_FUNC(dtgsna,DTGSNA)(char *job, char *howmny, int *select, int *n, double *a, int *lda, double *b, int *ldb, double *vl, int *ldvl, double *vr, int *ldvr, double *s, double *dif, int *mm, int *m, double *work, int *lwork, int *iwork, int *info);
void F_FUNC(dtgsy2,DTGSY2)(char *trans, int *ijob, int *m, int *n, double *a, int *lda, double *b, int *ldb, double *c, int *ldc, double *d, int *ldd, double *e, int *lde, double *f, int *ldf, double *scale, double *rdsum, double *rdscal, int *iwork, int *pq, int *info);
void F_FUNC(dtgsyl,DTGSYL)(char *trans, int *ijob, int *m, int *n, double *a, int *lda, double *b, int *ldb, double *c, int *ldc, double *d, int *ldd, double *e, int *lde, double *f, int *ldf, double *scale, double *dif, double *work, int *lwork, int *iwork, int *info);
void F_FUNC(dtpcon,DTPCON)(char *norm, char *uplo, char *diag, int *n, double *ap, double *rcond, double *work, int *iwork, int *info);
void F_FUNC(dtpmqrt,DTPMQRT)(char *side, char *trans, int *m, int *n, int *k, int *l, int *nb, double *v, int *ldv, double *t, int *ldt, double *a, int *lda, double *b, int *ldb, double *work, int *info);
void F_FUNC(dtpqrt,DTPQRT)(int *m, int *n, int *l, int *nb, double *a, int *lda, double *b, int *ldb, double *t, int *ldt, double *work, int *info);
void F_FUNC(dtpqrt2,DTPQRT2)(int *m, int *n, int *l, double *a, int *lda, double *b, int *ldb, double *t, int *ldt, int *info);
void F_FUNC(dtprfb,DTPRFB)(char *side, char *trans, char *direct, char *storev, int *m, int *n, int *k, int *l, double *v, int *ldv, double *t, int *ldt, double *a, int *lda, double *b, int *ldb, double *work, int *ldwork);
void F_FUNC(dtprfs,DTPRFS)(char *uplo, char *trans, char *diag, int *n, int *nrhs, double *ap, double *b, int *ldb, double *x, int *ldx, double *ferr, double *berr, double *work, int *iwork, int *info);
void F_FUNC(dtptri,DTPTRI)(char *uplo, char *diag, int *n, double *ap, int *info);
void F_FUNC(dtptrs,DTPTRS)(char *uplo, char *trans, char *diag, int *n, int *nrhs, double *ap, double *b, int *ldb, int *info);
void F_FUNC(dtpttf,DTPTTF)(char *transr, char *uplo, int *n, double *ap, double *arf, int *info);
void F_FUNC(dtpttr,DTPTTR)(char *uplo, int *n, double *ap, double *a, int *lda, int *info);
void F_FUNC(dtrcon,DTRCON)(char *norm, char *uplo, char *diag, int *n, double *a, int *lda, double *rcond, double *work, int *iwork, int *info);
void F_FUNC(dtrevc,DTREVC)(char *side, char *howmny, int *select, int *n, double *t, int *ldt, double *vl, int *ldvl, double *vr, int *ldvr, int *mm, int *m, double *work, int *info);
void F_FUNC(dtrexc,DTREXC)(char *compq, int *n, double *t, int *ldt, double *q, int *ldq, int *ifst, int *ilst, double *work, int *info);
void F_FUNC(dtrrfs,DTRRFS)(char *uplo, char *trans, char *diag, int *n, int *nrhs, double *a, int *lda, double *b, int *ldb, double *x, int *ldx, double *ferr, double *berr, double *work, int *iwork, int *info);
void F_FUNC(dtrsen,DTRSEN)(char *job, char *compq, int *select, int *n, double *t, int *ldt, double *q, int *ldq, double *wr, double *wi, int *m, double *s, double *sep, double *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(dtrsna,DTRSNA)(char *job, char *howmny, int *select, int *n, double *t, int *ldt, double *vl, int *ldvl, double *vr, int *ldvr, double *s, double *sep, int *mm, int *m, double *work, int *ldwork, int *iwork, int *info);
void F_FUNC(dtrsyl,DTRSYL)(char *trana, char *tranb, int *isgn, int *m, int *n, double *a, int *lda, double *b, int *ldb, double *c, int *ldc, double *scale, int *info);
void F_FUNC(dtrti2,DTRTI2)(char *uplo, char *diag, int *n, double *a, int *lda, int *info);
void F_FUNC(dtrtri,DTRTRI)(char *uplo, char *diag, int *n, double *a, int *lda, int *info);
void F_FUNC(dtrtrs,DTRTRS)(char *uplo, char *trans, char *diag, int *n, int *nrhs, double *a, int *lda, double *b, int *ldb, int *info);
void F_FUNC(dtrttf,DTRTTF)(char *transr, char *uplo, int *n, double *a, int *lda, double *arf, int *info);
void F_FUNC(dtrttp,DTRTTP)(char *uplo, int *n, double *a, int *lda, double *ap, int *info);
void F_FUNC(dtzrzf,DTZRZF)(int *m, int *n, double *a, int *lda, double *tau, double *work, int *lwork, int *info);
void F_FUNC(ilaver,ILAVER)(int *vers_major, int *vers_minor, int *vers_patch);
void F_FUNC(sbbcsd,SBBCSD)(char *jobu1, char *jobu2, char *jobv1t, char *jobv2t, char *trans, int *m, int *p, int *q, float *theta, float *phi, float *u1, int *ldu1, float *u2, int *ldu2, float *v1t, int *ldv1t, float *v2t, int *ldv2t, float *b11d, float *b11e, float *b12d, float *b12e, float *b21d, float *b21e, float *b22d, float *b22e, float *work, int *lwork, int *info);
void F_FUNC(sbdsdc,SBDSDC)(char *uplo, char *compq, int *n, float *d, float *e, float *u, int *ldu, float *vt, int *ldvt, float *q, int *iq, float *work, int *iwork, int *info);
void F_FUNC(sbdsqr,SBDSQR)(char *uplo, int *n, int *ncvt, int *nru, int *ncc, float *d, float *e, float *vt, int *ldvt, float *u, int *ldu, float *c, int *ldc, float *work, int *info);
void F_FUNC(sdisna,SDISNA)(char *job, int *m, int *n, float *d, float *sep, int *info);
void F_FUNC(sgbbrd,SGBBRD)(char *vect, int *m, int *n, int *ncc, int *kl, int *ku, float *ab, int *ldab, float *d, float *e, float *q, int *ldq, float *pt, int *ldpt, float *c, int *ldc, float *work, int *info);
void F_FUNC(sgbcon,SGBCON)(char *norm, int *n, int *kl, int *ku, float *ab, int *ldab, int *ipiv, float *anorm, float *rcond, float *work, int *iwork, int *info);
void F_FUNC(sgbequ,SGBEQU)(int *m, int *n, int *kl, int *ku, float *ab, int *ldab, float *r, float *c, float *rowcnd, float *colcnd, float *amax, int *info);
void F_FUNC(sgbequb,SGBEQUB)(int *m, int *n, int *kl, int *ku, float *ab, int *ldab, float *r, float *c, float *rowcnd, float *colcnd, float *amax, int *info);
void F_FUNC(sgbrfs,SGBRFS)(char *trans, int *n, int *kl, int *ku, int *nrhs, float *ab, int *ldab, float *afb, int *ldafb, int *ipiv, float *b, int *ldb, float *x, int *ldx, float *ferr, float *berr, float *work, int *iwork, int *info);
void F_FUNC(sgbsv,SGBSV)(int *n, int *kl, int *ku, int *nrhs, float *ab, int *ldab, int *ipiv, float *b, int *ldb, int *info);
void F_FUNC(sgbsvx,SGBSVX)(char *fact, char *trans, int *n, int *kl, int *ku, int *nrhs, float *ab, int *ldab, float *afb, int *ldafb, int *ipiv, char *equed, float *r, float *c, float *b, int *ldb, float *x, int *ldx, float *rcond, float *ferr, float *berr, float *work, int *iwork, int *info);
void F_FUNC(sgbtf2,SGBTF2)(int *m, int *n, int *kl, int *ku, float *ab, int *ldab, int *ipiv, int *info);
void F_FUNC(sgbtrf,SGBTRF)(int *m, int *n, int *kl, int *ku, float *ab, int *ldab, int *ipiv, int *info);
void F_FUNC(sgbtrs,SGBTRS)(char *trans, int *n, int *kl, int *ku, int *nrhs, float *ab, int *ldab, int *ipiv, float *b, int *ldb, int *info);
void F_FUNC(sgebak,SGEBAK)(char *job, char *side, int *n, int *ilo, int *ihi, float *scale, int *m, float *v, int *ldv, int *info);
void F_FUNC(sgebal,SGEBAL)(char *job, int *n, float *a, int *lda, int *ilo, int *ihi, float *scale, int *info);
void F_FUNC(sgebd2,SGEBD2)(int *m, int *n, float *a, int *lda, float *d, float *e, float *tauq, float *taup, float *work, int *info);
void F_FUNC(sgebrd,SGEBRD)(int *m, int *n, float *a, int *lda, float *d, float *e, float *tauq, float *taup, float *work, int *lwork, int *info);
void F_FUNC(sgecon,SGECON)(char *norm, int *n, float *a, int *lda, float *anorm, float *rcond, float *work, int *iwork, int *info);
void F_FUNC(sgeequ,SGEEQU)(int *m, int *n, float *a, int *lda, float *r, float *c, float *rowcnd, float *colcnd, float *amax, int *info);
void F_FUNC(sgeequb,SGEEQUB)(int *m, int *n, float *a, int *lda, float *r, float *c, float *rowcnd, float *colcnd, float *amax, int *info);
void F_FUNC(sgees,SGEES)(char *jobvs, char *sort, _sselect2 *select, int *n, float *a, int *lda, int *sdim, float *wr, float *wi, float *vs, int *ldvs, float *work, int *lwork, int *bwork, int *info);
void F_FUNC(sgeesx,SGEESX)(char *jobvs, char *sort, _sselect2 *select, char *sense, int *n, float *a, int *lda, int *sdim, float *wr, float *wi, float *vs, int *ldvs, float *rconde, float *rcondv, float *work, int *lwork, int *iwork, int *liwork, int *bwork, int *info);
void F_FUNC(sgeev,SGEEV)(char *jobvl, char *jobvr, int *n, float *a, int *lda, float *wr, float *wi, float *vl, int *ldvl, float *vr, int *ldvr, float *work, int *lwork, int *info);
void F_FUNC(sgeevx,SGEEVX)(char *balanc, char *jobvl, char *jobvr, char *sense, int *n, float *a, int *lda, float *wr, float *wi, float *vl, int *ldvl, float *vr, int *ldvr, int *ilo, int *ihi, float *scale, float *abnrm, float *rconde, float *rcondv, float *work, int *lwork, int *iwork, int *info);
void F_FUNC(sgehd2,SGEHD2)(int *n, int *ilo, int *ihi, float *a, int *lda, float *tau, float *work, int *info);
void F_FUNC(sgehrd,SGEHRD)(int *n, int *ilo, int *ihi, float *a, int *lda, float *tau, float *work, int *lwork, int *info);
void F_FUNC(sgejsv,SGEJSV)(char *joba, char *jobu, char *jobv, char *jobr, char *jobt, char *jobp, int *m, int *n, float *a, int *lda, float *sva, float *u, int *ldu, float *v, int *ldv, float *work, int *lwork, int *iwork, int *info);
void F_FUNC(sgelq2,SGELQ2)(int *m, int *n, float *a, int *lda, float *tau, float *work, int *info);
void F_FUNC(sgelqf,SGELQF)(int *m, int *n, float *a, int *lda, float *tau, float *work, int *lwork, int *info);
void F_FUNC(sgels,SGELS)(char *trans, int *m, int *n, int *nrhs, float *a, int *lda, float *b, int *ldb, float *work, int *lwork, int *info);
void F_FUNC(sgelsd,SGELSD)(int *m, int *n, int *nrhs, float *a, int *lda, float *b, int *ldb, float *s, float *rcond, int *rank, float *work, int *lwork, int *iwork, int *info);
void F_FUNC(sgelss,SGELSS)(int *m, int *n, int *nrhs, float *a, int *lda, float *b, int *ldb, float *s, float *rcond, int *rank, float *work, int *lwork, int *info);
void F_FUNC(sgelsy,SGELSY)(int *m, int *n, int *nrhs, float *a, int *lda, float *b, int *ldb, int *jpvt, float *rcond, int *rank, float *work, int *lwork, int *info);
void F_FUNC(sgemqrt,SGEMQRT)(char *side, char *trans, int *m, int *n, int *k, int *nb, float *v, int *ldv, float *t, int *ldt, float *c, int *ldc, float *work, int *info);
void F_FUNC(sgeql2,SGEQL2)(int *m, int *n, float *a, int *lda, float *tau, float *work, int *info);
void F_FUNC(sgeqlf,SGEQLF)(int *m, int *n, float *a, int *lda, float *tau, float *work, int *lwork, int *info);
void F_FUNC(sgeqp3,SGEQP3)(int *m, int *n, float *a, int *lda, int *jpvt, float *tau, float *work, int *lwork, int *info);
void F_FUNC(sgeqr2,SGEQR2)(int *m, int *n, float *a, int *lda, float *tau, float *work, int *info);
void F_FUNC(sgeqr2p,SGEQR2P)(int *m, int *n, float *a, int *lda, float *tau, float *work, int *info);
void F_FUNC(sgeqrf,SGEQRF)(int *m, int *n, float *a, int *lda, float *tau, float *work, int *lwork, int *info);
void F_FUNC(sgeqrfp,SGEQRFP)(int *m, int *n, float *a, int *lda, float *tau, float *work, int *lwork, int *info);
void F_FUNC(sgeqrt,SGEQRT)(int *m, int *n, int *nb, float *a, int *lda, float *t, int *ldt, float *work, int *info);
void F_FUNC(sgeqrt2,SGEQRT2)(int *m, int *n, float *a, int *lda, float *t, int *ldt, int *info);
void F_FUNC(sgeqrt3,SGEQRT3)(int *m, int *n, float *a, int *lda, float *t, int *ldt, int *info);
void F_FUNC(sgerfs,SGERFS)(char *trans, int *n, int *nrhs, float *a, int *lda, float *af, int *ldaf, int *ipiv, float *b, int *ldb, float *x, int *ldx, float *ferr, float *berr, float *work, int *iwork, int *info);
void F_FUNC(sgerq2,SGERQ2)(int *m, int *n, float *a, int *lda, float *tau, float *work, int *info);
void F_FUNC(sgerqf,SGERQF)(int *m, int *n, float *a, int *lda, float *tau, float *work, int *lwork, int *info);
void F_FUNC(sgesc2,SGESC2)(int *n, float *a, int *lda, float *rhs, int *ipiv, int *jpiv, float *scale);
void F_FUNC(sgesdd,SGESDD)(char *jobz, int *m, int *n, float *a, int *lda, float *s, float *u, int *ldu, float *vt, int *ldvt, float *work, int *lwork, int *iwork, int *info);
void F_FUNC(sgesv,SGESV)(int *n, int *nrhs, float *a, int *lda, int *ipiv, float *b, int *ldb, int *info);
void F_FUNC(sgesvd,SGESVD)(char *jobu, char *jobvt, int *m, int *n, float *a, int *lda, float *s, float *u, int *ldu, float *vt, int *ldvt, float *work, int *lwork, int *info);
void F_FUNC(sgesvj,SGESVJ)(char *joba, char *jobu, char *jobv, int *m, int *n, float *a, int *lda, float *sva, int *mv, float *v, int *ldv, float *work, int *lwork, int *info);
void F_FUNC(sgesvx,SGESVX)(char *fact, char *trans, int *n, int *nrhs, float *a, int *lda, float *af, int *ldaf, int *ipiv, char *equed, float *r, float *c, float *b, int *ldb, float *x, int *ldx, float *rcond, float *ferr, float *berr, float *work, int *iwork, int *info);
void F_FUNC(sgetc2,SGETC2)(int *n, float *a, int *lda, int *ipiv, int *jpiv, int *info);
void F_FUNC(sgetf2,SGETF2)(int *m, int *n, float *a, int *lda, int *ipiv, int *info);
void F_FUNC(sgetrf,SGETRF)(int *m, int *n, float *a, int *lda, int *ipiv, int *info);
void F_FUNC(sgetri,SGETRI)(int *n, float *a, int *lda, int *ipiv, float *work, int *lwork, int *info);
void F_FUNC(sgetrs,SGETRS)(char *trans, int *n, int *nrhs, float *a, int *lda, int *ipiv, float *b, int *ldb, int *info);
void F_FUNC(sggbak,SGGBAK)(char *job, char *side, int *n, int *ilo, int *ihi, float *lscale, float *rscale, int *m, float *v, int *ldv, int *info);
void F_FUNC(sggbal,SGGBAL)(char *job, int *n, float *a, int *lda, float *b, int *ldb, int *ilo, int *ihi, float *lscale, float *rscale, float *work, int *info);
void F_FUNC(sgges,SGGES)(char *jobvsl, char *jobvsr, char *sort, _sselect3 *selctg, int *n, float *a, int *lda, float *b, int *ldb, int *sdim, float *alphar, float *alphai, float *beta, float *vsl, int *ldvsl, float *vsr, int *ldvsr, float *work, int *lwork, int *bwork, int *info);
void F_FUNC(sggesx,SGGESX)(char *jobvsl, char *jobvsr, char *sort, _sselect3 *selctg, char *sense, int *n, float *a, int *lda, float *b, int *ldb, int *sdim, float *alphar, float *alphai, float *beta, float *vsl, int *ldvsl, float *vsr, int *ldvsr, float *rconde, float *rcondv, float *work, int *lwork, int *iwork, int *liwork, int *bwork, int *info);
void F_FUNC(sggev,SGGEV)(char *jobvl, char *jobvr, int *n, float *a, int *lda, float *b, int *ldb, float *alphar, float *alphai, float *beta, float *vl, int *ldvl, float *vr, int *ldvr, float *work, int *lwork, int *info);
void F_FUNC(sggevx,SGGEVX)(char *balanc, char *jobvl, char *jobvr, char *sense, int *n, float *a, int *lda, float *b, int *ldb, float *alphar, float *alphai, float *beta, float *vl, int *ldvl, float *vr, int *ldvr, int *ilo, int *ihi, float *lscale, float *rscale, float *abnrm, float *bbnrm, float *rconde, float *rcondv, float *work, int *lwork, int *iwork, int *bwork, int *info);
void F_FUNC(sggglm,SGGGLM)(int *n, int *m, int *p, float *a, int *lda, float *b, int *ldb, float *d, float *x, float *y, float *work, int *lwork, int *info);
void F_FUNC(sgghrd,SGGHRD)(char *compq, char *compz, int *n, int *ilo, int *ihi, float *a, int *lda, float *b, int *ldb, float *q, int *ldq, float *z, int *ldz, int *info);
void F_FUNC(sgglse,SGGLSE)(int *m, int *n, int *p, float *a, int *lda, float *b, int *ldb, float *c, float *d, float *x, float *work, int *lwork, int *info);
void F_FUNC(sggqrf,SGGQRF)(int *n, int *m, int *p, float *a, int *lda, float *taua, float *b, int *ldb, float *taub, float *work, int *lwork, int *info);
void F_FUNC(sggrqf,SGGRQF)(int *m, int *p, int *n, float *a, int *lda, float *taua, float *b, int *ldb, float *taub, float *work, int *lwork, int *info);
void F_FUNC(sgsvj0,SGSVJ0)(char *jobv, int *m, int *n, float *a, int *lda, float *d, float *sva, int *mv, float *v, int *ldv, float *eps, float *sfmin, float *tol, int *nsweep, float *work, int *lwork, int *info);
void F_FUNC(sgsvj1,SGSVJ1)(char *jobv, int *m, int *n, int *n1, float *a, int *lda, float *d, float *sva, int *mv, float *v, int *ldv, float *eps, float *sfmin, float *tol, int *nsweep, float *work, int *lwork, int *info);
void F_FUNC(sgtcon,SGTCON)(char *norm, int *n, float *dl, float *d, float *du, float *du2, int *ipiv, float *anorm, float *rcond, float *work, int *iwork, int *info);
void F_FUNC(sgtrfs,SGTRFS)(char *trans, int *n, int *nrhs, float *dl, float *d, float *du, float *dlf, float *df, float *duf, float *du2, int *ipiv, float *b, int *ldb, float *x, int *ldx, float *ferr, float *berr, float *work, int *iwork, int *info);
void F_FUNC(sgtsv,SGTSV)(int *n, int *nrhs, float *dl, float *d, float *du, float *b, int *ldb, int *info);
void F_FUNC(sgtsvx,SGTSVX)(char *fact, char *trans, int *n, int *nrhs, float *dl, float *d, float *du, float *dlf, float *df, float *duf, float *du2, int *ipiv, float *b, int *ldb, float *x, int *ldx, float *rcond, float *ferr, float *berr, float *work, int *iwork, int *info);
void F_FUNC(sgttrf,SGTTRF)(int *n, float *dl, float *d, float *du, float *du2, int *ipiv, int *info);
void F_FUNC(sgttrs,SGTTRS)(char *trans, int *n, int *nrhs, float *dl, float *d, float *du, float *du2, int *ipiv, float *b, int *ldb, int *info);
void F_FUNC(sgtts2,SGTTS2)(int *itrans, int *n, int *nrhs, float *dl, float *d, float *du, float *du2, int *ipiv, float *b, int *ldb);
void F_FUNC(shgeqz,SHGEQZ)(char *job, char *compq, char *compz, int *n, int *ilo, int *ihi, float *h, int *ldh, float *t, int *ldt, float *alphar, float *alphai, float *beta, float *q, int *ldq, float *z, int *ldz, float *work, int *lwork, int *info);
void F_FUNC(shsein,SHSEIN)(char *side, char *eigsrc, char *initv, int *select, int *n, float *h, int *ldh, float *wr, float *wi, float *vl, int *ldvl, float *vr, int *ldvr, int *mm, int *m, float *work, int *ifaill, int *ifailr, int *info);
void F_FUNC(shseqr,SHSEQR)(char *job, char *compz, int *n, int *ilo, int *ihi, float *h, int *ldh, float *wr, float *wi, float *z, int *ldz, float *work, int *lwork, int *info);
void F_FUNC(slabad,SLABAD)(float *small, float *large);
void F_FUNC(slabrd,SLABRD)(int *m, int *n, int *nb, float *a, int *lda, float *d, float *e, float *tauq, float *taup, float *x, int *ldx, float *y, int *ldy);
void F_FUNC(slacn2,SLACN2)(int *n, float *v, float *x, int *isgn, float *est, int *kase, int *isave);
void F_FUNC(slacon,SLACON)(int *n, float *v, float *x, int *isgn, float *est, int *kase);
void F_FUNC(slacpy,SLACPY)(char *uplo, int *m, int *n, float *a, int *lda, float *b, int *ldb);
void F_FUNC(sladiv,SLADIV)(float *a, float *b, float *c, float *d, float *p, float *q);
void F_FUNC(slae2,SLAE2)(float *a, float *b, float *c, float *rt1, float *rt2);
void F_FUNC(slaebz,SLAEBZ)(int *ijob, int *nitmax, int *n, int *mmax, int *minp, int *nbmin, float *abstol, float *reltol, float *pivmin, float *d, float *e, float *e2, int *nval, float *ab, float *c, int *mout, int *nab, float *work, int *iwork, int *info);
void F_FUNC(slaed0,SLAED0)(int *icompq, int *qsiz, int *n, float *d, float *e, float *q, int *ldq, float *qstore, int *ldqs, float *work, int *iwork, int *info);
void F_FUNC(slaed1,SLAED1)(int *n, float *d, float *q, int *ldq, int *indxq, float *rho, int *cutpnt, float *work, int *iwork, int *info);
void F_FUNC(slaed2,SLAED2)(int *k, int *n, int *n1, float *d, float *q, int *ldq, int *indxq, float *rho, float *z, float *dlamda, float *w, float *q2, int *indx, int *indxc, int *indxp, int *coltyp, int *info);
void F_FUNC(slaed3,SLAED3)(int *k, int *n, int *n1, float *d, float *q, int *ldq, float *rho, float *dlamda, float *q2, int *indx, int *ctot, float *w, float *s, int *info);
void F_FUNC(slaed4,SLAED4)(int *n, int *i, float *d, float *z, float *delta, float *rho, float *dlam, int *info);
void F_FUNC(slaed5,SLAED5)(int *i, float *d, float *z, float *delta, float *rho, float *dlam);
void F_FUNC(slaed6,SLAED6)(int *kniter, int *orgati, float *rho, float *d, float *z, float *finit, float *tau, int *info);
void F_FUNC(slaed7,SLAED7)(int *icompq, int *n, int *qsiz, int *tlvls, int *curlvl, int *curpbm, float *d, float *q, int *ldq, int *indxq, float *rho, int *cutpnt, float *qstore, int *qptr, int *prmptr, int *perm, int *givptr, int *givcol, float *givnum, float *work, int *iwork, int *info);
void F_FUNC(slaed8,SLAED8)(int *icompq, int *k, int *n, int *qsiz, float *d, float *q, int *ldq, int *indxq, float *rho, int *cutpnt, float *z, float *dlamda, float *q2, int *ldq2, float *w, int *perm, int *givptr, int *givcol, float *givnum, int *indxp, int *indx, int *info);
void F_FUNC(slaed9,SLAED9)(int *k, int *kstart, int *kstop, int *n, float *d, float *q, int *ldq, float *rho, float *dlamda, float *w, float *s, int *lds, int *info);
void F_FUNC(slaeda,SLAEDA)(int *n, int *tlvls, int *curlvl, int *curpbm, int *prmptr, int *perm, int *givptr, int *givcol, float *givnum, float *q, int *qptr, float *z, float *ztemp, int *info);
void F_FUNC(slaein,SLAEIN)(int *rightv, int *noinit, int *n, float *h, int *ldh, float *wr, float *wi, float *vr, float *vi, float *b, int *ldb, float *work, float *eps3, float *smlnum, float *bignum, int *info);
void F_FUNC(slaev2,SLAEV2)(float *a, float *b, float *c, float *rt1, float *rt2, float *cs1, float *sn1);
void F_FUNC(slaexc,SLAEXC)(int *wantq, int *n, float *t, int *ldt, float *q, int *ldq, int *j1, int *n1, int *n2, float *work, int *info);
void F_FUNC(slag2,SLAG2)(float *a, int *lda, float *b, int *ldb, float *safmin, float *scale1, float *scale2, float *wr1, float *wr2, float *wi);
void F_FUNC(slag2d,SLAG2D)(int *m, int *n, float *sa, int *ldsa, double *a, int *lda, int *info);
void F_FUNC(slags2,SLAGS2)(int *upper, float *a1, float *a2, float *a3, float *b1, float *b2, float *b3, float *csu, float *snu, float *csv, float *snv, float *csq, float *snq);
void F_FUNC(slagtf,SLAGTF)(int *n, float *a, float *lambda, float *b, float *c, float *tol, float *d, int *in, int *info);
void F_FUNC(slagtm,SLAGTM)(char *trans, int *n, int *nrhs, float *alpha, float *dl, float *d, float *du, float *x, int *ldx, float *beta, float *b, int *ldb);
void F_FUNC(slagts,SLAGTS)(int *job, int *n, float *a, float *b, float *c, float *d, int *in, float *y, float *tol, int *info);
void F_FUNC(slagv2,SLAGV2)(float *a, int *lda, float *b, int *ldb, float *alphar, float *alphai, float *beta, float *csl, float *snl, float *csr, float *snr);
void F_FUNC(slahqr,SLAHQR)(int *wantt, int *wantz, int *n, int *ilo, int *ihi, float *h, int *ldh, float *wr, float *wi, int *iloz, int *ihiz, float *z, int *ldz, int *info);
void F_FUNC(slahr2,SLAHR2)(int *n, int *k, int *nb, float *a, int *lda, float *tau, float *t, int *ldt, float *y, int *ldy);
void F_FUNC(slaic1,SLAIC1)(int *job, int *j, float *x, float *sest, float *w, float *gamma, float *sestpr, float *s, float *c);
void F_FUNC(slaln2,SLALN2)(int *ltrans, int *na, int *nw, float *smin, float *ca, float *a, int *lda, float *d1, float *d2, float *b, int *ldb, float *wr, float *wi, float *x, int *ldx, float *scale, float *xnorm, int *info);
void F_FUNC(slals0,SLALS0)(int *icompq, int *nl, int *nr, int *sqre, int *nrhs, float *b, int *ldb, float *bx, int *ldbx, int *perm, int *givptr, int *givcol, int *ldgcol, float *givnum, int *ldgnum, float *poles, float *difl, float *difr, float *z, int *k, float *c, float *s, float *work, int *info);
void F_FUNC(slalsa,SLALSA)(int *icompq, int *smlsiz, int *n, int *nrhs, float *b, int *ldb, float *bx, int *ldbx, float *u, int *ldu, float *vt, int *k, float *difl, float *difr, float *z, float *poles, int *givptr, int *givcol, int *ldgcol, int *perm, float *givnum, float *c, float *s, float *work, int *iwork, int *info);
void F_FUNC(slalsd,SLALSD)(char *uplo, int *smlsiz, int *n, int *nrhs, float *d, float *e, float *b, int *ldb, float *rcond, int *rank, float *work, int *iwork, int *info);
void F_FUNC(slamrg,SLAMRG)(int *n1, int *n2, float *a, int *strd1, int *strd2, int *index_bn);
void F_FUNC(slanv2,SLANV2)(float *a, float *b, float *c, float *d, float *rt1r, float *rt1i, float *rt2r, float *rt2i, float *cs, float *sn);
void F_FUNC(slapll,SLAPLL)(int *n, float *x, int *incx, float *y, int *incy, float *ssmin);
void F_FUNC(slapmr,SLAPMR)(int *forwrd, int *m, int *n, float *x, int *ldx, int *k);
void F_FUNC(slapmt,SLAPMT)(int *forwrd, int *m, int *n, float *x, int *ldx, int *k);
void F_FUNC(slaqgb,SLAQGB)(int *m, int *n, int *kl, int *ku, float *ab, int *ldab, float *r, float *c, float *rowcnd, float *colcnd, float *amax, char *equed);
void F_FUNC(slaqge,SLAQGE)(int *m, int *n, float *a, int *lda, float *r, float *c, float *rowcnd, float *colcnd, float *amax, char *equed);
void F_FUNC(slaqp2,SLAQP2)(int *m, int *n, int *offset, float *a, int *lda, int *jpvt, float *tau, float *vn1, float *vn2, float *work);
void F_FUNC(slaqps,SLAQPS)(int *m, int *n, int *offset, int *nb, int *kb, float *a, int *lda, int *jpvt, float *tau, float *vn1, float *vn2, float *auxv, float *f, int *ldf);
void F_FUNC(slaqr0,SLAQR0)(int *wantt, int *wantz, int *n, int *ilo, int *ihi, float *h, int *ldh, float *wr, float *wi, int *iloz, int *ihiz, float *z, int *ldz, float *work, int *lwork, int *info);
void F_FUNC(slaqr1,SLAQR1)(int *n, float *h, int *ldh, float *sr1, float *si1, float *sr2, float *si2, float *v);
void F_FUNC(slaqr2,SLAQR2)(int *wantt, int *wantz, int *n, int *ktop, int *kbot, int *nw, float *h, int *ldh, int *iloz, int *ihiz, float *z, int *ldz, int *ns, int *nd, float *sr, float *si, float *v, int *ldv, int *nh, float *t, int *ldt, int *nv, float *wv, int *ldwv, float *work, int *lwork);
void F_FUNC(slaqr3,SLAQR3)(int *wantt, int *wantz, int *n, int *ktop, int *kbot, int *nw, float *h, int *ldh, int *iloz, int *ihiz, float *z, int *ldz, int *ns, int *nd, float *sr, float *si, float *v, int *ldv, int *nh, float *t, int *ldt, int *nv, float *wv, int *ldwv, float *work, int *lwork);
void F_FUNC(slaqr4,SLAQR4)(int *wantt, int *wantz, int *n, int *ilo, int *ihi, float *h, int *ldh, float *wr, float *wi, int *iloz, int *ihiz, float *z, int *ldz, float *work, int *lwork, int *info);
void F_FUNC(slaqr5,SLAQR5)(int *wantt, int *wantz, int *kacc22, int *n, int *ktop, int *kbot, int *nshfts, float *sr, float *si, float *h, int *ldh, int *iloz, int *ihiz, float *z, int *ldz, float *v, int *ldv, float *u, int *ldu, int *nv, float *wv, int *ldwv, int *nh, float *wh, int *ldwh);
void F_FUNC(slaqsb,SLAQSB)(char *uplo, int *n, int *kd, float *ab, int *ldab, float *s, float *scond, float *amax, char *equed);
void F_FUNC(slaqsp,SLAQSP)(char *uplo, int *n, float *ap, float *s, float *scond, float *amax, char *equed);
void F_FUNC(slaqsy,SLAQSY)(char *uplo, int *n, float *a, int *lda, float *s, float *scond, float *amax, char *equed);
void F_FUNC(slaqtr,SLAQTR)(int *ltran, int *lreal, int *n, float *t, int *ldt, float *b, float *w, float *scale, float *x, float *work, int *info);
void F_FUNC(slar1v,SLAR1V)(int *n, int *b1, int *bn, float *lambda, float *d, float *l, float *ld, float *lld, float *pivmin, float *gaptol, float *z, int *wantnc, int *negcnt, float *ztz, float *mingma, int *r, int *isuppz, float *nrminv, float *resid, float *rqcorr, float *work);
void F_FUNC(slar2v,SLAR2V)(int *n, float *x, float *y, float *z, int *incx, float *c, float *s, int *incc);
void F_FUNC(slarf,SLARF)(char *side, int *m, int *n, float *v, int *incv, float *tau, float *c, int *ldc, float *work);
void F_FUNC(slarfb,SLARFB)(char *side, char *trans, char *direct, char *storev, int *m, int *n, int *k, float *v, int *ldv, float *t, int *ldt, float *c, int *ldc, float *work, int *ldwork);
void F_FUNC(slarfg,SLARFG)(int *n, float *alpha, float *x, int *incx, float *tau);
void F_FUNC(slarfgp,SLARFGP)(int *n, float *alpha, float *x, int *incx, float *tau);
void F_FUNC(slarft,SLARFT)(char *direct, char *storev, int *n, int *k, float *v, int *ldv, float *tau, float *t, int *ldt);
void F_FUNC(slarfx,SLARFX)(char *side, int *m, int *n, float *v, float *tau, float *c, int *ldc, float *work);
void F_FUNC(slargv,SLARGV)(int *n, float *x, int *incx, float *y, int *incy, float *c, int *incc);
void F_FUNC(slarnv,SLARNV)(int *idist, int *iseed, int *n, float *x);
void F_FUNC(slarra,SLARRA)(int *n, float *d, float *e, float *e2, float *spltol, float *tnrm, int *nsplit, int *isplit, int *info);
void F_FUNC(slarrb,SLARRB)(int *n, float *d, float *lld, int *ifirst, int *ilast, float *rtol1, float *rtol2, int *offset, float *w, float *wgap, float *werr, float *work, int *iwork, float *pivmin, float *spdiam, int *twist, int *info);
void F_FUNC(slarrc,SLARRC)(char *jobt, int *n, float *vl, float *vu, float *d, float *e, float *pivmin, int *eigcnt, int *lcnt, int *rcnt, int *info);
void F_FUNC(slarrd,SLARRD)(char *range, char *order, int *n, float *vl, float *vu, int *il, int *iu, float *gers, float *reltol, float *d, float *e, float *e2, float *pivmin, int *nsplit, int *isplit, int *m, float *w, float *werr, float *wl, float *wu, int *iblock, int *indexw, float *work, int *iwork, int *info);
void F_FUNC(slarre,SLARRE)(char *range, int *n, float *vl, float *vu, int *il, int *iu, float *d, float *e, float *e2, float *rtol1, float *rtol2, float *spltol, int *nsplit, int *isplit, int *m, float *w, float *werr, float *wgap, int *iblock, int *indexw, float *gers, float *pivmin, float *work, int *iwork, int *info);
void F_FUNC(slarrf,SLARRF)(int *n, float *d, float *l, float *ld, int *clstrt, int *clend, float *w, float *wgap, float *werr, float *spdiam, float *clgapl, float *clgapr, float *pivmin, float *sigma, float *dplus, float *lplus, float *work, int *info);
void F_FUNC(slarrj,SLARRJ)(int *n, float *d, float *e2, int *ifirst, int *ilast, float *rtol, int *offset, float *w, float *werr, float *work, int *iwork, float *pivmin, float *spdiam, int *info);
void F_FUNC(slarrk,SLARRK)(int *n, int *iw, float *gl, float *gu, float *d, float *e2, float *pivmin, float *reltol, float *w, float *werr, int *info);
void F_FUNC(slarrr,SLARRR)(int *n, float *d, float *e, int *info);
void F_FUNC(slarrv,SLARRV)(int *n, float *vl, float *vu, float *d, float *l, float *pivmin, int *isplit, int *m, int *dol, int *dou, float *minrgp, float *rtol1, float *rtol2, float *w, float *werr, float *wgap, int *iblock, int *indexw, float *gers, float *z, int *ldz, int *isuppz, float *work, int *iwork, int *info);
void F_FUNC(slartg,SLARTG)(float *f, float *g, float *cs, float *sn, float *r);
void F_FUNC(slartgp,SLARTGP)(float *f, float *g, float *cs, float *sn, float *r);
void F_FUNC(slartgs,SLARTGS)(float *x, float *y, float *sigma, float *cs, float *sn);
void F_FUNC(slartv,SLARTV)(int *n, float *x, int *incx, float *y, int *incy, float *c, float *s, int *incc);
void F_FUNC(slaruv,SLARUV)(int *iseed, int *n, float *x);
void F_FUNC(slarz,SLARZ)(char *side, int *m, int *n, int *l, float *v, int *incv, float *tau, float *c, int *ldc, float *work);
void F_FUNC(slarzb,SLARZB)(char *side, char *trans, char *direct, char *storev, int *m, int *n, int *k, int *l, float *v, int *ldv, float *t, int *ldt, float *c, int *ldc, float *work, int *ldwork);
void F_FUNC(slarzt,SLARZT)(char *direct, char *storev, int *n, int *k, float *v, int *ldv, float *tau, float *t, int *ldt);
void F_FUNC(slas2,SLAS2)(float *f, float *g, float *h, float *ssmin, float *ssmax);
void F_FUNC(slascl,SLASCL)(char *type_bn, int *kl, int *ku, float *cfrom, float *cto, int *m, int *n, float *a, int *lda, int *info);
void F_FUNC(slasd0,SLASD0)(int *n, int *sqre, float *d, float *e, float *u, int *ldu, float *vt, int *ldvt, int *smlsiz, int *iwork, float *work, int *info);
void F_FUNC(slasd1,SLASD1)(int *nl, int *nr, int *sqre, float *d, float *alpha, float *beta, float *u, int *ldu, float *vt, int *ldvt, int *idxq, int *iwork, float *work, int *info);
void F_FUNC(slasd2,SLASD2)(int *nl, int *nr, int *sqre, int *k, float *d, float *z, float *alpha, float *beta, float *u, int *ldu, float *vt, int *ldvt, float *dsigma, float *u2, int *ldu2, float *vt2, int *ldvt2, int *idxp, int *idx, int *idxc, int *idxq, int *coltyp, int *info);
void F_FUNC(slasd3,SLASD3)(int *nl, int *nr, int *sqre, int *k, float *d, float *q, int *ldq, float *dsigma, float *u, int *ldu, float *u2, int *ldu2, float *vt, int *ldvt, float *vt2, int *ldvt2, int *idxc, int *ctot, float *z, int *info);
void F_FUNC(slasd4,SLASD4)(int *n, int *i, float *d, float *z, float *delta, float *rho, float *sigma, float *work, int *info);
void F_FUNC(slasd5,SLASD5)(int *i, float *d, float *z, float *delta, float *rho, float *dsigma, float *work);
void F_FUNC(slasd6,SLASD6)(int *icompq, int *nl, int *nr, int *sqre, float *d, float *vf, float *vl, float *alpha, float *beta, int *idxq, int *perm, int *givptr, int *givcol, int *ldgcol, float *givnum, int *ldgnum, float *poles, float *difl, float *difr, float *z, int *k, float *c, float *s, float *work, int *iwork, int *info);
void F_FUNC(slasd7,SLASD7)(int *icompq, int *nl, int *nr, int *sqre, int *k, float *d, float *z, float *zw, float *vf, float *vfw, float *vl, float *vlw, float *alpha, float *beta, float *dsigma, int *idx, int *idxp, int *idxq, int *perm, int *givptr, int *givcol, int *ldgcol, float *givnum, int *ldgnum, float *c, float *s, int *info);
void F_FUNC(slasd8,SLASD8)(int *icompq, int *k, float *d, float *z, float *vf, float *vl, float *difl, float *difr, int *lddifr, float *dsigma, float *work, int *info);
void F_FUNC(slasda,SLASDA)(int *icompq, int *smlsiz, int *n, int *sqre, float *d, float *e, float *u, int *ldu, float *vt, int *k, float *difl, float *difr, float *z, float *poles, int *givptr, int *givcol, int *ldgcol, int *perm, float *givnum, float *c, float *s, float *work, int *iwork, int *info);
void F_FUNC(slasdq,SLASDQ)(char *uplo, int *sqre, int *n, int *ncvt, int *nru, int *ncc, float *d, float *e, float *vt, int *ldvt, float *u, int *ldu, float *c, int *ldc, float *work, int *info);
void F_FUNC(slasdt,SLASDT)(int *n, int *lvl, int *nd, int *inode, int *ndiml, int *ndimr, int *msub);
void F_FUNC(slaset,SLASET)(char *uplo, int *m, int *n, float *alpha, float *beta, float *a, int *lda);
void F_FUNC(slasq1,SLASQ1)(int *n, float *d, float *e, float *work, int *info);
void F_FUNC(slasq2,SLASQ2)(int *n, float *z, int *info);
void F_FUNC(slasq3,SLASQ3)(int *i0, int *n0, float *z, int *pp, float *dmin, float *sigma, float *desig, float *qmax, int *nfail, int *iter, int *ndiv, int *ieee, int *ttype, float *dmin1, float *dmin2, float *dn, float *dn1, float *dn2, float *g, float *tau);
void F_FUNC(slasq4,SLASQ4)(int *i0, int *n0, float *z, int *pp, int *n0in, float *dmin, float *dmin1, float *dmin2, float *dn, float *dn1, float *dn2, float *tau, int *ttype, float *g);
void F_FUNC(slasq6,SLASQ6)(int *i0, int *n0, float *z, int *pp, float *dmin, float *dmin1, float *dmin2, float *dn, float *dnm1, float *dnm2);
void F_FUNC(slasr,SLASR)(char *side, char *pivot, char *direct, int *m, int *n, float *c, float *s, float *a, int *lda);
void F_FUNC(slasrt,SLASRT)(char *id, int *n, float *d, int *info);
void F_FUNC(slassq,SLASSQ)(int *n, float *x, int *incx, float *scale, float *sumsq);
void F_FUNC(slasv2,SLASV2)(float *f, float *g, float *h, float *ssmin, float *ssmax, float *snr, float *csr, float *snl, float *csl);
void F_FUNC(slaswp,SLASWP)(int *n, float *a, int *lda, int *k1, int *k2, int *ipiv, int *incx);
void F_FUNC(slasy2,SLASY2)(int *ltranl, int *ltranr, int *isgn, int *n1, int *n2, float *tl, int *ldtl, float *tr, int *ldtr, float *b, int *ldb, float *scale, float *x, int *ldx, float *xnorm, int *info);
void F_FUNC(slasyf,SLASYF)(char *uplo, int *n, int *nb, int *kb, float *a, int *lda, int *ipiv, float *w, int *ldw, int *info);
void F_FUNC(slatbs,SLATBS)(char *uplo, char *trans, char *diag, char *normin, int *n, int *kd, float *ab, int *ldab, float *x, float *scale, float *cnorm, int *info);
void F_FUNC(slatdf,SLATDF)(int *ijob, int *n, float *z, int *ldz, float *rhs, float *rdsum, float *rdscal, int *ipiv, int *jpiv);
void F_FUNC(slatps,SLATPS)(char *uplo, char *trans, char *diag, char *normin, int *n, float *ap, float *x, float *scale, float *cnorm, int *info);
void F_FUNC(slatrd,SLATRD)(char *uplo, int *n, int *nb, float *a, int *lda, float *e, float *tau, float *w, int *ldw);
void F_FUNC(slatrs,SLATRS)(char *uplo, char *trans, char *diag, char *normin, int *n, float *a, int *lda, float *x, float *scale, float *cnorm, int *info);
void F_FUNC(slatrz,SLATRZ)(int *m, int *n, int *l, float *a, int *lda, float *tau, float *work);
void F_FUNC(slauu2,SLAUU2)(char *uplo, int *n, float *a, int *lda, int *info);
void F_FUNC(slauum,SLAUUM)(char *uplo, int *n, float *a, int *lda, int *info);
void F_FUNC(sopgtr,SOPGTR)(char *uplo, int *n, float *ap, float *tau, float *q, int *ldq, float *work, int *info);
void F_FUNC(sopmtr,SOPMTR)(char *side, char *uplo, char *trans, int *m, int *n, float *ap, float *tau, float *c, int *ldc, float *work, int *info);
void F_FUNC(sorbdb,SORBDB)(char *trans, char *signs, int *m, int *p, int *q, float *x11, int *ldx11, float *x12, int *ldx12, float *x21, int *ldx21, float *x22, int *ldx22, float *theta, float *phi, float *taup1, float *taup2, float *tauq1, float *tauq2, float *work, int *lwork, int *info);
void F_FUNC(sorcsd,SORCSD)(char *jobu1, char *jobu2, char *jobv1t, char *jobv2t, char *trans, char *signs, int *m, int *p, int *q, float *x11, int *ldx11, float *x12, int *ldx12, float *x21, int *ldx21, float *x22, int *ldx22, float *theta, float *u1, int *ldu1, float *u2, int *ldu2, float *v1t, int *ldv1t, float *v2t, int *ldv2t, float *work, int *lwork, int *iwork, int *info);
void F_FUNC(sorg2l,SORG2L)(int *m, int *n, int *k, float *a, int *lda, float *tau, float *work, int *info);
void F_FUNC(sorg2r,SORG2R)(int *m, int *n, int *k, float *a, int *lda, float *tau, float *work, int *info);
void F_FUNC(sorgbr,SORGBR)(char *vect, int *m, int *n, int *k, float *a, int *lda, float *tau, float *work, int *lwork, int *info);
void F_FUNC(sorghr,SORGHR)(int *n, int *ilo, int *ihi, float *a, int *lda, float *tau, float *work, int *lwork, int *info);
void F_FUNC(sorgl2,SORGL2)(int *m, int *n, int *k, float *a, int *lda, float *tau, float *work, int *info);
void F_FUNC(sorglq,SORGLQ)(int *m, int *n, int *k, float *a, int *lda, float *tau, float *work, int *lwork, int *info);
void F_FUNC(sorgql,SORGQL)(int *m, int *n, int *k, float *a, int *lda, float *tau, float *work, int *lwork, int *info);
void F_FUNC(sorgqr,SORGQR)(int *m, int *n, int *k, float *a, int *lda, float *tau, float *work, int *lwork, int *info);
void F_FUNC(sorgr2,SORGR2)(int *m, int *n, int *k, float *a, int *lda, float *tau, float *work, int *info);
void F_FUNC(sorgrq,SORGRQ)(int *m, int *n, int *k, float *a, int *lda, float *tau, float *work, int *lwork, int *info);
void F_FUNC(sorgtr,SORGTR)(char *uplo, int *n, float *a, int *lda, float *tau, float *work, int *lwork, int *info);
void F_FUNC(sorm2l,SORM2L)(char *side, char *trans, int *m, int *n, int *k, float *a, int *lda, float *tau, float *c, int *ldc, float *work, int *info);
void F_FUNC(sorm2r,SORM2R)(char *side, char *trans, int *m, int *n, int *k, float *a, int *lda, float *tau, float *c, int *ldc, float *work, int *info);
void F_FUNC(sormbr,SORMBR)(char *vect, char *side, char *trans, int *m, int *n, int *k, float *a, int *lda, float *tau, float *c, int *ldc, float *work, int *lwork, int *info);
void F_FUNC(sormhr,SORMHR)(char *side, char *trans, int *m, int *n, int *ilo, int *ihi, float *a, int *lda, float *tau, float *c, int *ldc, float *work, int *lwork, int *info);
void F_FUNC(sorml2,SORML2)(char *side, char *trans, int *m, int *n, int *k, float *a, int *lda, float *tau, float *c, int *ldc, float *work, int *info);
void F_FUNC(sormlq,SORMLQ)(char *side, char *trans, int *m, int *n, int *k, float *a, int *lda, float *tau, float *c, int *ldc, float *work, int *lwork, int *info);
void F_FUNC(sormql,SORMQL)(char *side, char *trans, int *m, int *n, int *k, float *a, int *lda, float *tau, float *c, int *ldc, float *work, int *lwork, int *info);
void F_FUNC(sormqr,SORMQR)(char *side, char *trans, int *m, int *n, int *k, float *a, int *lda, float *tau, float *c, int *ldc, float *work, int *lwork, int *info);
void F_FUNC(sormr2,SORMR2)(char *side, char *trans, int *m, int *n, int *k, float *a, int *lda, float *tau, float *c, int *ldc, float *work, int *info);
void F_FUNC(sormr3,SORMR3)(char *side, char *trans, int *m, int *n, int *k, int *l, float *a, int *lda, float *tau, float *c, int *ldc, float *work, int *info);
void F_FUNC(sormrq,SORMRQ)(char *side, char *trans, int *m, int *n, int *k, float *a, int *lda, float *tau, float *c, int *ldc, float *work, int *lwork, int *info);
void F_FUNC(sormrz,SORMRZ)(char *side, char *trans, int *m, int *n, int *k, int *l, float *a, int *lda, float *tau, float *c, int *ldc, float *work, int *lwork, int *info);
void F_FUNC(sormtr,SORMTR)(char *side, char *uplo, char *trans, int *m, int *n, float *a, int *lda, float *tau, float *c, int *ldc, float *work, int *lwork, int *info);
void F_FUNC(spbcon,SPBCON)(char *uplo, int *n, int *kd, float *ab, int *ldab, float *anorm, float *rcond, float *work, int *iwork, int *info);
void F_FUNC(spbequ,SPBEQU)(char *uplo, int *n, int *kd, float *ab, int *ldab, float *s, float *scond, float *amax, int *info);
void F_FUNC(spbrfs,SPBRFS)(char *uplo, int *n, int *kd, int *nrhs, float *ab, int *ldab, float *afb, int *ldafb, float *b, int *ldb, float *x, int *ldx, float *ferr, float *berr, float *work, int *iwork, int *info);
void F_FUNC(spbstf,SPBSTF)(char *uplo, int *n, int *kd, float *ab, int *ldab, int *info);
void F_FUNC(spbsv,SPBSV)(char *uplo, int *n, int *kd, int *nrhs, float *ab, int *ldab, float *b, int *ldb, int *info);
void F_FUNC(spbsvx,SPBSVX)(char *fact, char *uplo, int *n, int *kd, int *nrhs, float *ab, int *ldab, float *afb, int *ldafb, char *equed, float *s, float *b, int *ldb, float *x, int *ldx, float *rcond, float *ferr, float *berr, float *work, int *iwork, int *info);
void F_FUNC(spbtf2,SPBTF2)(char *uplo, int *n, int *kd, float *ab, int *ldab, int *info);
void F_FUNC(spbtrf,SPBTRF)(char *uplo, int *n, int *kd, float *ab, int *ldab, int *info);
void F_FUNC(spbtrs,SPBTRS)(char *uplo, int *n, int *kd, int *nrhs, float *ab, int *ldab, float *b, int *ldb, int *info);
void F_FUNC(spftrf,SPFTRF)(char *transr, char *uplo, int *n, float *a, int *info);
void F_FUNC(spftri,SPFTRI)(char *transr, char *uplo, int *n, float *a, int *info);
void F_FUNC(spftrs,SPFTRS)(char *transr, char *uplo, int *n, int *nrhs, float *a, float *b, int *ldb, int *info);
void F_FUNC(spocon,SPOCON)(char *uplo, int *n, float *a, int *lda, float *anorm, float *rcond, float *work, int *iwork, int *info);
void F_FUNC(spoequ,SPOEQU)(int *n, float *a, int *lda, float *s, float *scond, float *amax, int *info);
void F_FUNC(spoequb,SPOEQUB)(int *n, float *a, int *lda, float *s, float *scond, float *amax, int *info);
void F_FUNC(sporfs,SPORFS)(char *uplo, int *n, int *nrhs, float *a, int *lda, float *af, int *ldaf, float *b, int *ldb, float *x, int *ldx, float *ferr, float *berr, float *work, int *iwork, int *info);
void F_FUNC(sposv,SPOSV)(char *uplo, int *n, int *nrhs, float *a, int *lda, float *b, int *ldb, int *info);
void F_FUNC(sposvx,SPOSVX)(char *fact, char *uplo, int *n, int *nrhs, float *a, int *lda, float *af, int *ldaf, char *equed, float *s, float *b, int *ldb, float *x, int *ldx, float *rcond, float *ferr, float *berr, float *work, int *iwork, int *info);
void F_FUNC(spotf2,SPOTF2)(char *uplo, int *n, float *a, int *lda, int *info);
void F_FUNC(spotrf,SPOTRF)(char *uplo, int *n, float *a, int *lda, int *info);
void F_FUNC(spotri,SPOTRI)(char *uplo, int *n, float *a, int *lda, int *info);
void F_FUNC(spotrs,SPOTRS)(char *uplo, int *n, int *nrhs, float *a, int *lda, float *b, int *ldb, int *info);
void F_FUNC(sppcon,SPPCON)(char *uplo, int *n, float *ap, float *anorm, float *rcond, float *work, int *iwork, int *info);
void F_FUNC(sppequ,SPPEQU)(char *uplo, int *n, float *ap, float *s, float *scond, float *amax, int *info);
void F_FUNC(spprfs,SPPRFS)(char *uplo, int *n, int *nrhs, float *ap, float *afp, float *b, int *ldb, float *x, int *ldx, float *ferr, float *berr, float *work, int *iwork, int *info);
void F_FUNC(sppsv,SPPSV)(char *uplo, int *n, int *nrhs, float *ap, float *b, int *ldb, int *info);
void F_FUNC(sppsvx,SPPSVX)(char *fact, char *uplo, int *n, int *nrhs, float *ap, float *afp, char *equed, float *s, float *b, int *ldb, float *x, int *ldx, float *rcond, float *ferr, float *berr, float *work, int *iwork, int *info);
void F_FUNC(spptrf,SPPTRF)(char *uplo, int *n, float *ap, int *info);
void F_FUNC(spptri,SPPTRI)(char *uplo, int *n, float *ap, int *info);
void F_FUNC(spptrs,SPPTRS)(char *uplo, int *n, int *nrhs, float *ap, float *b, int *ldb, int *info);
void F_FUNC(spstf2,SPSTF2)(char *uplo, int *n, float *a, int *lda, int *piv, int *rank, float *tol, float *work, int *info);
void F_FUNC(spstrf,SPSTRF)(char *uplo, int *n, float *a, int *lda, int *piv, int *rank, float *tol, float *work, int *info);
void F_FUNC(sptcon,SPTCON)(int *n, float *d, float *e, float *anorm, float *rcond, float *work, int *info);
void F_FUNC(spteqr,SPTEQR)(char *compz, int *n, float *d, float *e, float *z, int *ldz, float *work, int *info);
void F_FUNC(sptrfs,SPTRFS)(int *n, int *nrhs, float *d, float *e, float *df, float *ef, float *b, int *ldb, float *x, int *ldx, float *ferr, float *berr, float *work, int *info);
void F_FUNC(sptsv,SPTSV)(int *n, int *nrhs, float *d, float *e, float *b, int *ldb, int *info);
void F_FUNC(sptsvx,SPTSVX)(char *fact, int *n, int *nrhs, float *d, float *e, float *df, float *ef, float *b, int *ldb, float *x, int *ldx, float *rcond, float *ferr, float *berr, float *work, int *info);
void F_FUNC(spttrf,SPTTRF)(int *n, float *d, float *e, int *info);
void F_FUNC(spttrs,SPTTRS)(int *n, int *nrhs, float *d, float *e, float *b, int *ldb, int *info);
void F_FUNC(sptts2,SPTTS2)(int *n, int *nrhs, float *d, float *e, float *b, int *ldb);
void F_FUNC(srscl,SRSCL)(int *n, float *sa, float *sx, int *incx);
void F_FUNC(ssbev,SSBEV)(char *jobz, char *uplo, int *n, int *kd, float *ab, int *ldab, float *w, float *z, int *ldz, float *work, int *info);
void F_FUNC(ssbevd,SSBEVD)(char *jobz, char *uplo, int *n, int *kd, float *ab, int *ldab, float *w, float *z, int *ldz, float *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(ssbevx,SSBEVX)(char *jobz, char *range, char *uplo, int *n, int *kd, float *ab, int *ldab, float *q, int *ldq, float *vl, float *vu, int *il, int *iu, float *abstol, int *m, float *w, float *z, int *ldz, float *work, int *iwork, int *ifail, int *info);
void F_FUNC(ssbgst,SSBGST)(char *vect, char *uplo, int *n, int *ka, int *kb, float *ab, int *ldab, float *bb, int *ldbb, float *x, int *ldx, float *work, int *info);
void F_FUNC(ssbgv,SSBGV)(char *jobz, char *uplo, int *n, int *ka, int *kb, float *ab, int *ldab, float *bb, int *ldbb, float *w, float *z, int *ldz, float *work, int *info);
void F_FUNC(ssbgvd,SSBGVD)(char *jobz, char *uplo, int *n, int *ka, int *kb, float *ab, int *ldab, float *bb, int *ldbb, float *w, float *z, int *ldz, float *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(ssbgvx,SSBGVX)(char *jobz, char *range, char *uplo, int *n, int *ka, int *kb, float *ab, int *ldab, float *bb, int *ldbb, float *q, int *ldq, float *vl, float *vu, int *il, int *iu, float *abstol, int *m, float *w, float *z, int *ldz, float *work, int *iwork, int *ifail, int *info);
void F_FUNC(ssbtrd,SSBTRD)(char *vect, char *uplo, int *n, int *kd, float *ab, int *ldab, float *d, float *e, float *q, int *ldq, float *work, int *info);
void F_FUNC(ssfrk,SSFRK)(char *transr, char *uplo, char *trans, int *n, int *k, float *alpha, float *a, int *lda, float *beta, float *c);
void F_FUNC(sspcon,SSPCON)(char *uplo, int *n, float *ap, int *ipiv, float *anorm, float *rcond, float *work, int *iwork, int *info);
void F_FUNC(sspev,SSPEV)(char *jobz, char *uplo, int *n, float *ap, float *w, float *z, int *ldz, float *work, int *info);
void F_FUNC(sspevd,SSPEVD)(char *jobz, char *uplo, int *n, float *ap, float *w, float *z, int *ldz, float *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(sspevx,SSPEVX)(char *jobz, char *range, char *uplo, int *n, float *ap, float *vl, float *vu, int *il, int *iu, float *abstol, int *m, float *w, float *z, int *ldz, float *work, int *iwork, int *ifail, int *info);
void F_FUNC(sspgst,SSPGST)(int *itype, char *uplo, int *n, float *ap, float *bp, int *info);
void F_FUNC(sspgv,SSPGV)(int *itype, char *jobz, char *uplo, int *n, float *ap, float *bp, float *w, float *z, int *ldz, float *work, int *info);
void F_FUNC(sspgvd,SSPGVD)(int *itype, char *jobz, char *uplo, int *n, float *ap, float *bp, float *w, float *z, int *ldz, float *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(sspgvx,SSPGVX)(int *itype, char *jobz, char *range, char *uplo, int *n, float *ap, float *bp, float *vl, float *vu, int *il, int *iu, float *abstol, int *m, float *w, float *z, int *ldz, float *work, int *iwork, int *ifail, int *info);
void F_FUNC(ssprfs,SSPRFS)(char *uplo, int *n, int *nrhs, float *ap, float *afp, int *ipiv, float *b, int *ldb, float *x, int *ldx, float *ferr, float *berr, float *work, int *iwork, int *info);
void F_FUNC(sspsv,SSPSV)(char *uplo, int *n, int *nrhs, float *ap, int *ipiv, float *b, int *ldb, int *info);
void F_FUNC(sspsvx,SSPSVX)(char *fact, char *uplo, int *n, int *nrhs, float *ap, float *afp, int *ipiv, float *b, int *ldb, float *x, int *ldx, float *rcond, float *ferr, float *berr, float *work, int *iwork, int *info);
void F_FUNC(ssptrd,SSPTRD)(char *uplo, int *n, float *ap, float *d, float *e, float *tau, int *info);
void F_FUNC(ssptrf,SSPTRF)(char *uplo, int *n, float *ap, int *ipiv, int *info);
void F_FUNC(ssptri,SSPTRI)(char *uplo, int *n, float *ap, int *ipiv, float *work, int *info);
void F_FUNC(ssptrs,SSPTRS)(char *uplo, int *n, int *nrhs, float *ap, int *ipiv, float *b, int *ldb, int *info);
void F_FUNC(sstebz,SSTEBZ)(char *range, char *order, int *n, float *vl, float *vu, int *il, int *iu, float *abstol, float *d, float *e, int *m, int *nsplit, float *w, int *iblock, int *isplit, float *work, int *iwork, int *info);
void F_FUNC(sstedc,SSTEDC)(char *compz, int *n, float *d, float *e, float *z, int *ldz, float *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(sstegr,SSTEGR)(char *jobz, char *range, int *n, float *d, float *e, float *vl, float *vu, int *il, int *iu, float *abstol, int *m, float *w, float *z, int *ldz, int *isuppz, float *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(sstein,SSTEIN)(int *n, float *d, float *e, int *m, float *w, int *iblock, int *isplit, float *z, int *ldz, float *work, int *iwork, int *ifail, int *info);
void F_FUNC(sstemr,SSTEMR)(char *jobz, char *range, int *n, float *d, float *e, float *vl, float *vu, int *il, int *iu, int *m, float *w, float *z, int *ldz, int *nzc, int *isuppz, int *tryrac, float *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(ssteqr,SSTEQR)(char *compz, int *n, float *d, float *e, float *z, int *ldz, float *work, int *info);
void F_FUNC(ssterf,SSTERF)(int *n, float *d, float *e, int *info);
void F_FUNC(sstev,SSTEV)(char *jobz, int *n, float *d, float *e, float *z, int *ldz, float *work, int *info);
void F_FUNC(sstevd,SSTEVD)(char *jobz, int *n, float *d, float *e, float *z, int *ldz, float *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(sstevr,SSTEVR)(char *jobz, char *range, int *n, float *d, float *e, float *vl, float *vu, int *il, int *iu, float *abstol, int *m, float *w, float *z, int *ldz, int *isuppz, float *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(sstevx,SSTEVX)(char *jobz, char *range, int *n, float *d, float *e, float *vl, float *vu, int *il, int *iu, float *abstol, int *m, float *w, float *z, int *ldz, float *work, int *iwork, int *ifail, int *info);
void F_FUNC(ssycon,SSYCON)(char *uplo, int *n, float *a, int *lda, int *ipiv, float *anorm, float *rcond, float *work, int *iwork, int *info);
void F_FUNC(ssyconv,SSYCONV)(char *uplo, char *way, int *n, float *a, int *lda, int *ipiv, float *work, int *info);
void F_FUNC(ssyequb,SSYEQUB)(char *uplo, int *n, float *a, int *lda, float *s, float *scond, float *amax, float *work, int *info);
void F_FUNC(ssyev,SSYEV)(char *jobz, char *uplo, int *n, float *a, int *lda, float *w, float *work, int *lwork, int *info);
void F_FUNC(ssyevd,SSYEVD)(char *jobz, char *uplo, int *n, float *a, int *lda, float *w, float *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(ssyevr,SSYEVR)(char *jobz, char *range, char *uplo, int *n, float *a, int *lda, float *vl, float *vu, int *il, int *iu, float *abstol, int *m, float *w, float *z, int *ldz, int *isuppz, float *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(ssyevx,SSYEVX)(char *jobz, char *range, char *uplo, int *n, float *a, int *lda, float *vl, float *vu, int *il, int *iu, float *abstol, int *m, float *w, float *z, int *ldz, float *work, int *lwork, int *iwork, int *ifail, int *info);
void F_FUNC(ssygs2,SSYGS2)(int *itype, char *uplo, int *n, float *a, int *lda, float *b, int *ldb, int *info);
void F_FUNC(ssygst,SSYGST)(int *itype, char *uplo, int *n, float *a, int *lda, float *b, int *ldb, int *info);
void F_FUNC(ssygv,SSYGV)(int *itype, char *jobz, char *uplo, int *n, float *a, int *lda, float *b, int *ldb, float *w, float *work, int *lwork, int *info);
void F_FUNC(ssygvd,SSYGVD)(int *itype, char *jobz, char *uplo, int *n, float *a, int *lda, float *b, int *ldb, float *w, float *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(ssygvx,SSYGVX)(int *itype, char *jobz, char *range, char *uplo, int *n, float *a, int *lda, float *b, int *ldb, float *vl, float *vu, int *il, int *iu, float *abstol, int *m, float *w, float *z, int *ldz, float *work, int *lwork, int *iwork, int *ifail, int *info);
void F_FUNC(ssyrfs,SSYRFS)(char *uplo, int *n, int *nrhs, float *a, int *lda, float *af, int *ldaf, int *ipiv, float *b, int *ldb, float *x, int *ldx, float *ferr, float *berr, float *work, int *iwork, int *info);
void F_FUNC(ssysv,SSYSV)(char *uplo, int *n, int *nrhs, float *a, int *lda, int *ipiv, float *b, int *ldb, float *work, int *lwork, int *info);
void F_FUNC(ssysvx,SSYSVX)(char *fact, char *uplo, int *n, int *nrhs, float *a, int *lda, float *af, int *ldaf, int *ipiv, float *b, int *ldb, float *x, int *ldx, float *rcond, float *ferr, float *berr, float *work, int *lwork, int *iwork, int *info);
void F_FUNC(ssyswapr,SSYSWAPR)(char *uplo, int *n, float *a, int *lda, int *i1, int *i2);
void F_FUNC(ssytd2,SSYTD2)(char *uplo, int *n, float *a, int *lda, float *d, float *e, float *tau, int *info);
void F_FUNC(ssytf2,SSYTF2)(char *uplo, int *n, float *a, int *lda, int *ipiv, int *info);
void F_FUNC(ssytrd,SSYTRD)(char *uplo, int *n, float *a, int *lda, float *d, float *e, float *tau, float *work, int *lwork, int *info);
void F_FUNC(ssytrf,SSYTRF)(char *uplo, int *n, float *a, int *lda, int *ipiv, float *work, int *lwork, int *info);
void F_FUNC(ssytri,SSYTRI)(char *uplo, int *n, float *a, int *lda, int *ipiv, float *work, int *info);
void F_FUNC(ssytri2,SSYTRI2)(char *uplo, int *n, float *a, int *lda, int *ipiv, float *work, int *lwork, int *info);
void F_FUNC(ssytri2x,SSYTRI2X)(char *uplo, int *n, float *a, int *lda, int *ipiv, float *work, int *nb, int *info);
void F_FUNC(ssytrs,SSYTRS)(char *uplo, int *n, int *nrhs, float *a, int *lda, int *ipiv, float *b, int *ldb, int *info);
void F_FUNC(ssytrs2,SSYTRS2)(char *uplo, int *n, int *nrhs, float *a, int *lda, int *ipiv, float *b, int *ldb, float *work, int *info);
void F_FUNC(stbcon,STBCON)(char *norm, char *uplo, char *diag, int *n, int *kd, float *ab, int *ldab, float *rcond, float *work, int *iwork, int *info);
void F_FUNC(stbrfs,STBRFS)(char *uplo, char *trans, char *diag, int *n, int *kd, int *nrhs, float *ab, int *ldab, float *b, int *ldb, float *x, int *ldx, float *ferr, float *berr, float *work, int *iwork, int *info);
void F_FUNC(stbtrs,STBTRS)(char *uplo, char *trans, char *diag, int *n, int *kd, int *nrhs, float *ab, int *ldab, float *b, int *ldb, int *info);
void F_FUNC(stfsm,STFSM)(char *transr, char *side, char *uplo, char *trans, char *diag, int *m, int *n, float *alpha, float *a, float *b, int *ldb);
void F_FUNC(stftri,STFTRI)(char *transr, char *uplo, char *diag, int *n, float *a, int *info);
void F_FUNC(stfttp,STFTTP)(char *transr, char *uplo, int *n, float *arf, float *ap, int *info);
void F_FUNC(stfttr,STFTTR)(char *transr, char *uplo, int *n, float *arf, float *a, int *lda, int *info);
void F_FUNC(stgevc,STGEVC)(char *side, char *howmny, int *select, int *n, float *s, int *lds, float *p, int *ldp, float *vl, int *ldvl, float *vr, int *ldvr, int *mm, int *m, float *work, int *info);
void F_FUNC(stgex2,STGEX2)(int *wantq, int *wantz, int *n, float *a, int *lda, float *b, int *ldb, float *q, int *ldq, float *z, int *ldz, int *j1, int *n1, int *n2, float *work, int *lwork, int *info);
void F_FUNC(stgexc,STGEXC)(int *wantq, int *wantz, int *n, float *a, int *lda, float *b, int *ldb, float *q, int *ldq, float *z, int *ldz, int *ifst, int *ilst, float *work, int *lwork, int *info);
void F_FUNC(stgsen,STGSEN)(int *ijob, int *wantq, int *wantz, int *select, int *n, float *a, int *lda, float *b, int *ldb, float *alphar, float *alphai, float *beta, float *q, int *ldq, float *z, int *ldz, int *m, float *pl, float *pr, float *dif, float *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(stgsja,STGSJA)(char *jobu, char *jobv, char *jobq, int *m, int *p, int *n, int *k, int *l, float *a, int *lda, float *b, int *ldb, float *tola, float *tolb, float *alpha, float *beta, float *u, int *ldu, float *v, int *ldv, float *q, int *ldq, float *work, int *ncycle, int *info);
void F_FUNC(stgsna,STGSNA)(char *job, char *howmny, int *select, int *n, float *a, int *lda, float *b, int *ldb, float *vl, int *ldvl, float *vr, int *ldvr, float *s, float *dif, int *mm, int *m, float *work, int *lwork, int *iwork, int *info);
void F_FUNC(stgsy2,STGSY2)(char *trans, int *ijob, int *m, int *n, float *a, int *lda, float *b, int *ldb, float *c, int *ldc, float *d, int *ldd, float *e, int *lde, float *f, int *ldf, float *scale, float *rdsum, float *rdscal, int *iwork, int *pq, int *info);
void F_FUNC(stgsyl,STGSYL)(char *trans, int *ijob, int *m, int *n, float *a, int *lda, float *b, int *ldb, float *c, int *ldc, float *d, int *ldd, float *e, int *lde, float *f, int *ldf, float *scale, float *dif, float *work, int *lwork, int *iwork, int *info);
void F_FUNC(stpcon,STPCON)(char *norm, char *uplo, char *diag, int *n, float *ap, float *rcond, float *work, int *iwork, int *info);
void F_FUNC(stpmqrt,STPMQRT)(char *side, char *trans, int *m, int *n, int *k, int *l, int *nb, float *v, int *ldv, float *t, int *ldt, float *a, int *lda, float *b, int *ldb, float *work, int *info);
void F_FUNC(stpqrt,STPQRT)(int *m, int *n, int *l, int *nb, float *a, int *lda, float *b, int *ldb, float *t, int *ldt, float *work, int *info);
void F_FUNC(stpqrt2,STPQRT2)(int *m, int *n, int *l, float *a, int *lda, float *b, int *ldb, float *t, int *ldt, int *info);
void F_FUNC(stprfb,STPRFB)(char *side, char *trans, char *direct, char *storev, int *m, int *n, int *k, int *l, float *v, int *ldv, float *t, int *ldt, float *a, int *lda, float *b, int *ldb, float *work, int *ldwork);
void F_FUNC(stprfs,STPRFS)(char *uplo, char *trans, char *diag, int *n, int *nrhs, float *ap, float *b, int *ldb, float *x, int *ldx, float *ferr, float *berr, float *work, int *iwork, int *info);
void F_FUNC(stptri,STPTRI)(char *uplo, char *diag, int *n, float *ap, int *info);
void F_FUNC(stptrs,STPTRS)(char *uplo, char *trans, char *diag, int *n, int *nrhs, float *ap, float *b, int *ldb, int *info);
void F_FUNC(stpttf,STPTTF)(char *transr, char *uplo, int *n, float *ap, float *arf, int *info);
void F_FUNC(stpttr,STPTTR)(char *uplo, int *n, float *ap, float *a, int *lda, int *info);
void F_FUNC(strcon,STRCON)(char *norm, char *uplo, char *diag, int *n, float *a, int *lda, float *rcond, float *work, int *iwork, int *info);
void F_FUNC(strevc,STREVC)(char *side, char *howmny, int *select, int *n, float *t, int *ldt, float *vl, int *ldvl, float *vr, int *ldvr, int *mm, int *m, float *work, int *info);
void F_FUNC(strexc,STREXC)(char *compq, int *n, float *t, int *ldt, float *q, int *ldq, int *ifst, int *ilst, float *work, int *info);
void F_FUNC(strrfs,STRRFS)(char *uplo, char *trans, char *diag, int *n, int *nrhs, float *a, int *lda, float *b, int *ldb, float *x, int *ldx, float *ferr, float *berr, float *work, int *iwork, int *info);
void F_FUNC(strsen,STRSEN)(char *job, char *compq, int *select, int *n, float *t, int *ldt, float *q, int *ldq, float *wr, float *wi, int *m, float *s, float *sep, float *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(strsna,STRSNA)(char *job, char *howmny, int *select, int *n, float *t, int *ldt, float *vl, int *ldvl, float *vr, int *ldvr, float *s, float *sep, int *mm, int *m, float *work, int *ldwork, int *iwork, int *info);
void F_FUNC(strsyl,STRSYL)(char *trana, char *tranb, int *isgn, int *m, int *n, float *a, int *lda, float *b, int *ldb, float *c, int *ldc, float *scale, int *info);
void F_FUNC(strti2,STRTI2)(char *uplo, char *diag, int *n, float *a, int *lda, int *info);
void F_FUNC(strtri,STRTRI)(char *uplo, char *diag, int *n, float *a, int *lda, int *info);
void F_FUNC(strtrs,STRTRS)(char *uplo, char *trans, char *diag, int *n, int *nrhs, float *a, int *lda, float *b, int *ldb, int *info);
void F_FUNC(strttf,STRTTF)(char *transr, char *uplo, int *n, float *a, int *lda, float *arf, int *info);
void F_FUNC(strttp,STRTTP)(char *uplo, int *n, float *a, int *lda, float *ap, int *info);
void F_FUNC(stzrzf,STZRZF)(int *m, int *n, float *a, int *lda, float *tau, float *work, int *lwork, int *info);
void F_FUNC(xerbla_array,XERBLA_ARRAY)(char *srname_array, int *srname_len, int *info);
void F_FUNC(zbbcsd,ZBBCSD)(char *jobu1, char *jobu2, char *jobv1t, char *jobv2t, char *trans, int *m, int *p, int *q, double *theta, double *phi, npy_complex128 *u1, int *ldu1, npy_complex128 *u2, int *ldu2, npy_complex128 *v1t, int *ldv1t, npy_complex128 *v2t, int *ldv2t, double *b11d, double *b11e, double *b12d, double *b12e, double *b21d, double *b21e, double *b22d, double *b22e, double *rwork, int *lrwork, int *info);
void F_FUNC(zbdsqr,ZBDSQR)(char *uplo, int *n, int *ncvt, int *nru, int *ncc, double *d, double *e, npy_complex128 *vt, int *ldvt, npy_complex128 *u, int *ldu, npy_complex128 *c, int *ldc, double *rwork, int *info);
void F_FUNC(zcgesv,ZCGESV)(int *n, int *nrhs, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, npy_complex128 *work, npy_complex64 *swork, double *rwork, int *iter, int *info);
void F_FUNC(zcposv,ZCPOSV)(char *uplo, int *n, int *nrhs, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, npy_complex128 *work, npy_complex64 *swork, double *rwork, int *iter, int *info);
void F_FUNC(zdrscl,ZDRSCL)(int *n, double *sa, npy_complex128 *sx, int *incx);
void F_FUNC(zgbbrd,ZGBBRD)(char *vect, int *m, int *n, int *ncc, int *kl, int *ku, npy_complex128 *ab, int *ldab, double *d, double *e, npy_complex128 *q, int *ldq, npy_complex128 *pt, int *ldpt, npy_complex128 *c, int *ldc, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zgbcon,ZGBCON)(char *norm, int *n, int *kl, int *ku, npy_complex128 *ab, int *ldab, int *ipiv, double *anorm, double *rcond, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zgbequ,ZGBEQU)(int *m, int *n, int *kl, int *ku, npy_complex128 *ab, int *ldab, double *r, double *c, double *rowcnd, double *colcnd, double *amax, int *info);
void F_FUNC(zgbequb,ZGBEQUB)(int *m, int *n, int *kl, int *ku, npy_complex128 *ab, int *ldab, double *r, double *c, double *rowcnd, double *colcnd, double *amax, int *info);
void F_FUNC(zgbrfs,ZGBRFS)(char *trans, int *n, int *kl, int *ku, int *nrhs, npy_complex128 *ab, int *ldab, npy_complex128 *afb, int *ldafb, int *ipiv, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zgbsv,ZGBSV)(int *n, int *kl, int *ku, int *nrhs, npy_complex128 *ab, int *ldab, int *ipiv, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zgbsvx,ZGBSVX)(char *fact, char *trans, int *n, int *kl, int *ku, int *nrhs, npy_complex128 *ab, int *ldab, npy_complex128 *afb, int *ldafb, int *ipiv, char *equed, double *r, double *c, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *rcond, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zgbtf2,ZGBTF2)(int *m, int *n, int *kl, int *ku, npy_complex128 *ab, int *ldab, int *ipiv, int *info);
void F_FUNC(zgbtrf,ZGBTRF)(int *m, int *n, int *kl, int *ku, npy_complex128 *ab, int *ldab, int *ipiv, int *info);
void F_FUNC(zgbtrs,ZGBTRS)(char *trans, int *n, int *kl, int *ku, int *nrhs, npy_complex128 *ab, int *ldab, int *ipiv, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zgebak,ZGEBAK)(char *job, char *side, int *n, int *ilo, int *ihi, double *scale, int *m, npy_complex128 *v, int *ldv, int *info);
void F_FUNC(zgebal,ZGEBAL)(char *job, int *n, npy_complex128 *a, int *lda, int *ilo, int *ihi, double *scale, int *info);
void F_FUNC(zgebd2,ZGEBD2)(int *m, int *n, npy_complex128 *a, int *lda, double *d, double *e, npy_complex128 *tauq, npy_complex128 *taup, npy_complex128 *work, int *info);
void F_FUNC(zgebrd,ZGEBRD)(int *m, int *n, npy_complex128 *a, int *lda, double *d, double *e, npy_complex128 *tauq, npy_complex128 *taup, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zgecon,ZGECON)(char *norm, int *n, npy_complex128 *a, int *lda, double *anorm, double *rcond, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zgeequ,ZGEEQU)(int *m, int *n, npy_complex128 *a, int *lda, double *r, double *c, double *rowcnd, double *colcnd, double *amax, int *info);
void F_FUNC(zgeequb,ZGEEQUB)(int *m, int *n, npy_complex128 *a, int *lda, double *r, double *c, double *rowcnd, double *colcnd, double *amax, int *info);
void F_FUNC(zgees,ZGEES)(char *jobvs, char *sort, _zselect1 *select, int *n, npy_complex128 *a, int *lda, int *sdim, npy_complex128 *w, npy_complex128 *vs, int *ldvs, npy_complex128 *work, int *lwork, double *rwork, int *bwork, int *info);
void F_FUNC(zgeesx,ZGEESX)(char *jobvs, char *sort, _zselect1 *select, char *sense, int *n, npy_complex128 *a, int *lda, int *sdim, npy_complex128 *w, npy_complex128 *vs, int *ldvs, double *rconde, double *rcondv, npy_complex128 *work, int *lwork, double *rwork, int *bwork, int *info);
void F_FUNC(zgeev,ZGEEV)(char *jobvl, char *jobvr, int *n, npy_complex128 *a, int *lda, npy_complex128 *w, npy_complex128 *vl, int *ldvl, npy_complex128 *vr, int *ldvr, npy_complex128 *work, int *lwork, double *rwork, int *info);
void F_FUNC(zgeevx,ZGEEVX)(char *balanc, char *jobvl, char *jobvr, char *sense, int *n, npy_complex128 *a, int *lda, npy_complex128 *w, npy_complex128 *vl, int *ldvl, npy_complex128 *vr, int *ldvr, int *ilo, int *ihi, double *scale, double *abnrm, double *rconde, double *rcondv, npy_complex128 *work, int *lwork, double *rwork, int *info);
void F_FUNC(zgehd2,ZGEHD2)(int *n, int *ilo, int *ihi, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *info);
void F_FUNC(zgehrd,ZGEHRD)(int *n, int *ilo, int *ihi, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zgelq2,ZGELQ2)(int *m, int *n, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *info);
void F_FUNC(zgelqf,ZGELQF)(int *m, int *n, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zgels,ZGELS)(char *trans, int *m, int *n, int *nrhs, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zgelsd,ZGELSD)(int *m, int *n, int *nrhs, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, double *s, double *rcond, int *rank, npy_complex128 *work, int *lwork, double *rwork, int *iwork, int *info);
void F_FUNC(zgelss,ZGELSS)(int *m, int *n, int *nrhs, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, double *s, double *rcond, int *rank, npy_complex128 *work, int *lwork, double *rwork, int *info);
void F_FUNC(zgelsy,ZGELSY)(int *m, int *n, int *nrhs, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, int *jpvt, double *rcond, int *rank, npy_complex128 *work, int *lwork, double *rwork, int *info);
void F_FUNC(zgemqrt,ZGEMQRT)(char *side, char *trans, int *m, int *n, int *k, int *nb, npy_complex128 *v, int *ldv, npy_complex128 *t, int *ldt, npy_complex128 *c, int *ldc, npy_complex128 *work, int *info);
void F_FUNC(zgeql2,ZGEQL2)(int *m, int *n, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *info);
void F_FUNC(zgeqlf,ZGEQLF)(int *m, int *n, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zgeqp3,ZGEQP3)(int *m, int *n, npy_complex128 *a, int *lda, int *jpvt, npy_complex128 *tau, npy_complex128 *work, int *lwork, double *rwork, int *info);
void F_FUNC(zgeqr2,ZGEQR2)(int *m, int *n, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *info);
void F_FUNC(zgeqr2p,ZGEQR2P)(int *m, int *n, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *info);
void F_FUNC(zgeqrf,ZGEQRF)(int *m, int *n, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zgeqrfp,ZGEQRFP)(int *m, int *n, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zgeqrt,ZGEQRT)(int *m, int *n, int *nb, npy_complex128 *a, int *lda, npy_complex128 *t, int *ldt, npy_complex128 *work, int *info);
void F_FUNC(zgeqrt2,ZGEQRT2)(int *m, int *n, npy_complex128 *a, int *lda, npy_complex128 *t, int *ldt, int *info);
void F_FUNC(zgeqrt3,ZGEQRT3)(int *m, int *n, npy_complex128 *a, int *lda, npy_complex128 *t, int *ldt, int *info);
void F_FUNC(zgerfs,ZGERFS)(char *trans, int *n, int *nrhs, npy_complex128 *a, int *lda, npy_complex128 *af, int *ldaf, int *ipiv, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zgerq2,ZGERQ2)(int *m, int *n, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *info);
void F_FUNC(zgerqf,ZGERQF)(int *m, int *n, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zgesc2,ZGESC2)(int *n, npy_complex128 *a, int *lda, npy_complex128 *rhs, int *ipiv, int *jpiv, double *scale);
void F_FUNC(zgesdd,ZGESDD)(char *jobz, int *m, int *n, npy_complex128 *a, int *lda, double *s, npy_complex128 *u, int *ldu, npy_complex128 *vt, int *ldvt, npy_complex128 *work, int *lwork, double *rwork, int *iwork, int *info);
void F_FUNC(zgesv,ZGESV)(int *n, int *nrhs, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zgesvd,ZGESVD)(char *jobu, char *jobvt, int *m, int *n, npy_complex128 *a, int *lda, double *s, npy_complex128 *u, int *ldu, npy_complex128 *vt, int *ldvt, npy_complex128 *work, int *lwork, double *rwork, int *info);
void F_FUNC(zgesvx,ZGESVX)(char *fact, char *trans, int *n, int *nrhs, npy_complex128 *a, int *lda, npy_complex128 *af, int *ldaf, int *ipiv, char *equed, double *r, double *c, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *rcond, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zgetc2,ZGETC2)(int *n, npy_complex128 *a, int *lda, int *ipiv, int *jpiv, int *info);
void F_FUNC(zgetf2,ZGETF2)(int *m, int *n, npy_complex128 *a, int *lda, int *ipiv, int *info);
void F_FUNC(zgetrf,ZGETRF)(int *m, int *n, npy_complex128 *a, int *lda, int *ipiv, int *info);
void F_FUNC(zgetri,ZGETRI)(int *n, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zgetrs,ZGETRS)(char *trans, int *n, int *nrhs, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zggbak,ZGGBAK)(char *job, char *side, int *n, int *ilo, int *ihi, double *lscale, double *rscale, int *m, npy_complex128 *v, int *ldv, int *info);
void F_FUNC(zggbal,ZGGBAL)(char *job, int *n, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, int *ilo, int *ihi, double *lscale, double *rscale, double *work, int *info);
void F_FUNC(zgges,ZGGES)(char *jobvsl, char *jobvsr, char *sort, _zselect2 *selctg, int *n, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, int *sdim, npy_complex128 *alpha, npy_complex128 *beta, npy_complex128 *vsl, int *ldvsl, npy_complex128 *vsr, int *ldvsr, npy_complex128 *work, int *lwork, double *rwork, int *bwork, int *info);
void F_FUNC(zggesx,ZGGESX)(char *jobvsl, char *jobvsr, char *sort, _zselect2 *selctg, char *sense, int *n, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, int *sdim, npy_complex128 *alpha, npy_complex128 *beta, npy_complex128 *vsl, int *ldvsl, npy_complex128 *vsr, int *ldvsr, double *rconde, double *rcondv, npy_complex128 *work, int *lwork, double *rwork, int *iwork, int *liwork, int *bwork, int *info);
void F_FUNC(zggev,ZGGEV)(char *jobvl, char *jobvr, int *n, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *alpha, npy_complex128 *beta, npy_complex128 *vl, int *ldvl, npy_complex128 *vr, int *ldvr, npy_complex128 *work, int *lwork, double *rwork, int *info);
void F_FUNC(zggevx,ZGGEVX)(char *balanc, char *jobvl, char *jobvr, char *sense, int *n, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *alpha, npy_complex128 *beta, npy_complex128 *vl, int *ldvl, npy_complex128 *vr, int *ldvr, int *ilo, int *ihi, double *lscale, double *rscale, double *abnrm, double *bbnrm, double *rconde, double *rcondv, npy_complex128 *work, int *lwork, double *rwork, int *iwork, int *bwork, int *info);
void F_FUNC(zggglm,ZGGGLM)(int *n, int *m, int *p, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *d, npy_complex128 *x, npy_complex128 *y, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zgghrd,ZGGHRD)(char *compq, char *compz, int *n, int *ilo, int *ihi, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *q, int *ldq, npy_complex128 *z, int *ldz, int *info);
void F_FUNC(zgglse,ZGGLSE)(int *m, int *n, int *p, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *c, npy_complex128 *d, npy_complex128 *x, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zggqrf,ZGGQRF)(int *n, int *m, int *p, npy_complex128 *a, int *lda, npy_complex128 *taua, npy_complex128 *b, int *ldb, npy_complex128 *taub, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zggrqf,ZGGRQF)(int *m, int *p, int *n, npy_complex128 *a, int *lda, npy_complex128 *taua, npy_complex128 *b, int *ldb, npy_complex128 *taub, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zgtcon,ZGTCON)(char *norm, int *n, npy_complex128 *dl, npy_complex128 *d, npy_complex128 *du, npy_complex128 *du2, int *ipiv, double *anorm, double *rcond, npy_complex128 *work, int *info);
void F_FUNC(zgtrfs,ZGTRFS)(char *trans, int *n, int *nrhs, npy_complex128 *dl, npy_complex128 *d, npy_complex128 *du, npy_complex128 *dlf, npy_complex128 *df, npy_complex128 *duf, npy_complex128 *du2, int *ipiv, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zgtsv,ZGTSV)(int *n, int *nrhs, npy_complex128 *dl, npy_complex128 *d, npy_complex128 *du, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zgtsvx,ZGTSVX)(char *fact, char *trans, int *n, int *nrhs, npy_complex128 *dl, npy_complex128 *d, npy_complex128 *du, npy_complex128 *dlf, npy_complex128 *df, npy_complex128 *duf, npy_complex128 *du2, int *ipiv, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *rcond, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zgttrf,ZGTTRF)(int *n, npy_complex128 *dl, npy_complex128 *d, npy_complex128 *du, npy_complex128 *du2, int *ipiv, int *info);
void F_FUNC(zgttrs,ZGTTRS)(char *trans, int *n, int *nrhs, npy_complex128 *dl, npy_complex128 *d, npy_complex128 *du, npy_complex128 *du2, int *ipiv, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zgtts2,ZGTTS2)(int *itrans, int *n, int *nrhs, npy_complex128 *dl, npy_complex128 *d, npy_complex128 *du, npy_complex128 *du2, int *ipiv, npy_complex128 *b, int *ldb);
void F_FUNC(zhbev,ZHBEV)(char *jobz, char *uplo, int *n, int *kd, npy_complex128 *ab, int *ldab, double *w, npy_complex128 *z, int *ldz, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zhbevd,ZHBEVD)(char *jobz, char *uplo, int *n, int *kd, npy_complex128 *ab, int *ldab, double *w, npy_complex128 *z, int *ldz, npy_complex128 *work, int *lwork, double *rwork, int *lrwork, int *iwork, int *liwork, int *info);
void F_FUNC(zhbevx,ZHBEVX)(char *jobz, char *range, char *uplo, int *n, int *kd, npy_complex128 *ab, int *ldab, npy_complex128 *q, int *ldq, double *vl, double *vu, int *il, int *iu, double *abstol, int *m, double *w, npy_complex128 *z, int *ldz, npy_complex128 *work, double *rwork, int *iwork, int *ifail, int *info);
void F_FUNC(zhbgst,ZHBGST)(char *vect, char *uplo, int *n, int *ka, int *kb, npy_complex128 *ab, int *ldab, npy_complex128 *bb, int *ldbb, npy_complex128 *x, int *ldx, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zhbgv,ZHBGV)(char *jobz, char *uplo, int *n, int *ka, int *kb, npy_complex128 *ab, int *ldab, npy_complex128 *bb, int *ldbb, double *w, npy_complex128 *z, int *ldz, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zhbgvd,ZHBGVD)(char *jobz, char *uplo, int *n, int *ka, int *kb, npy_complex128 *ab, int *ldab, npy_complex128 *bb, int *ldbb, double *w, npy_complex128 *z, int *ldz, npy_complex128 *work, int *lwork, double *rwork, int *lrwork, int *iwork, int *liwork, int *info);
void F_FUNC(zhbgvx,ZHBGVX)(char *jobz, char *range, char *uplo, int *n, int *ka, int *kb, npy_complex128 *ab, int *ldab, npy_complex128 *bb, int *ldbb, npy_complex128 *q, int *ldq, double *vl, double *vu, int *il, int *iu, double *abstol, int *m, double *w, npy_complex128 *z, int *ldz, npy_complex128 *work, double *rwork, int *iwork, int *ifail, int *info);
void F_FUNC(zhbtrd,ZHBTRD)(char *vect, char *uplo, int *n, int *kd, npy_complex128 *ab, int *ldab, double *d, double *e, npy_complex128 *q, int *ldq, npy_complex128 *work, int *info);
void F_FUNC(zhecon,ZHECON)(char *uplo, int *n, npy_complex128 *a, int *lda, int *ipiv, double *anorm, double *rcond, npy_complex128 *work, int *info);
void F_FUNC(zheequb,ZHEEQUB)(char *uplo, int *n, npy_complex128 *a, int *lda, double *s, double *scond, double *amax, npy_complex128 *work, int *info);
void F_FUNC(zheev,ZHEEV)(char *jobz, char *uplo, int *n, npy_complex128 *a, int *lda, double *w, npy_complex128 *work, int *lwork, double *rwork, int *info);
void F_FUNC(zheevd,ZHEEVD)(char *jobz, char *uplo, int *n, npy_complex128 *a, int *lda, double *w, npy_complex128 *work, int *lwork, double *rwork, int *lrwork, int *iwork, int *liwork, int *info);
void F_FUNC(zheevr,ZHEEVR)(char *jobz, char *range, char *uplo, int *n, npy_complex128 *a, int *lda, double *vl, double *vu, int *il, int *iu, double *abstol, int *m, double *w, npy_complex128 *z, int *ldz, int *isuppz, npy_complex128 *work, int *lwork, double *rwork, int *lrwork, int *iwork, int *liwork, int *info);
void F_FUNC(zheevx,ZHEEVX)(char *jobz, char *range, char *uplo, int *n, npy_complex128 *a, int *lda, double *vl, double *vu, int *il, int *iu, double *abstol, int *m, double *w, npy_complex128 *z, int *ldz, npy_complex128 *work, int *lwork, double *rwork, int *iwork, int *ifail, int *info);
void F_FUNC(zhegs2,ZHEGS2)(int *itype, char *uplo, int *n, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zhegst,ZHEGST)(int *itype, char *uplo, int *n, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zhegv,ZHEGV)(int *itype, char *jobz, char *uplo, int *n, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, double *w, npy_complex128 *work, int *lwork, double *rwork, int *info);
void F_FUNC(zhegvd,ZHEGVD)(int *itype, char *jobz, char *uplo, int *n, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, double *w, npy_complex128 *work, int *lwork, double *rwork, int *lrwork, int *iwork, int *liwork, int *info);
void F_FUNC(zhegvx,ZHEGVX)(int *itype, char *jobz, char *range, char *uplo, int *n, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, double *vl, double *vu, int *il, int *iu, double *abstol, int *m, double *w, npy_complex128 *z, int *ldz, npy_complex128 *work, int *lwork, double *rwork, int *iwork, int *ifail, int *info);
void F_FUNC(zherfs,ZHERFS)(char *uplo, int *n, int *nrhs, npy_complex128 *a, int *lda, npy_complex128 *af, int *ldaf, int *ipiv, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zhesv,ZHESV)(char *uplo, int *n, int *nrhs, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *b, int *ldb, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zhesvx,ZHESVX)(char *fact, char *uplo, int *n, int *nrhs, npy_complex128 *a, int *lda, npy_complex128 *af, int *ldaf, int *ipiv, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *rcond, double *ferr, double *berr, npy_complex128 *work, int *lwork, double *rwork, int *info);
void F_FUNC(zheswapr,ZHESWAPR)(char *uplo, int *n, npy_complex128 *a, int *lda, int *i1, int *i2);
void F_FUNC(zhetd2,ZHETD2)(char *uplo, int *n, npy_complex128 *a, int *lda, double *d, double *e, npy_complex128 *tau, int *info);
void F_FUNC(zhetf2,ZHETF2)(char *uplo, int *n, npy_complex128 *a, int *lda, int *ipiv, int *info);
void F_FUNC(zhetrd,ZHETRD)(char *uplo, int *n, npy_complex128 *a, int *lda, double *d, double *e, npy_complex128 *tau, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zhetrf,ZHETRF)(char *uplo, int *n, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zhetri,ZHETRI)(char *uplo, int *n, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *work, int *info);
void F_FUNC(zhetri2,ZHETRI2)(char *uplo, int *n, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zhetri2x,ZHETRI2X)(char *uplo, int *n, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *work, int *nb, int *info);
void F_FUNC(zhetrs,ZHETRS)(char *uplo, int *n, int *nrhs, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zhetrs2,ZHETRS2)(char *uplo, int *n, int *nrhs, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *b, int *ldb, npy_complex128 *work, int *info);
void F_FUNC(zhfrk,ZHFRK)(char *transr, char *uplo, char *trans, int *n, int *k, double *alpha, npy_complex128 *a, int *lda, double *beta, npy_complex128 *c);
void F_FUNC(zhgeqz,ZHGEQZ)(char *job, char *compq, char *compz, int *n, int *ilo, int *ihi, npy_complex128 *h, int *ldh, npy_complex128 *t, int *ldt, npy_complex128 *alpha, npy_complex128 *beta, npy_complex128 *q, int *ldq, npy_complex128 *z, int *ldz, npy_complex128 *work, int *lwork, double *rwork, int *info);
void F_FUNC(zhpcon,ZHPCON)(char *uplo, int *n, npy_complex128 *ap, int *ipiv, double *anorm, double *rcond, npy_complex128 *work, int *info);
void F_FUNC(zhpev,ZHPEV)(char *jobz, char *uplo, int *n, npy_complex128 *ap, double *w, npy_complex128 *z, int *ldz, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zhpevd,ZHPEVD)(char *jobz, char *uplo, int *n, npy_complex128 *ap, double *w, npy_complex128 *z, int *ldz, npy_complex128 *work, int *lwork, double *rwork, int *lrwork, int *iwork, int *liwork, int *info);
void F_FUNC(zhpevx,ZHPEVX)(char *jobz, char *range, char *uplo, int *n, npy_complex128 *ap, double *vl, double *vu, int *il, int *iu, double *abstol, int *m, double *w, npy_complex128 *z, int *ldz, npy_complex128 *work, double *rwork, int *iwork, int *ifail, int *info);
void F_FUNC(zhpgst,ZHPGST)(int *itype, char *uplo, int *n, npy_complex128 *ap, npy_complex128 *bp, int *info);
void F_FUNC(zhpgv,ZHPGV)(int *itype, char *jobz, char *uplo, int *n, npy_complex128 *ap, npy_complex128 *bp, double *w, npy_complex128 *z, int *ldz, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zhpgvd,ZHPGVD)(int *itype, char *jobz, char *uplo, int *n, npy_complex128 *ap, npy_complex128 *bp, double *w, npy_complex128 *z, int *ldz, npy_complex128 *work, int *lwork, double *rwork, int *lrwork, int *iwork, int *liwork, int *info);
void F_FUNC(zhpgvx,ZHPGVX)(int *itype, char *jobz, char *range, char *uplo, int *n, npy_complex128 *ap, npy_complex128 *bp, double *vl, double *vu, int *il, int *iu, double *abstol, int *m, double *w, npy_complex128 *z, int *ldz, npy_complex128 *work, double *rwork, int *iwork, int *ifail, int *info);
void F_FUNC(zhprfs,ZHPRFS)(char *uplo, int *n, int *nrhs, npy_complex128 *ap, npy_complex128 *afp, int *ipiv, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zhpsv,ZHPSV)(char *uplo, int *n, int *nrhs, npy_complex128 *ap, int *ipiv, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zhpsvx,ZHPSVX)(char *fact, char *uplo, int *n, int *nrhs, npy_complex128 *ap, npy_complex128 *afp, int *ipiv, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *rcond, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zhptrd,ZHPTRD)(char *uplo, int *n, npy_complex128 *ap, double *d, double *e, npy_complex128 *tau, int *info);
void F_FUNC(zhptrf,ZHPTRF)(char *uplo, int *n, npy_complex128 *ap, int *ipiv, int *info);
void F_FUNC(zhptri,ZHPTRI)(char *uplo, int *n, npy_complex128 *ap, int *ipiv, npy_complex128 *work, int *info);
void F_FUNC(zhptrs,ZHPTRS)(char *uplo, int *n, int *nrhs, npy_complex128 *ap, int *ipiv, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zhsein,ZHSEIN)(char *side, char *eigsrc, char *initv, int *select, int *n, npy_complex128 *h, int *ldh, npy_complex128 *w, npy_complex128 *vl, int *ldvl, npy_complex128 *vr, int *ldvr, int *mm, int *m, npy_complex128 *work, double *rwork, int *ifaill, int *ifailr, int *info);
void F_FUNC(zhseqr,ZHSEQR)(char *job, char *compz, int *n, int *ilo, int *ihi, npy_complex128 *h, int *ldh, npy_complex128 *w, npy_complex128 *z, int *ldz, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zlabrd,ZLABRD)(int *m, int *n, int *nb, npy_complex128 *a, int *lda, double *d, double *e, npy_complex128 *tauq, npy_complex128 *taup, npy_complex128 *x, int *ldx, npy_complex128 *y, int *ldy);
void F_FUNC(zlacgv,ZLACGV)(int *n, npy_complex128 *x, int *incx);
void F_FUNC(zlacn2,ZLACN2)(int *n, npy_complex128 *v, npy_complex128 *x, double *est, int *kase, int *isave);
void F_FUNC(zlacon,ZLACON)(int *n, npy_complex128 *v, npy_complex128 *x, double *est, int *kase);
void F_FUNC(zlacp2,ZLACP2)(char *uplo, int *m, int *n, double *a, int *lda, npy_complex128 *b, int *ldb);
void F_FUNC(zlacpy,ZLACPY)(char *uplo, int *m, int *n, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb);
void F_FUNC(zlacrm,ZLACRM)(int *m, int *n, npy_complex128 *a, int *lda, double *b, int *ldb, npy_complex128 *c, int *ldc, double *rwork);
void F_FUNC(zlacrt,ZLACRT)(int *n, npy_complex128 *cx, int *incx, npy_complex128 *cy, int *incy, npy_complex128 *c, npy_complex128 *s);
void F_FUNC(zlaed0,ZLAED0)(int *qsiz, int *n, double *d, double *e, npy_complex128 *q, int *ldq, npy_complex128 *qstore, int *ldqs, double *rwork, int *iwork, int *info);
void F_FUNC(zlaed7,ZLAED7)(int *n, int *cutpnt, int *qsiz, int *tlvls, int *curlvl, int *curpbm, double *d, npy_complex128 *q, int *ldq, double *rho, int *indxq, double *qstore, int *qptr, int *prmptr, int *perm, int *givptr, int *givcol, double *givnum, npy_complex128 *work, double *rwork, int *iwork, int *info);
void F_FUNC(zlaed8,ZLAED8)(int *k, int *n, int *qsiz, npy_complex128 *q, int *ldq, double *d, double *rho, int *cutpnt, double *z, double *dlamda, npy_complex128 *q2, int *ldq2, double *w, int *indxp, int *indx, int *indxq, int *perm, int *givptr, int *givcol, double *givnum, int *info);
void F_FUNC(zlaein,ZLAEIN)(int *rightv, int *noinit, int *n, npy_complex128 *h, int *ldh, npy_complex128 *w, npy_complex128 *v, npy_complex128 *b, int *ldb, double *rwork, double *eps3, double *smlnum, int *info);
void F_FUNC(zlaesy,ZLAESY)(npy_complex128 *a, npy_complex128 *b, npy_complex128 *c, npy_complex128 *rt1, npy_complex128 *rt2, npy_complex128 *evscal, npy_complex128 *cs1, npy_complex128 *sn1);
void F_FUNC(zlaev2,ZLAEV2)(npy_complex128 *a, npy_complex128 *b, npy_complex128 *c, double *rt1, double *rt2, double *cs1, npy_complex128 *sn1);
void F_FUNC(zlag2c,ZLAG2C)(int *m, int *n, npy_complex128 *a, int *lda, npy_complex64 *sa, int *ldsa, int *info);
void F_FUNC(zlags2,ZLAGS2)(int *upper, double *a1, npy_complex128 *a2, double *a3, double *b1, npy_complex128 *b2, double *b3, double *csu, npy_complex128 *snu, double *csv, npy_complex128 *snv, double *csq, npy_complex128 *snq);
void F_FUNC(zlagtm,ZLAGTM)(char *trans, int *n, int *nrhs, double *alpha, npy_complex128 *dl, npy_complex128 *d, npy_complex128 *du, npy_complex128 *x, int *ldx, double *beta, npy_complex128 *b, int *ldb);
void F_FUNC(zlahef,ZLAHEF)(char *uplo, int *n, int *nb, int *kb, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *w, int *ldw, int *info);
void F_FUNC(zlahqr,ZLAHQR)(int *wantt, int *wantz, int *n, int *ilo, int *ihi, npy_complex128 *h, int *ldh, npy_complex128 *w, int *iloz, int *ihiz, npy_complex128 *z, int *ldz, int *info);
void F_FUNC(zlahr2,ZLAHR2)(int *n, int *k, int *nb, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *t, int *ldt, npy_complex128 *y, int *ldy);
void F_FUNC(zlaic1,ZLAIC1)(int *job, int *j, npy_complex128 *x, double *sest, npy_complex128 *w, npy_complex128 *gamma, double *sestpr, npy_complex128 *s, npy_complex128 *c);
void F_FUNC(zlals0,ZLALS0)(int *icompq, int *nl, int *nr, int *sqre, int *nrhs, npy_complex128 *b, int *ldb, npy_complex128 *bx, int *ldbx, int *perm, int *givptr, int *givcol, int *ldgcol, double *givnum, int *ldgnum, double *poles, double *difl, double *difr, double *z, int *k, double *c, double *s, double *rwork, int *info);
void F_FUNC(zlalsa,ZLALSA)(int *icompq, int *smlsiz, int *n, int *nrhs, npy_complex128 *b, int *ldb, npy_complex128 *bx, int *ldbx, double *u, int *ldu, double *vt, int *k, double *difl, double *difr, double *z, double *poles, int *givptr, int *givcol, int *ldgcol, int *perm, double *givnum, double *c, double *s, double *rwork, int *iwork, int *info);
void F_FUNC(zlalsd,ZLALSD)(char *uplo, int *smlsiz, int *n, int *nrhs, double *d, double *e, npy_complex128 *b, int *ldb, double *rcond, int *rank, npy_complex128 *work, double *rwork, int *iwork, int *info);
void F_FUNC(zlapll,ZLAPLL)(int *n, npy_complex128 *x, int *incx, npy_complex128 *y, int *incy, double *ssmin);
void F_FUNC(zlapmr,ZLAPMR)(int *forwrd, int *m, int *n, npy_complex128 *x, int *ldx, int *k);
void F_FUNC(zlapmt,ZLAPMT)(int *forwrd, int *m, int *n, npy_complex128 *x, int *ldx, int *k);
void F_FUNC(zlaqgb,ZLAQGB)(int *m, int *n, int *kl, int *ku, npy_complex128 *ab, int *ldab, double *r, double *c, double *rowcnd, double *colcnd, double *amax, char *equed);
void F_FUNC(zlaqge,ZLAQGE)(int *m, int *n, npy_complex128 *a, int *lda, double *r, double *c, double *rowcnd, double *colcnd, double *amax, char *equed);
void F_FUNC(zlaqhb,ZLAQHB)(char *uplo, int *n, int *kd, npy_complex128 *ab, int *ldab, double *s, double *scond, double *amax, char *equed);
void F_FUNC(zlaqhe,ZLAQHE)(char *uplo, int *n, npy_complex128 *a, int *lda, double *s, double *scond, double *amax, char *equed);
void F_FUNC(zlaqhp,ZLAQHP)(char *uplo, int *n, npy_complex128 *ap, double *s, double *scond, double *amax, char *equed);
void F_FUNC(zlaqp2,ZLAQP2)(int *m, int *n, int *offset, npy_complex128 *a, int *lda, int *jpvt, npy_complex128 *tau, double *vn1, double *vn2, npy_complex128 *work);
void F_FUNC(zlaqps,ZLAQPS)(int *m, int *n, int *offset, int *nb, int *kb, npy_complex128 *a, int *lda, int *jpvt, npy_complex128 *tau, double *vn1, double *vn2, npy_complex128 *auxv, npy_complex128 *f, int *ldf);
void F_FUNC(zlaqr0,ZLAQR0)(int *wantt, int *wantz, int *n, int *ilo, int *ihi, npy_complex128 *h, int *ldh, npy_complex128 *w, int *iloz, int *ihiz, npy_complex128 *z, int *ldz, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zlaqr1,ZLAQR1)(int *n, npy_complex128 *h, int *ldh, npy_complex128 *s1, npy_complex128 *s2, npy_complex128 *v);
void F_FUNC(zlaqr2,ZLAQR2)(int *wantt, int *wantz, int *n, int *ktop, int *kbot, int *nw, npy_complex128 *h, int *ldh, int *iloz, int *ihiz, npy_complex128 *z, int *ldz, int *ns, int *nd, npy_complex128 *sh, npy_complex128 *v, int *ldv, int *nh, npy_complex128 *t, int *ldt, int *nv, npy_complex128 *wv, int *ldwv, npy_complex128 *work, int *lwork);
void F_FUNC(zlaqr3,ZLAQR3)(int *wantt, int *wantz, int *n, int *ktop, int *kbot, int *nw, npy_complex128 *h, int *ldh, int *iloz, int *ihiz, npy_complex128 *z, int *ldz, int *ns, int *nd, npy_complex128 *sh, npy_complex128 *v, int *ldv, int *nh, npy_complex128 *t, int *ldt, int *nv, npy_complex128 *wv, int *ldwv, npy_complex128 *work, int *lwork);
void F_FUNC(zlaqr4,ZLAQR4)(int *wantt, int *wantz, int *n, int *ilo, int *ihi, npy_complex128 *h, int *ldh, npy_complex128 *w, int *iloz, int *ihiz, npy_complex128 *z, int *ldz, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zlaqr5,ZLAQR5)(int *wantt, int *wantz, int *kacc22, int *n, int *ktop, int *kbot, int *nshfts, npy_complex128 *s, npy_complex128 *h, int *ldh, int *iloz, int *ihiz, npy_complex128 *z, int *ldz, npy_complex128 *v, int *ldv, npy_complex128 *u, int *ldu, int *nv, npy_complex128 *wv, int *ldwv, int *nh, npy_complex128 *wh, int *ldwh);
void F_FUNC(zlaqsb,ZLAQSB)(char *uplo, int *n, int *kd, npy_complex128 *ab, int *ldab, double *s, double *scond, double *amax, char *equed);
void F_FUNC(zlaqsp,ZLAQSP)(char *uplo, int *n, npy_complex128 *ap, double *s, double *scond, double *amax, char *equed);
void F_FUNC(zlaqsy,ZLAQSY)(char *uplo, int *n, npy_complex128 *a, int *lda, double *s, double *scond, double *amax, char *equed);
void F_FUNC(zlar1v,ZLAR1V)(int *n, int *b1, int *bn, double *lambda, double *d, double *l, double *ld, double *lld, double *pivmin, double *gaptol, npy_complex128 *z, int *wantnc, int *negcnt, double *ztz, double *mingma, int *r, int *isuppz, double *nrminv, double *resid, double *rqcorr, double *work);
void F_FUNC(zlar2v,ZLAR2V)(int *n, npy_complex128 *x, npy_complex128 *y, npy_complex128 *z, int *incx, double *c, npy_complex128 *s, int *incc);
void F_FUNC(zlarcm,ZLARCM)(int *m, int *n, double *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *c, int *ldc, double *rwork);
void F_FUNC(zlarf,ZLARF)(char *side, int *m, int *n, npy_complex128 *v, int *incv, npy_complex128 *tau, npy_complex128 *c, int *ldc, npy_complex128 *work);
void F_FUNC(zlarfb,ZLARFB)(char *side, char *trans, char *direct, char *storev, int *m, int *n, int *k, npy_complex128 *v, int *ldv, npy_complex128 *t, int *ldt, npy_complex128 *c, int *ldc, npy_complex128 *work, int *ldwork);
void F_FUNC(zlarfg,ZLARFG)(int *n, npy_complex128 *alpha, npy_complex128 *x, int *incx, npy_complex128 *tau);
void F_FUNC(zlarfgp,ZLARFGP)(int *n, npy_complex128 *alpha, npy_complex128 *x, int *incx, npy_complex128 *tau);
void F_FUNC(zlarft,ZLARFT)(char *direct, char *storev, int *n, int *k, npy_complex128 *v, int *ldv, npy_complex128 *tau, npy_complex128 *t, int *ldt);
void F_FUNC(zlarfx,ZLARFX)(char *side, int *m, int *n, npy_complex128 *v, npy_complex128 *tau, npy_complex128 *c, int *ldc, npy_complex128 *work);
void F_FUNC(zlargv,ZLARGV)(int *n, npy_complex128 *x, int *incx, npy_complex128 *y, int *incy, double *c, int *incc);
void F_FUNC(zlarnv,ZLARNV)(int *idist, int *iseed, int *n, npy_complex128 *x);
void F_FUNC(zlarrv,ZLARRV)(int *n, double *vl, double *vu, double *d, double *l, double *pivmin, int *isplit, int *m, int *dol, int *dou, double *minrgp, double *rtol1, double *rtol2, double *w, double *werr, double *wgap, int *iblock, int *indexw, double *gers, npy_complex128 *z, int *ldz, int *isuppz, double *work, int *iwork, int *info);
void F_FUNC(zlartg,ZLARTG)(npy_complex128 *f, npy_complex128 *g, double *cs, npy_complex128 *sn, npy_complex128 *r);
void F_FUNC(zlartv,ZLARTV)(int *n, npy_complex128 *x, int *incx, npy_complex128 *y, int *incy, double *c, npy_complex128 *s, int *incc);
void F_FUNC(zlarz,ZLARZ)(char *side, int *m, int *n, int *l, npy_complex128 *v, int *incv, npy_complex128 *tau, npy_complex128 *c, int *ldc, npy_complex128 *work);
void F_FUNC(zlarzb,ZLARZB)(char *side, char *trans, char *direct, char *storev, int *m, int *n, int *k, int *l, npy_complex128 *v, int *ldv, npy_complex128 *t, int *ldt, npy_complex128 *c, int *ldc, npy_complex128 *work, int *ldwork);
void F_FUNC(zlarzt,ZLARZT)(char *direct, char *storev, int *n, int *k, npy_complex128 *v, int *ldv, npy_complex128 *tau, npy_complex128 *t, int *ldt);
void F_FUNC(zlascl,ZLASCL)(char *type_bn, int *kl, int *ku, double *cfrom, double *cto, int *m, int *n, npy_complex128 *a, int *lda, int *info);
void F_FUNC(zlaset,ZLASET)(char *uplo, int *m, int *n, npy_complex128 *alpha, npy_complex128 *beta, npy_complex128 *a, int *lda);
void F_FUNC(zlasr,ZLASR)(char *side, char *pivot, char *direct, int *m, int *n, double *c, double *s, npy_complex128 *a, int *lda);
void F_FUNC(zlassq,ZLASSQ)(int *n, npy_complex128 *x, int *incx, double *scale, double *sumsq);
void F_FUNC(zlaswp,ZLASWP)(int *n, npy_complex128 *a, int *lda, int *k1, int *k2, int *ipiv, int *incx);
void F_FUNC(zlasyf,ZLASYF)(char *uplo, int *n, int *nb, int *kb, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *w, int *ldw, int *info);
void F_FUNC(zlat2c,ZLAT2C)(char *uplo, int *n, npy_complex128 *a, int *lda, npy_complex64 *sa, int *ldsa, int *info);
void F_FUNC(zlatbs,ZLATBS)(char *uplo, char *trans, char *diag, char *normin, int *n, int *kd, npy_complex128 *ab, int *ldab, npy_complex128 *x, double *scale, double *cnorm, int *info);
void F_FUNC(zlatdf,ZLATDF)(int *ijob, int *n, npy_complex128 *z, int *ldz, npy_complex128 *rhs, double *rdsum, double *rdscal, int *ipiv, int *jpiv);
void F_FUNC(zlatps,ZLATPS)(char *uplo, char *trans, char *diag, char *normin, int *n, npy_complex128 *ap, npy_complex128 *x, double *scale, double *cnorm, int *info);
void F_FUNC(zlatrd,ZLATRD)(char *uplo, int *n, int *nb, npy_complex128 *a, int *lda, double *e, npy_complex128 *tau, npy_complex128 *w, int *ldw);
void F_FUNC(zlatrs,ZLATRS)(char *uplo, char *trans, char *diag, char *normin, int *n, npy_complex128 *a, int *lda, npy_complex128 *x, double *scale, double *cnorm, int *info);
void F_FUNC(zlatrz,ZLATRZ)(int *m, int *n, int *l, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work);
void F_FUNC(zlauu2,ZLAUU2)(char *uplo, int *n, npy_complex128 *a, int *lda, int *info);
void F_FUNC(zlauum,ZLAUUM)(char *uplo, int *n, npy_complex128 *a, int *lda, int *info);
void F_FUNC(zpbcon,ZPBCON)(char *uplo, int *n, int *kd, npy_complex128 *ab, int *ldab, double *anorm, double *rcond, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zpbequ,ZPBEQU)(char *uplo, int *n, int *kd, npy_complex128 *ab, int *ldab, double *s, double *scond, double *amax, int *info);
void F_FUNC(zpbrfs,ZPBRFS)(char *uplo, int *n, int *kd, int *nrhs, npy_complex128 *ab, int *ldab, npy_complex128 *afb, int *ldafb, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zpbstf,ZPBSTF)(char *uplo, int *n, int *kd, npy_complex128 *ab, int *ldab, int *info);
void F_FUNC(zpbsv,ZPBSV)(char *uplo, int *n, int *kd, int *nrhs, npy_complex128 *ab, int *ldab, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zpbsvx,ZPBSVX)(char *fact, char *uplo, int *n, int *kd, int *nrhs, npy_complex128 *ab, int *ldab, npy_complex128 *afb, int *ldafb, char *equed, double *s, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *rcond, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zpbtf2,ZPBTF2)(char *uplo, int *n, int *kd, npy_complex128 *ab, int *ldab, int *info);
void F_FUNC(zpbtrf,ZPBTRF)(char *uplo, int *n, int *kd, npy_complex128 *ab, int *ldab, int *info);
void F_FUNC(zpbtrs,ZPBTRS)(char *uplo, int *n, int *kd, int *nrhs, npy_complex128 *ab, int *ldab, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zpftrf,ZPFTRF)(char *transr, char *uplo, int *n, npy_complex128 *a, int *info);
void F_FUNC(zpftri,ZPFTRI)(char *transr, char *uplo, int *n, npy_complex128 *a, int *info);
void F_FUNC(zpftrs,ZPFTRS)(char *transr, char *uplo, int *n, int *nrhs, npy_complex128 *a, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zpocon,ZPOCON)(char *uplo, int *n, npy_complex128 *a, int *lda, double *anorm, double *rcond, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zpoequ,ZPOEQU)(int *n, npy_complex128 *a, int *lda, double *s, double *scond, double *amax, int *info);
void F_FUNC(zpoequb,ZPOEQUB)(int *n, npy_complex128 *a, int *lda, double *s, double *scond, double *amax, int *info);
void F_FUNC(zporfs,ZPORFS)(char *uplo, int *n, int *nrhs, npy_complex128 *a, int *lda, npy_complex128 *af, int *ldaf, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zposv,ZPOSV)(char *uplo, int *n, int *nrhs, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zposvx,ZPOSVX)(char *fact, char *uplo, int *n, int *nrhs, npy_complex128 *a, int *lda, npy_complex128 *af, int *ldaf, char *equed, double *s, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *rcond, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zpotf2,ZPOTF2)(char *uplo, int *n, npy_complex128 *a, int *lda, int *info);
void F_FUNC(zpotrf,ZPOTRF)(char *uplo, int *n, npy_complex128 *a, int *lda, int *info);
void F_FUNC(zpotri,ZPOTRI)(char *uplo, int *n, npy_complex128 *a, int *lda, int *info);
void F_FUNC(zpotrs,ZPOTRS)(char *uplo, int *n, int *nrhs, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zppcon,ZPPCON)(char *uplo, int *n, npy_complex128 *ap, double *anorm, double *rcond, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zppequ,ZPPEQU)(char *uplo, int *n, npy_complex128 *ap, double *s, double *scond, double *amax, int *info);
void F_FUNC(zpprfs,ZPPRFS)(char *uplo, int *n, int *nrhs, npy_complex128 *ap, npy_complex128 *afp, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zppsv,ZPPSV)(char *uplo, int *n, int *nrhs, npy_complex128 *ap, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zppsvx,ZPPSVX)(char *fact, char *uplo, int *n, int *nrhs, npy_complex128 *ap, npy_complex128 *afp, char *equed, double *s, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *rcond, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zpptrf,ZPPTRF)(char *uplo, int *n, npy_complex128 *ap, int *info);
void F_FUNC(zpptri,ZPPTRI)(char *uplo, int *n, npy_complex128 *ap, int *info);
void F_FUNC(zpptrs,ZPPTRS)(char *uplo, int *n, int *nrhs, npy_complex128 *ap, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zpstf2,ZPSTF2)(char *uplo, int *n, npy_complex128 *a, int *lda, int *piv, int *rank, double *tol, double *work, int *info);
void F_FUNC(zpstrf,ZPSTRF)(char *uplo, int *n, npy_complex128 *a, int *lda, int *piv, int *rank, double *tol, double *work, int *info);
void F_FUNC(zptcon,ZPTCON)(int *n, double *d, npy_complex128 *e, double *anorm, double *rcond, double *rwork, int *info);
void F_FUNC(zpteqr,ZPTEQR)(char *compz, int *n, double *d, double *e, npy_complex128 *z, int *ldz, double *work, int *info);
void F_FUNC(zptrfs,ZPTRFS)(char *uplo, int *n, int *nrhs, double *d, npy_complex128 *e, double *df, npy_complex128 *ef, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zptsv,ZPTSV)(int *n, int *nrhs, double *d, npy_complex128 *e, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zptsvx,ZPTSVX)(char *fact, int *n, int *nrhs, double *d, npy_complex128 *e, double *df, npy_complex128 *ef, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *rcond, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zpttrf,ZPTTRF)(int *n, double *d, npy_complex128 *e, int *info);
void F_FUNC(zpttrs,ZPTTRS)(char *uplo, int *n, int *nrhs, double *d, npy_complex128 *e, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zptts2,ZPTTS2)(int *iuplo, int *n, int *nrhs, double *d, npy_complex128 *e, npy_complex128 *b, int *ldb);
void F_FUNC(zrot,ZROT)(int *n, npy_complex128 *cx, int *incx, npy_complex128 *cy, int *incy, double *c, npy_complex128 *s);
void F_FUNC(zspcon,ZSPCON)(char *uplo, int *n, npy_complex128 *ap, int *ipiv, double *anorm, double *rcond, npy_complex128 *work, int *info);
void F_FUNC(zspmv,ZSPMV)(char *uplo, int *n, npy_complex128 *alpha, npy_complex128 *ap, npy_complex128 *x, int *incx, npy_complex128 *beta, npy_complex128 *y, int *incy);
void F_FUNC(zspr,ZSPR)(char *uplo, int *n, npy_complex128 *alpha, npy_complex128 *x, int *incx, npy_complex128 *ap);
void F_FUNC(zsprfs,ZSPRFS)(char *uplo, int *n, int *nrhs, npy_complex128 *ap, npy_complex128 *afp, int *ipiv, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zspsv,ZSPSV)(char *uplo, int *n, int *nrhs, npy_complex128 *ap, int *ipiv, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zspsvx,ZSPSVX)(char *fact, char *uplo, int *n, int *nrhs, npy_complex128 *ap, npy_complex128 *afp, int *ipiv, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *rcond, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zsptrf,ZSPTRF)(char *uplo, int *n, npy_complex128 *ap, int *ipiv, int *info);
void F_FUNC(zsptri,ZSPTRI)(char *uplo, int *n, npy_complex128 *ap, int *ipiv, npy_complex128 *work, int *info);
void F_FUNC(zsptrs,ZSPTRS)(char *uplo, int *n, int *nrhs, npy_complex128 *ap, int *ipiv, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zstedc,ZSTEDC)(char *compz, int *n, double *d, double *e, npy_complex128 *z, int *ldz, npy_complex128 *work, int *lwork, double *rwork, int *lrwork, int *iwork, int *liwork, int *info);
void F_FUNC(zstegr,ZSTEGR)(char *jobz, char *range, int *n, double *d, double *e, double *vl, double *vu, int *il, int *iu, double *abstol, int *m, double *w, npy_complex128 *z, int *ldz, int *isuppz, double *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(zstein,ZSTEIN)(int *n, double *d, double *e, int *m, double *w, int *iblock, int *isplit, npy_complex128 *z, int *ldz, double *work, int *iwork, int *ifail, int *info);
void F_FUNC(zstemr,ZSTEMR)(char *jobz, char *range, int *n, double *d, double *e, double *vl, double *vu, int *il, int *iu, int *m, double *w, npy_complex128 *z, int *ldz, int *nzc, int *isuppz, int *tryrac, double *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(zsteqr,ZSTEQR)(char *compz, int *n, double *d, double *e, npy_complex128 *z, int *ldz, double *work, int *info);
void F_FUNC(zsycon,ZSYCON)(char *uplo, int *n, npy_complex128 *a, int *lda, int *ipiv, double *anorm, double *rcond, npy_complex128 *work, int *info);
void F_FUNC(zsyconv,ZSYCONV)(char *uplo, char *way, int *n, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *work, int *info);
void F_FUNC(zsyequb,ZSYEQUB)(char *uplo, int *n, npy_complex128 *a, int *lda, double *s, double *scond, double *amax, npy_complex128 *work, int *info);
void F_FUNC(zsymv,ZSYMV)(char *uplo, int *n, npy_complex128 *alpha, npy_complex128 *a, int *lda, npy_complex128 *x, int *incx, npy_complex128 *beta, npy_complex128 *y, int *incy);
void F_FUNC(zsyr,ZSYR)(char *uplo, int *n, npy_complex128 *alpha, npy_complex128 *x, int *incx, npy_complex128 *a, int *lda);
void F_FUNC(zsyrfs,ZSYRFS)(char *uplo, int *n, int *nrhs, npy_complex128 *a, int *lda, npy_complex128 *af, int *ldaf, int *ipiv, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(zsysv,ZSYSV)(char *uplo, int *n, int *nrhs, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *b, int *ldb, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zsysvx,ZSYSVX)(char *fact, char *uplo, int *n, int *nrhs, npy_complex128 *a, int *lda, npy_complex128 *af, int *ldaf, int *ipiv, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *rcond, double *ferr, double *berr, npy_complex128 *work, int *lwork, double *rwork, int *info);
void F_FUNC(zsyswapr,ZSYSWAPR)(char *uplo, int *n, npy_complex128 *a, int *lda, int *i1, int *i2);
void F_FUNC(zsytf2,ZSYTF2)(char *uplo, int *n, npy_complex128 *a, int *lda, int *ipiv, int *info);
void F_FUNC(zsytrf,ZSYTRF)(char *uplo, int *n, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zsytri,ZSYTRI)(char *uplo, int *n, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *work, int *info);
void F_FUNC(zsytri2,ZSYTRI2)(char *uplo, int *n, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zsytri2x,ZSYTRI2X)(char *uplo, int *n, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *work, int *nb, int *info);
void F_FUNC(zsytrs,ZSYTRS)(char *uplo, int *n, int *nrhs, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(zsytrs2,ZSYTRS2)(char *uplo, int *n, int *nrhs, npy_complex128 *a, int *lda, int *ipiv, npy_complex128 *b, int *ldb, npy_complex128 *work, int *info);
void F_FUNC(ztbcon,ZTBCON)(char *norm, char *uplo, char *diag, int *n, int *kd, npy_complex128 *ab, int *ldab, double *rcond, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(ztbrfs,ZTBRFS)(char *uplo, char *trans, char *diag, int *n, int *kd, int *nrhs, npy_complex128 *ab, int *ldab, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(ztbtrs,ZTBTRS)(char *uplo, char *trans, char *diag, int *n, int *kd, int *nrhs, npy_complex128 *ab, int *ldab, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(ztfsm,ZTFSM)(char *transr, char *side, char *uplo, char *trans, char *diag, int *m, int *n, npy_complex128 *alpha, npy_complex128 *a, npy_complex128 *b, int *ldb);
void F_FUNC(ztftri,ZTFTRI)(char *transr, char *uplo, char *diag, int *n, npy_complex128 *a, int *info);
void F_FUNC(ztfttp,ZTFTTP)(char *transr, char *uplo, int *n, npy_complex128 *arf, npy_complex128 *ap, int *info);
void F_FUNC(ztfttr,ZTFTTR)(char *transr, char *uplo, int *n, npy_complex128 *arf, npy_complex128 *a, int *lda, int *info);
void F_FUNC(ztgevc,ZTGEVC)(char *side, char *howmny, int *select, int *n, npy_complex128 *s, int *lds, npy_complex128 *p, int *ldp, npy_complex128 *vl, int *ldvl, npy_complex128 *vr, int *ldvr, int *mm, int *m, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(ztgex2,ZTGEX2)(int *wantq, int *wantz, int *n, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *q, int *ldq, npy_complex128 *z, int *ldz, int *j1, int *info);
void F_FUNC(ztgexc,ZTGEXC)(int *wantq, int *wantz, int *n, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *q, int *ldq, npy_complex128 *z, int *ldz, int *ifst, int *ilst, int *info);
void F_FUNC(ztgsen,ZTGSEN)(int *ijob, int *wantq, int *wantz, int *select, int *n, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *alpha, npy_complex128 *beta, npy_complex128 *q, int *ldq, npy_complex128 *z, int *ldz, int *m, double *pl, double *pr, double *dif, npy_complex128 *work, int *lwork, int *iwork, int *liwork, int *info);
void F_FUNC(ztgsja,ZTGSJA)(char *jobu, char *jobv, char *jobq, int *m, int *p, int *n, int *k, int *l, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, double *tola, double *tolb, double *alpha, double *beta, npy_complex128 *u, int *ldu, npy_complex128 *v, int *ldv, npy_complex128 *q, int *ldq, npy_complex128 *work, int *ncycle, int *info);
void F_FUNC(ztgsna,ZTGSNA)(char *job, char *howmny, int *select, int *n, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *vl, int *ldvl, npy_complex128 *vr, int *ldvr, double *s, double *dif, int *mm, int *m, npy_complex128 *work, int *lwork, int *iwork, int *info);
void F_FUNC(ztgsy2,ZTGSY2)(char *trans, int *ijob, int *m, int *n, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *c, int *ldc, npy_complex128 *d, int *ldd, npy_complex128 *e, int *lde, npy_complex128 *f, int *ldf, double *scale, double *rdsum, double *rdscal, int *info);
void F_FUNC(ztgsyl,ZTGSYL)(char *trans, int *ijob, int *m, int *n, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *c, int *ldc, npy_complex128 *d, int *ldd, npy_complex128 *e, int *lde, npy_complex128 *f, int *ldf, double *scale, double *dif, npy_complex128 *work, int *lwork, int *iwork, int *info);
void F_FUNC(ztpcon,ZTPCON)(char *norm, char *uplo, char *diag, int *n, npy_complex128 *ap, double *rcond, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(ztpmqrt,ZTPMQRT)(char *side, char *trans, int *m, int *n, int *k, int *l, int *nb, npy_complex128 *v, int *ldv, npy_complex128 *t, int *ldt, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *work, int *info);
void F_FUNC(ztpqrt,ZTPQRT)(int *m, int *n, int *l, int *nb, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *t, int *ldt, npy_complex128 *work, int *info);
void F_FUNC(ztpqrt2,ZTPQRT2)(int *m, int *n, int *l, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *t, int *ldt, int *info);
void F_FUNC(ztprfb,ZTPRFB)(char *side, char *trans, char *direct, char *storev, int *m, int *n, int *k, int *l, npy_complex128 *v, int *ldv, npy_complex128 *t, int *ldt, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *work, int *ldwork);
void F_FUNC(ztprfs,ZTPRFS)(char *uplo, char *trans, char *diag, int *n, int *nrhs, npy_complex128 *ap, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(ztptri,ZTPTRI)(char *uplo, char *diag, int *n, npy_complex128 *ap, int *info);
void F_FUNC(ztptrs,ZTPTRS)(char *uplo, char *trans, char *diag, int *n, int *nrhs, npy_complex128 *ap, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(ztpttf,ZTPTTF)(char *transr, char *uplo, int *n, npy_complex128 *ap, npy_complex128 *arf, int *info);
void F_FUNC(ztpttr,ZTPTTR)(char *uplo, int *n, npy_complex128 *ap, npy_complex128 *a, int *lda, int *info);
void F_FUNC(ztrcon,ZTRCON)(char *norm, char *uplo, char *diag, int *n, npy_complex128 *a, int *lda, double *rcond, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(ztrevc,ZTREVC)(char *side, char *howmny, int *select, int *n, npy_complex128 *t, int *ldt, npy_complex128 *vl, int *ldvl, npy_complex128 *vr, int *ldvr, int *mm, int *m, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(ztrexc,ZTREXC)(char *compq, int *n, npy_complex128 *t, int *ldt, npy_complex128 *q, int *ldq, int *ifst, int *ilst, int *info);
void F_FUNC(ztrrfs,ZTRRFS)(char *uplo, char *trans, char *diag, int *n, int *nrhs, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *x, int *ldx, double *ferr, double *berr, npy_complex128 *work, double *rwork, int *info);
void F_FUNC(ztrsen,ZTRSEN)(char *job, char *compq, int *select, int *n, npy_complex128 *t, int *ldt, npy_complex128 *q, int *ldq, npy_complex128 *w, int *m, double *s, double *sep, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(ztrsna,ZTRSNA)(char *job, char *howmny, int *select, int *n, npy_complex128 *t, int *ldt, npy_complex128 *vl, int *ldvl, npy_complex128 *vr, int *ldvr, double *s, double *sep, int *mm, int *m, npy_complex128 *work, int *ldwork, double *rwork, int *info);
void F_FUNC(ztrsyl,ZTRSYL)(char *trana, char *tranb, int *isgn, int *m, int *n, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *c, int *ldc, double *scale, int *info);
void F_FUNC(ztrti2,ZTRTI2)(char *uplo, char *diag, int *n, npy_complex128 *a, int *lda, int *info);
void F_FUNC(ztrtri,ZTRTRI)(char *uplo, char *diag, int *n, npy_complex128 *a, int *lda, int *info);
void F_FUNC(ztrtrs,ZTRTRS)(char *uplo, char *trans, char *diag, int *n, int *nrhs, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, int *info);
void F_FUNC(ztrttf,ZTRTTF)(char *transr, char *uplo, int *n, npy_complex128 *a, int *lda, npy_complex128 *arf, int *info);
void F_FUNC(ztrttp,ZTRTTP)(char *uplo, int *n, npy_complex128 *a, int *lda, npy_complex128 *ap, int *info);
void F_FUNC(ztzrzf,ZTZRZF)(int *m, int *n, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zunbdb,ZUNBDB)(char *trans, char *signs, int *m, int *p, int *q, npy_complex128 *x11, int *ldx11, npy_complex128 *x12, int *ldx12, npy_complex128 *x21, int *ldx21, npy_complex128 *x22, int *ldx22, double *theta, double *phi, npy_complex128 *taup1, npy_complex128 *taup2, npy_complex128 *tauq1, npy_complex128 *tauq2, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zuncsd,ZUNCSD)(char *jobu1, char *jobu2, char *jobv1t, char *jobv2t, char *trans, char *signs, int *m, int *p, int *q, npy_complex128 *x11, int *ldx11, npy_complex128 *x12, int *ldx12, npy_complex128 *x21, int *ldx21, npy_complex128 *x22, int *ldx22, double *theta, npy_complex128 *u1, int *ldu1, npy_complex128 *u2, int *ldu2, npy_complex128 *v1t, int *ldv1t, npy_complex128 *v2t, int *ldv2t, npy_complex128 *work, int *lwork, double *rwork, int *lrwork, int *iwork, int *info);
void F_FUNC(zung2l,ZUNG2L)(int *m, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *info);
void F_FUNC(zung2r,ZUNG2R)(int *m, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *info);
void F_FUNC(zungbr,ZUNGBR)(char *vect, int *m, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zunghr,ZUNGHR)(int *n, int *ilo, int *ihi, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zungl2,ZUNGL2)(int *m, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *info);
void F_FUNC(zunglq,ZUNGLQ)(int *m, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zungql,ZUNGQL)(int *m, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zungqr,ZUNGQR)(int *m, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zungr2,ZUNGR2)(int *m, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *info);
void F_FUNC(zungrq,ZUNGRQ)(int *m, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zungtr,ZUNGTR)(char *uplo, int *n, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zunm2l,ZUNM2L)(char *side, char *trans, int *m, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *c, int *ldc, npy_complex128 *work, int *info);
void F_FUNC(zunm2r,ZUNM2R)(char *side, char *trans, int *m, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *c, int *ldc, npy_complex128 *work, int *info);
void F_FUNC(zunmbr,ZUNMBR)(char *vect, char *side, char *trans, int *m, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *c, int *ldc, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zunmhr,ZUNMHR)(char *side, char *trans, int *m, int *n, int *ilo, int *ihi, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *c, int *ldc, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zunml2,ZUNML2)(char *side, char *trans, int *m, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *c, int *ldc, npy_complex128 *work, int *info);
void F_FUNC(zunmlq,ZUNMLQ)(char *side, char *trans, int *m, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *c, int *ldc, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zunmql,ZUNMQL)(char *side, char *trans, int *m, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *c, int *ldc, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zunmqr,ZUNMQR)(char *side, char *trans, int *m, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *c, int *ldc, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zunmr2,ZUNMR2)(char *side, char *trans, int *m, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *c, int *ldc, npy_complex128 *work, int *info);
void F_FUNC(zunmr3,ZUNMR3)(char *side, char *trans, int *m, int *n, int *k, int *l, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *c, int *ldc, npy_complex128 *work, int *info);
void F_FUNC(zunmrq,ZUNMRQ)(char *side, char *trans, int *m, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *c, int *ldc, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zunmrz,ZUNMRZ)(char *side, char *trans, int *m, int *n, int *k, int *l, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *c, int *ldc, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zunmtr,ZUNMTR)(char *side, char *uplo, char *trans, int *m, int *n, npy_complex128 *a, int *lda, npy_complex128 *tau, npy_complex128 *c, int *ldc, npy_complex128 *work, int *lwork, int *info);
void F_FUNC(zupgtr,ZUPGTR)(char *uplo, int *n, npy_complex128 *ap, npy_complex128 *tau, npy_complex128 *q, int *ldq, npy_complex128 *work, int *info);
void F_FUNC(zupmtr,ZUPMTR)(char *side, char *uplo, char *trans, int *m, int *n, npy_complex128 *ap, npy_complex128 *tau, npy_complex128 *c, int *ldc, npy_complex128 *work, int *info);

#ifdef __cplusplus
}
#endif
#endif
