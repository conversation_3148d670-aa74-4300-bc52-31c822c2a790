#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_compute_linear_combination_ops.h>

namespace at {


// aten::_compute_linear_combination(Tensor input, Tensor coefficients) -> Tensor
inline at::Tensor _compute_linear_combination(const at::Tensor & input, const at::Tensor & coefficients) {
    return at::_ops::_compute_linear_combination::call(input, coefficients);
}

// aten::_compute_linear_combination.out(Tensor input, Tensor coefficients, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & _compute_linear_combination_out(at::Tensor & out, const at::Tensor & input, const at::Tensor & coefficients) {
    return at::_ops::_compute_linear_combination_out::call(input, coefficients, out);
}
// aten::_compute_linear_combination.out(Tensor input, Tensor coefficients, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & _compute_linear_combination_outf(const at::Tensor & input, const at::Tensor & coefficients, at::Tensor & out) {
    return at::_ops::_compute_linear_combination_out::call(input, coefficients, out);
}

}
