#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _copy_from {
  using schema = at::Tensor (const at::Tensor &, const at::Tensor &, bool);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_copy_from";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_copy_from(Tensor self, Tensor dst, bool non_blocking=False) -> Tensor";
  static at::Tensor call(const at::Tensor & self, const at::Tensor & dst, bool non_blocking);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & dst, bool non_blocking);
};

struct TORCH_API _copy_from_out {
  using schema = at::Tensor & (const at::Tensor &, const at::Tensor &, bool, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_copy_from";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "_copy_from.out(Tensor self, Tensor dst, bool non_blocking=False, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, const at::Tensor & dst, bool non_blocking, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & dst, bool non_blocking, at::Tensor & out);
};

}} // namespace at::_ops
