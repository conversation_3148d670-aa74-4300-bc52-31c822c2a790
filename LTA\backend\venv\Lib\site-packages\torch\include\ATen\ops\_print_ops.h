#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _print {
  using schema = void (c10::string_view);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_print";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_print(str s) -> ()";
  static void call(c10::string_view s);
  static void redispatch(c10::DispatchKeySet dispatchKeySet, c10::string_view s);
};

}} // namespace at::_ops
