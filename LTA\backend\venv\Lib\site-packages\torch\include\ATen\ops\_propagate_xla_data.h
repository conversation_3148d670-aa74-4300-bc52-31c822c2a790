#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_propagate_xla_data_ops.h>

namespace at {


// aten::_propagate_xla_data(Tensor input, Tensor output) -> ()
inline void _propagate_xla_data(const at::Tensor & input, const at::Tensor & output) {
    return at::_ops::_propagate_xla_data::call(input, output);
}

}
