#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/squeeze_ops.h>

namespace at {


// aten::squeeze(Tensor(a) self) -> Tensor(a)
inline at::Tensor squeeze(const at::Tensor & self) {
    return at::_ops::squeeze::call(self);
}

// aten::squeeze.dim(Tensor(a) self, int dim) -> Tensor(a)
inline at::Tensor squeeze(const at::Tensor & self, int64_t dim) {
    return at::_ops::squeeze_dim::call(self, dim);
}

// aten::squeeze.dimname(Tensor(a) self, Dimname dim) -> Tensor(a)
inline at::Tensor squeeze(const at::Tensor & self, at::Dimname dim) {
    return at::_ops::squeeze_dimname::call(self, dim);
}

// aten::squeeze.dims(Tensor(a) self, int[] dim) -> Tensor(a)
inline at::Tensor squeeze(const at::Tensor & self, at::IntArrayRef dim) {
    return at::_ops::squeeze_dims::call(self, dim);
}

}
