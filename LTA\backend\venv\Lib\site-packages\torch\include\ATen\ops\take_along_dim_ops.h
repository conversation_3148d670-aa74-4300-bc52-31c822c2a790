#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API take_along_dim_out {
  using schema = at::Tensor & (const at::Tensor &, const at::Tensor &, ::std::optional<int64_t>, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::take_along_dim";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "take_along_dim.out(Tensor self, Tensor indices, int? dim=None, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, const at::Tensor & indices, ::std::optional<int64_t> dim, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & indices, ::std::optional<int64_t> dim, at::Tensor & out);
};

struct TORCH_API take_along_dim {
  using schema = at::Tensor (const at::Tensor &, const at::Tensor &, ::std::optional<int64_t>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::take_along_dim";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "take_along_dim(Tensor self, Tensor indices, int? dim=None) -> Tensor";
  static at::Tensor call(const at::Tensor & self, const at::Tensor & indices, ::std::optional<int64_t> dim);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & indices, ::std::optional<int64_t> dim);
};

}} // namespace at::_ops
