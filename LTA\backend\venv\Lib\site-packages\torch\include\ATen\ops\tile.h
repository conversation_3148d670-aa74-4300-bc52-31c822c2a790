#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/tile_ops.h>

namespace at {


// aten::tile(Tensor self, SymInt[] dims) -> Tensor
inline at::Tensor tile(const at::Tensor & self, at::IntArrayRef dims) {
    return at::_ops::tile::call(self, c10::fromIntArrayRefSlow(dims));
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  at::Tensor tile(const at::Tensor & self, at::IntArrayRef dims) {
    return at::_ops::tile::call(self, c10::fromIntArrayRefSlow(dims));
  }
}

// aten::tile(Tensor self, SymInt[] dims) -> Tensor
inline at::Tensor tile_symint(const at::Tensor & self, c10::SymIntArrayRef dims) {
    return at::_ops::tile::call(self, dims);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  at::Tensor tile(const at::Tensor & self, c10::SymIntArrayRef dims) {
    return at::_ops::tile::call(self, dims);
  }
}

}
